export function  nameFormat (item) {
   console.log(item)
   if (!item) return '';
   let str = item;
   if(str.length == 2){
     str = str.toString().replace(/^([^\x00-\xff])([^\x00-\xff]{0,})([^\x00-\xff])/g , '$1*')
   }else if(str.length == 3){
     str = str.toString().replace(/^([^\x00-\xff])([^\x00-\xff]{0,})([^\x00-\xff])/g , '$1**')
  }else if(str.length == 4){
    str = str.toString().replace(/^([^\x00-\xff])([^\x00-\xff]{0,2})([^\x00-\xff])/g , '$1**$3')
   }else if(str.length > 4){
    str = str.toString().replace(/^([^\x00-\xff])([^\x00-\xff]{0,3})([^\x00-\xff])/g , '$1***$3')
   }else{}
 
  return str;
}


export function formatIDcard (value) {
   if (!value) return '';
   let str = value;
   str = str.toString().replace(/^(.{6})(?:\w+)(.{4})$/ , '$1********$2')
   return str;
}
export function formatPhone (value) {
   if (!value) return '';
   let str = value;
   str = str.toString().replace(/^(\d{3})(\d{4})(\d{4})/g , '$1****$3')
   return str;
 }