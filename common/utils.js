//判断是否有特殊字符
const charTest = str => {
	var pattern = new RegExp("[`~!#$^&*()=|{}':;',\\[\\]<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]", 'g');
	if(pattern.test(str)) return true
	return false
}
//获取价格精度
const getPrecisionLength = (number) => {
	let length
	if(typeof(number) == 'number') number = String(number)
	length = number.indexOf('.') > -1 ? number.split('.')[1].length : 0
	return length
}
const getColor = (price) => {
	//判断是否为字符串
	if(typeof price == "string"){
		if(price.indexOf("%") > -1){
			price = price.slice(0,price.length - 1)
		}
		price = +price
	}
	if(price > 0){
		return "12"
	}else if(price == 0){
		return "13"
	}else{
		return "14"
	}
}
const jump = (url,type = 'navigateTo') => {
	switch(type){
		case 'navigateTo':
		console.log('navigateTo')
			uni.navigateTo({
				url
			})
			return;
		case 'redirectTo':
		console.log('redirectTo')
			uni.redirectTo({
				url
			})
			return;
		case 'reLaunch':
			uni.reLaunch({
				url
			})
			return;
		case 'switchTab':
			uni.switchTab({
				url
			})
			return;
		case 'navigateBack':	
			uni.navigateBack({
				delta:url
			})
	}
}
const setTabbar = (that) => {
	const nav = that.$t("nav")
	for(let i = 0; i < 4; i++){
		let texts=""
		
		if(i==0) texts=nav.market
		if(i==1) texts=nav.order
		// if(i==3) texts=nav.news
		if(i==2) texts=nav.trade
		if(i==3) texts=nav.mine
		
		uni.setTabBarItem({
			index:i,
			text:texts,
			fail(res) {
			}
		})
	}
}
export default{
	charTest,
	getPrecisionLength,
	getColor,
	jump,
	setTabbar
}