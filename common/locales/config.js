import Vue from "vue"
// i18n部分的配置
// 引入语言包，注意路径
import zh from './zh.js';
import en from './en.js';
import ara from './ara.js';
import fre from './fre.js';
import ger from './ger.js';
import Ita from './Ita.js';
import jp from './jp.js';
import kor from './kor.js';

import por from './por.js';
import rus from './rus.js';
import spa from './spa.js';
import th from './th.js';
import tur from './tur.js';
import vie from './vie.js';




// VueI18n
import VueI18n from '@/common/vue-i18n.min.js';

// VueI18n
Vue.use(VueI18n);
// 默认语言
if(!uni.getStorageSync('lang')){
	localStorage.setItem('lang', 'jp');
}
const i18n = new VueI18n({
	locale: uni.getStorageSync('lang') || 'jp',
	// 引入语言文件
	messages: {
		'zh':zh,
		'en':en,
		'ara':ara,
		'fre':fre,
		'ger':ger,
		'Ita':Ita,
		'jp':jp,
		'kor':kor,
		'por':por,
		'rus':rus,
		'spa':spa,
		'th':th,
		'tur':tur,
		'vie':vie,
	}
});

export default i18n
