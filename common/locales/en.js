export default {
	languages: {
		chinese: '简体中文',
		traditional_chinese: '繁体中文',
		english: '英文',
		japanese: '日本語',
		korean: '한국어',
		thailand: 'ภาษาไทย',
		french: 'Français',
		spanish: 'Español',
		german: 'Deutsch',
		italian: 'Italiano',
		russian: 'Русский',
		portuguese: 'português',
		arabic: ' بالعربية',
		turkish: 'Türkçe',
		vietnamese: 'Tiếng Việt'
	},
	nav: {
		market: 'Market',
		order: 'Orders',
		news: 'News',
		trade: 'Trade',
		mine: 'Mine'
	},
	login: {
		welcome_sign_in: 'Welcome Login',
		emallogo: 'Email Login',
		pwd: 'password',
		enter_email: 'Please enter your email address',
		enter_password: 'Please enter your password',
		enter_vercode: 'Please enter the verification code',
		have_account: 'New User？',
		register: 'Join Now',
		forgot_password: 'Forgot password？',
		sign_in: 'Login',
		creat_account: 'No need to open an account, create a demo account to trade now!'
	},
	mining: {
		money_management: 'Wealth Management',
		subscription_list: 'Subscription List',
		subscription: 'Application',
		expected_nissan: 'Expected Daily Production',
		mining_machine_rental: 'Mining Machine Rental',
		mining_life: 'Mining Duration',
		maximum_subscription: 'Maximum Subscription',
		max_number: 'Please Enter the Locked Amount',
		subscribe: 'Subscribe',
		projected_total_revenue: 'Projected Total Revenue',
		number_application: 'Application Quantity',
		redemption_default: 'Default Redemption',
		view_earnings_list: 'View Earnings List',
		Application_time: 'Application Time',
		underway: 'In Progress',
		applying_refund: 'Applying for Refund',
		payment: 'Payment',
		liquidated_damages: 'Early Redemption with Penalty',
		refund_successful: 'Refund Successful',
		product_presentation: 'Product Introduction',
		no_more: 'No More'
	},
	register: {
		emalzc: '郵箱注册',
		phonezc: '手机号注册',
		phone: '手机号',
		emallogo: 'Email Login',
		have_account: 'New User？',
		forgot_password: 'Forgot password？',
		sign_in: 'Login',
		creat_account: 'No need to open an account, create a demo account to trade now!',
		pwd: 'password',
		welcome_register: 'Welcome to register',
		enter_email: 'Please enter your email address',
		enter_password: 'Please enter your password',
		enter_password_again: 'Please enter your password',
		enter_invite_code: 'Please enter the invitation code',
		enter_vercode: 'Please enter the verification code',
		get_ver_code: 'Send',
		read_agress: ' I have read and agree to',
		user_service: 'User Service Agreement',
		and: 'and',
		privacy: 'Privacy Policy',
		register: 'Register'
	},
	reset: {
		reset_password: 'Reset Password',
		enter_email: 'Please enter your email address',
		enter_email_or_phone: 'Please enter your phone/email',
		enter_email_or_phone_placeholder: 'Please enter phone number or email',
		enter_password: 'Password',
		enter_new_password: 'Please enter a new password',
		enter_password_again: 'Enter password again',
		enter_new_password_again: 'Please enter your new password again',
		enter_vercode: 'Please enter the verification code',
		get_ver_code: 'Get code',
		have_account: 'Already have an account？',
		sign_in: 'Sign in',
		// 新增验证提示
		please_enter_email_or_phone: 'Please enter email or phone number',
		please_enter_correct_format: 'Please enter correct email or phone format',
	},
	search: {
		search: 'Search',
		enter_product_code: 'Search Products',
		search_result: 'Search Results',
	},
	market: {
		traded_item: 'varieties',
		my_fav: 'Collection',
		closed: 'Closed',

	},
	news: {
		news_detail: 'Detail'
	},
	order: {
		rw1: 'Market',
		rw2: 'Orders',
		rw3: 'News',
		rw3_1: 'Wealth Management',
		rw4: 'Mine',
		contract: 'Contract',
		no: 'No data yet',
		second_contract: 'Seconds',
		position: 'Position holding',
		listing: 'Pending Orders',
		history: 'History',
		profit_Loss: 'Profit and Loss',
		current_deposit: 'Current Margin',
		risk_rate: 'Risk Rate',
		sell: 'Sell',
		quantity: 'Lots',
		buy: 'Buy',
		prompt: 'Alerts',
		confirm_cancellation: 'Confirm Withdrawal？',
		cancel: 'Cancel',
		ok: 'Confirm',
		order_detail: 'Order Details',
		order_num: 'Order ID#',
		stop_profit_loss: 'Set Profit/Loss',
		deposit: 'Margin',
		hanling_fee: 'Handling Fee',
		cancellation: 'Withdrawal',
		position_closed: 'Close Position or not？',
		modify_TP_SL: 'Set TP/SL',
		stop_profit: 'Set Profit',
		stop_loss: 'Set Loss',
		modify: 'Modify',
		closing_positions: 'Close Position',
		opening_time: 'Open Time',
		closing_time: 'Closing Time',
		transaction: 'Transaction',
		balance: 'Balance',
		fenxianlv: 'MMR',

		direction: 'direction',
		measurement: 'measurement',
		product_list: 'product list',
		Secondary_graph_metrics: 'secondary graph metrics',
		indicator: 'indicator',
		place_order: 'place order',
		transaction_currency: 'transaction currency',
		order_form: 'order form',
		place_the_unit_price: 'place the unit price',
		current_price: 'current price',
		operation: 'operation',
	},
	mine: {
		creditscore: 'Credit score',
		financial_records: 'Financial Records',
		balance: 'Available Assets',
		net_worth: 'Net Value',
		used_margin: 'Used Margin',
		free_margin: 'Available Margin',
		margin_level: 'Margin Ratio',
		cash_deposit: 'Deposit',
		cash_out: 'Withdrawal',
		billing_details: 'Billing Details',
		wallet: 'Wallet',
		real_name_authentication: 'Real Name Verification',
		mock_account: 'Simulated account ',
		invite_friends: 'Invite Friends',
		change_password: 'Change Password',
		complaint_email: 'Complaint Email',
		contact_customer_service: 'Online Service',
		announcement: 'Announcement',
		language: 'Language',
		style: 'Style',
		other: 'Other',
		darkness: 'Dark',
		light: 'Light'
	},
	wallet: {
		bind_bank_card: 'Binding Bank Card',
		currency: 'Currency',
		bank_name: 'Bank Name',
		enter_bank_name: 'Please enter bank name',
		bank_address: 'Bank Address',
		enter_bank_address: 'Please enter bank address',
		swift: 'SWIFT',
		payee: 'Payee',
		enter_the_payee: 'Please enter the payee',
		payee_account: 'Payee Account',
		enter_account: `Please enter the payee's account`,
		add: 'Add',
		bank_card: 'Bank Card',
		pay_currency: 'Currency',
		delete: 'Delete',
		bind_digital_currency_address: 'Bind digital currency address',
		wallet_address: 'Wallet Address',
		enter_the_wallet_address: 'Please enter the wallet address',
		withdrawal_address: 'Withdrawal Address',
		balance: 'Balance',
		digital_currency_address: 'Digital Currency Address',
	},
	invite: {
		invite_friends: 'Invite Friends',
		invitation_code: 'Invitation Code',
		copy_invitation_link: 'Copy Invitation Link',

	},
	records: {
		financial_records: 'Financial Records',
		quantity: 'Lots',
		time: 'Time',
		record: 'Record',
		chain_recharge_account: 'Chain recharge to account'
	},
	service: {
		contact_customer_service: 'Contact Customer Service',
		copy: 'Copy',
		complaints_mailbox: 'Complaints mailbox'
	},
	real: {
		real_name_auth: 'Real Name Authentication',
		upload_the_front_and_back: 'Please upload front and back of passport/document',
		front: 'Front Side',
		back: 'Back Side',
		improve_information: 'Please complete your personal information',
		name: 'Name',
		enter_name: 'Please enter your name',
		passport_idnumber: 'Passport/Document Number',
		enter_passport_idnumber: 'Please enter passport/document number',
		submit: 'Submit',
		re_certification: 'Recertify',
		under_review: 'Audit',
		upload_success_waiting: 'Upload successful, awaiting review'
	},
	out: {
		withdrawal_record: 'Withdrawal Record',
		number_of_withdrawal: 'Amount',
		number_of_arrival: 'Number of arrivals',
		unit: 'Unit',
		hand_fee: 'Handling Fee',
		status: 'Status',
		reipient_account: `Recipient's Account`,
		remarks: 'Remark',
		withdrawal: 'Withdrawal',
		digital_currency: 'Digital Currency',
		bank_card: 'Bank Card',
		currency: 'Currency',
		fill_quantity: 'Charge quantity',
		enter_number: 'Please enter the number of coins',
		details: 'Detail',
		estimated_amount: 'It is expected that',
		balance: 'Balance',
		withdrawal_: 'Submit Withdrawal',
		successful: 'Audit passed',
		rejected: 'Audit Failure',
		withdrawal_adress: 'Withdrawal Address',
		coin_withdrawal_record: 'Coin withdrawal record',
		prompt: 'Prompt',
		no_withdrawal_address: 'Current currency has no withdrawal address, go to add?',
		no_recipient_account: 'Current currency has no recipient account, go to add?',
		please_enter_amount: 'Please enter withdrawal amount',
		please_select_currency: 'Please select withdrawal currency',
		please_select_address: 'Please select withdrawal address',
		please_select_account: 'Please select recipient account',
		submit_failed: 'Submission failed',
		network_error: 'Network error, please try again',
	},
	other: {
		other: 'Other',
		logout: 'Log out',
		alerts: 'Alerts',
		to_log_out: 'Are you sure you want to log out？',
		cancel: 'Cancel',
		confirm: 'Confirm',
		announcement: 'Announcemen',

	},
	deposit: {
		deposit_record: 'Deposit Record',
		deposit: 'Deposit',
		digital_currency: 'Digital Currency',
		bank_card: 'Bank Card',
		select_net: 'Select Network',
		wallet_adress: 'Wallet address',
		copy: 'Copy',
		number_of_coins: 'Number of coins',
		enter_number_coins: 'Please enter the number of coins',
		payment_voucher: 'Payment voucher',
		upload_image: 'Upload image',
		submit: 'Submit',
		recharge_amount: 'Recharge Amount',
		type: 'Type',
		unit: 'Unit',
		status: 'Status',
		time: 'Time',
		completed: 'Completed',
		invalid: 'Invalid',
		currency: 'Currency',
		recharge_account: 'Recharge Account',


	},
	trade: {
		order_confirmed: 'Order Confirmed',
		view_order: 'View Order',
		confirm: 'Confirmation',
		contracts: 'Contracts',
		second_contract: 'Seconds',
		open_number: 'Number of open positions',
		opening_time: 'Opening time',
		profit_rate: 'Profit Rate',
		estimated_commission: 'Estimated Handing Fee',
		buy_down: 'Sell',
		buy_up: 'Buy',
		trade: 'Market Price',
		pending_order: 'Pending Order',
		price: 'Price',
		multiplier: 'Multiplier',
		stop_loss: 'Set Loss',
		take_prodit: 'Set Profit',
		buy_quantity: 'Lots(Step:0.1)',
		each: 'Each sheet',
		sheet: '1Sheet',
		estimated_margin: 'Estimated Margin',
		balance: 'Balance',
		buy: 'Buy',
		sell: 'Sell',
		minutes: 'Minutes',
		hour: 'Hour',
		more: 'More'
	}

}