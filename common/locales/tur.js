export default {
       languages:{
			chinese:'简体中文',
			traditional_chinese:'繁体中文',
			english:'英文',
			japanese:'日本語',
			korean:'한국어',
			thailand:'ภาษาไทย',
			french:'Français',
			spanish:'Español',
			german:'Deutsch',
			italian:'Italiano',
			russian:'Русский',
			portuguese:'português',
			arabic:' بالعربية',
			turkish:'Türkçe',
			vietnamese:'Tiếng Việt'
		},
		nav:{
			market:'Pazar',
			order:'Sıra formu',
			news:'Ha<PERSON><PERSON>',
			trade:'Transaction',
			mine:'Kişisel bilgi'
		},
		login:{
			welcome_sign_in:'Hoş Geldiniz Giriş',
			enter_email:'Lütfen e-posta adresinizi girin',
			enter_password:'<PERSON>ütfen parolanı girin',
			enter_vercode:'Lütfen doğrulama kodunu girin',
			have_account:'Hen<PERSON><PERSON> bir hesap yok mu?',
			register:'kayıt',
			forgot_password:'<PERSON><PERSON><PERSON> unuttun mu？',
			sign_in:'İçeri gir',
			creat_account:'<PERSON>eyimi girmek için şimdi simulasyon bir hesap oluştur'
		},
		register:{
			welcome_register:'Kayıtlara hoş geldiniz',
			enter_email:'Lütfen e-posta adresinizi girin',
			enter_password:'Lütfen parolanı girin',
			enter_password_again:'Lütfen parolanı tekrar girin',
 			enter_invite_code:'Lütfen davet kodunu girin',
			enter_vercode:'Lütfen doğrulama kodunu girin',
			get_ver_code:'Denetim kodu alın',
			read_agress:'Okudum ve kabul ettim.',
			user_service:'Kullanıcı Hizmet Anlaşması',
			and:'ve',
			privacy:'Gizlilik politikası',
			register:'kayıt'
		},
		reset:{
			reset_password:'Parola yeniden ayarla',
			enter_email:'Lütfen e-posta adresinizi girin',
			enter_new_password:'Lütfen yeni bir parola girin',
			enter_new_password_again:'Lütfen yeni parolanı tekrar girin',
			enter_vercode:'Lütfen doğrulama kodunu girin',
			get_ver_code:'Denetim kodu alın',
 			have_account:'Hesap var mı?',
			sign_in:'İçeri gir',
			// 新增验证提示
			please_enter_email_or_phone:'Lütfen e-posta veya telefon numarası girin',
			please_enter_correct_format:'Lütfen doğru e-posta veya telefon formatı girin',
		},
		search:{
			search:'Arama',
			enter_product_code:'Çeşitli kodu girin',
			search_result:'Arama sonuçları',
		},
		market:{
			traded_item:'Satış çeşitliği',
			my_fav:'Kolleksiyonlarım',
			closed:'Pazarı Çıktır',

		},
		news:{
			news_detail:'Haber detayları'
		},
		order:{
			contract:'sözleşme',
			second_contract:'İkinci anlaşma',
			position:'Konum tutuyor',
			listing:'Liste',
			history:'Tarih',
			profit_Loss:'Profit ve kaybı',
			current_deposit:'Mevcut sınıf',
			risk_rate:'Risk hızı',
			sell:'Satın',
			quantity:'Miktarda',
			buy:'Alın.',
			prompt:'Prompt',
			confirm_cancellation:'İptal edilmesini doğrulamak mı?',
			cancel:'Iptal et',
			ok:'onaylayın',
			order_detail:'Sıra detayları',
			order_num:'Sıra numarası',
			stop_profit_loss:'Parayı durdur/kaybetmeyi durdur',
			deposit:'Margin',
			hanling_fee:'Ödemeleri yönetme',
			cancellation:'Bir emri öldürün.',
			position_closed:'Konuyu kapatmalıyız mı?',
			modify_TP_SL:'TP/SL değiştir',
			stop_profit:'Geri kalanı durdur',
			stop_loss:'Kaybı durdur.',
			modify:'değiştir',
			closing_positions:'Konum kapatılıyor',
			opening_time:'Aç Zamanı',
			closing_time:'Kapatma zamanı',
			transaction:'Transaksyon',
			balance:'Balance',

			direction: 'yön',
			measurement: 'ölçüm',
			product_list: 'ürün listesi',
			Secondary_graph_metrics: 'ikincil grafik metrikleri',
			indicator: 'göstergesi',
			place_order: 'sipariş vermek',
			transaction_currency: 'işlem para birimi',
			order_form: 'sipariş formu',
			place_the_unit_price: 'birim fiyatını belirlemek',
			current_price: 'mevcut fiyat',
			operation: 'işlem',

		},
		mine:{
			reditscore: 'Credit score',
			financial_records:'Mali kayıtlar',
			balance:'balance',
			net_worth:'Ağ değeri',
			used_margin:'Kullanılan sınır',
			free_margin:'Boş sınıf',
			margin_level:'Margin seviyesi',
			cash_deposit:'Para depoziti',
			cash_out:'Çıkarma',
			billing_details:'Faturalama Ayrıntıları',
			wallet:'Cüzdanı',
			real_name_authentication:'Gerçek isim doğrulaması',
			mock_account:'Benzersiz Hesap',
			invite_friends:'Arkadaşları davet et',
			change_password:'Parola değiştir',
			complaint_email:'Sıkıştırma e- postası',
			contact_customer_service:'Müşteri hizmeti ile bağlantı kur',
			announcement:'duyuru',
			language:'dil',
			style:'stil',
			other:'diğer',
			darkness:'Diablo',
			light:'Işık renk'
		},
		wallet:{
			bind_bank_card:'Bağlam banka kartı',
			currency:'para',
			bank_name:'Banka İsmi',
			enter_bank_name:'Lütfen banka adını girin',
			bank_address:'Banka adresi',
			enter_bank_address:'Lütfen banka adresini girin',
			swift:'SWIFT',
			payee:'Payee',
			enter_the_payee:'Lütfen ödeme sahibini girin.',
			payee_account:'Payee hesabı',
			enter_account:'Lütfen alıcının hesabını girin',
			add:'Ekle',
			bank_card:'Banka kartı',
			pay_currency:'Paranın parası',
			delete:'Sil',
			bind_digital_currency_address:'Dijital para adresini bağla',
			wallet_address:'Cüzdanın adresi',
			enter_the_wallet_address:'Lütfen cüzdanın adresini girin',
			withdrawal_address:'Çıkart adresi',
			balance:'balance',
			digital_currency_address:'Dijital Vali Adresi',
		},
		invite:{
			invite_friends:'Arkadaşları davet et',
			invitation_code:'Davet kodu',
			copy_invitation_link:'Davet bağlantısını kopyala',

		},
		 records:{
			financial_records:'Mali kayıtlar',
			quantity:'Miktarda',
			time:'Zaman',
			record:'kayıt',
			chain_recharge_account:'Hesapla zincir yeniden yüklenmek üzere'
		},
		service:{
			contact_customer_service:'Müşteri hizmeti ile bağlantı kur',
			copy:'Kopyalama',
			complaints_mailbox:'Sıkıştırma e- postası'
		},
		real:{
		    real_name_auth	:'Gerçek isim doğrulaması',
		    upload_the_front_and_back:'Lütfen pasaportunuz/belgelerinizin önünü ve arkasını yükleyin',
			front:'ön',
			back:'tersi',
			improve_information:'请完善个人信息',
			name:'isim',
			enter_name:'Lütfen isminizi girin',
			passport_idnumber:'Pasaportu/Kimlik Numarası',
			enter_passport_idnumber:'Lütfen pasaportunuzu/kimliğinizi girin',
			submit:'Teslim et',
			re_certification:'Yeniden sertifika',
			under_review:'Gözlemde',
			upload_success_waiting:'Yükleme başarılı, inceleme bekliyor'
		},
		out:{
			withdrawal_record:'Para ödeme kayıtları',
			number_of_withdrawal:'Kaldırma miktarı',
			number_of_arrival:'Alılan miktarda',
			unit:'unit',
			hand_fee:'Ödemeleri yönetme',
			status:'Durum',
			reipient_account:'Payee hesabı',
			remarks:'İşaretler',
			withdrawal:'Cash out',
			digital_currency:'Dijital para',
			bank_card:'Banka kartı',
			currency:'Para',
 			fill_quantity:'Charge quantity',
			enter_number:'Lütfen yeniden yüklenecek paranın sayısını girin',
			details:'detaylar',
 			estimated_amount:'Tahmin edilen ölçü alındı',
			balance:'balance',
			withdrawal_:'Parayı kaldırmak',
			successful:'başarılı',
			rejected:'Red edildi',
			withdrawal_adress:'Hesap adresi',
			coin_withdrawal_record:'Madeni para çekme kaydı',
			prompt:'Uyarı',
			no_withdrawal_address:'Mevcut para biriminin çekme adresi yok, eklemek ister misiniz?',
			no_recipient_account:'Mevcut para biriminin alıcı hesabı yok, eklemek ister misiniz?',
			please_enter_amount:'Lütfen çekme miktarını girin',
			please_select_currency:'Lütfen çekme para birimini seçin',
			please_select_address:'Lütfen çekme adresini seçin',
			please_select_account:'Lütfen alıcı hesabını seçin',
			submit_failed:'Gönderim başarısız',
			network_error:'Ağ hatası, lütfen tekrar deneyin',
		},
		other:{
			other:'diğer',
			logout:'Girişten çık',
			alerts:'Prompt',
            to_log_out:'Çıkacağına emin misin？',
			cancel:'iptal et',
			confirm:'onaylayın',
			announcement:'duyuru',

		},
		deposit:{
		deposit_record:'Para depozit kayıtları',
			deposit:'Para depoziti',
			digital_currency:'Dijital para',
			bank_card:'banka kartı',
			select_net:'Ağ Seç',
			wallet_adress:'Cüzdanın adresi',
			copy:'Kopyalama',
			number_of_coins:'Charge quantity',
			enter_number_coins:'Lütfen yeniden yüklenecek paranın sayısını girin',
			payment_voucher:'Ödeme verici',
			upload_image:'Resimleri yükle',
			submit:'Teslim et',
			recharge_amount:'Tekrar yükle miktarı',
			type:'Tür',
			unit:'unit',
			status:'Durum',
			time:'Zaman',
			completed:'Tamamlandı',
			invalid:'Keçersiz',
			currency:'Paranın parası',
			recharge_account:'Yükleme hesabı',


		},
		trade:{
			order_confirmed:'Düzeni doğruladı',
			view_order:'Emirleri görüntüle',
			confirm:'onaylayın',
			contracts:'sözleşme',
			second_contract:'İkinci anlaşma',
			open_number:'Çokluğu açılıyor',
			opening_time:'Aç Zamanı',
			profit_rate:'Profit sınırı',
			estimated_commission:'Tahmin edilen yönetim ücreti',
			sheet:'1sheet',
			buy_down:'Aşağı al.',
			buy_up:'Alıyorum.',
			trade:'Sıra aktarımı yerleştir',
			pending_order:'beklenen düzeni',
			price:'fiyat',
			multiplier:'çoklu',
			stop_loss:'Kaybı durdur.',
			take_prodit:'Geri kalanı durdur',
			buy_quantity:'Alış miktarı',
			each:'Her çarşaf',
 			estimated_margin:'Tahmin edilen Margin',
			balance:'balance',
			buy:'Alın.',
			sell:'Satın',
			minutes:'dakika',
			hour:'saat',
			more:'Daha fazla'
		}

}
