export default {
       languages:{
			chinese:'简体中文',
			traditional_chinese:'繁体中文',
			english:'英文',
			japanese:'日本語',
			korean:'한국어',
			thailand:'ภาษาไทย',
			french:'Français',
			spanish:'<PERSON><PERSON><PERSON><PERSON><PERSON>',
			german:'Deutsch',
			italian:'Italiano',
			russian:'Русский',
			portuguese:'português',
			arabic:' بالعربية',
			turkish:'Türkçe',
			vietnamese:'Tiếng Việt'
		},
		nav:{
			market:'конъюнктура ',
			order:'Заказы',
			news:'Информация',
			trade:'Сделки ',
			mine:'Мой.'
		},
		login:{
			welcome_sign_in:'Добро пожаловать Вход',
			enter_email:'Введите свой почтовый ящик ',
			enter_password:' Введите пароль. ',
			enter_vercode:'Введите код проверки. ',
			have_account:'Нет аккаунта? ',
			register:'Регистрация',
			forgot_password:'Забыли пароль?',
			sign_in:'Регистрация',
			creat_account:'Создайте аналоговый аккаунт прямо сейчас, чтобы войти в опыт'
		},
		register:{
			welcome_register:'Добро пожаловать на регистрацию ',
			enter_email:'Введите свой почтовый ящик ',
			enter_password:'Введите пароль.',
			enter_password_again:'Введите пароль еще раз. ',
 			enter_invite_code:'Введите код приглашения. ',
			enter_vercode:'Введите код проверки. ',
			get_ver_code:'Получение кода проверки',
			read_agress:'Я прочитал и согласился. ',
			user_service:'Соглашение об обслуживании пользователей ',
			and:'А',
			privacy:'Политика конфиденциальности',
			register:'Регистрация'
		},
		reset:{
			reset_password:'Сбросить пароль',
			enter_email:'Введите свой почтовый ящик',
			enter_new_password:' Введите новый пароль.',
			enter_new_password_again:'Пожалуйста, введите новый пароль.',
			enter_vercode:'Введите код проверки. ',
			get_ver_code:'Получение кода проверки ',
 			have_account:' Уже есть аккаунт？',
			sign_in:' Регистрация ',
			// 新增验证提示
			please_enter_email_or_phone:'Пожалуйста, введите email или номер телефона',
			please_enter_correct_format:'Пожалуйста, введите правильный формат email или телефона',
		},
		search:{
			search:' Поиск ',
			enter_product_code:'Введите код сорта',
			search_result:'Результаты поиска',
		},
		market:{
			traded_item:'Виды сделок',
			my_fav:'Моя коллекция.',
			closed:'Хью ',

		},
		news:{
			news_detail:'Подробнее '
		},
		order:{
			contract:'Контракт',
			second_contract:'Секундный контракт',
			position:'Хранить склад',
			listing:'Заказы',
			history:'История',
			profit_Loss:'Прибыль и убыток',
			current_deposit:'Текущий залог',
			risk_rate:'Коэффициент риска',
			sell:'Продать',
			quantity:'Количество',
			buy:'Купить',
			prompt:'Подсказка',
			confirm_cancellation:'Подтвердить отзыв？',
			cancel:'Отменить',
			ok:'Определение',
			order_detail:'Подробности заказа',
			order_num:'Номер заказа ',
			stop_profit_loss:'Превышение / прекращение потерь',
			deposit:'Гарантия',
			hanling_fee:'Плата за услуги',
			cancellation:'Снятие ордера ',
			position_closed:'Быть ли плоским？',
			modify_TP_SL:'Изменить TP/SL',
			stop_profit:'Ликвидация',
			stop_loss:'Остановить потерю ',
			modify:'Изменения',
			closing_positions:'Хиракура',
			opening_time:' Время открытия склада ',
			closing_time:'Время складирования',
			transaction:'Договорились',
			balance:'Остаток',


			direction: 'направление',
			measurement: 'измерение',
			product_list: 'список продуктов',
			Secondary_graph_metrics: 'метрики вторичного графика',
			indicator: 'индикатор',
			place_order: 'размещение заказа',
			transaction_currency: 'валюта транзакции',
			order_form: 'форма заказа',
			place_the_unit_price: 'цена единицы заказа',
			current_price: 'текущая цена',
			operation: 'операция',
		},
		mine:{
			reditscore: 'Credit score',
			financial_records:'Финансовая отчетность',
			balance:'Остаток',
			net_worth:'Чистая стоимость ',
			used_margin:'Использованный залог ',
			free_margin:'Имеющаяся гарантия',
			margin_level:'Доля гарантийного покрытия',
			cash_deposit:'Поступления ',
			cash_out:'Показать',
			billing_details:'Подробности счета',
			wallet:'Бумажник',
			real_name_authentication:'Сертификация подлинного имени',
			mock_account:'Симулированный Аккаунт',
			invite_friends:'Пригласить друзей',
			change_password:'Изменить пароль',
			complaint_email:' Почтовый ящик для жалоб',
			contact_customer_service:'Связаться с клиентом',
			announcement:'Объявление',
			language:'Язык',
			style:'Стиль',
			other:'Прочее',
			darkness:'Тёмная',
			light:'Светлый'
		},
		wallet:{
			bind_bank_card:' Привязка банковских карт',
			currency:'Валюта',
			bank_name:'Название банка',
			enter_bank_name:'Введите название банка.',
			bank_address:' Адрес банка ',
			enter_bank_address:'Введите адрес банка.',
			swift:'SWIFT',
			payee:'Получатель',
			enter_the_payee:'Введите получателя',
			payee_account:'Счета получателей',
			enter_account:'Введите счет получателя.',
			add:'Добавить',
			bank_card:'Банковские карточки ',
			pay_currency:'Валюта поступлений',
			delete:'Удалить',
			bind_digital_currency_address:' Адрес привязанной цифровой валюты',
			wallet_address:'Адрес кошелька',
			enter_the_wallet_address:'Введите адрес кошелька',
			withdrawal_address:'Адрес монеты ',
			balance:'Остаток',
			digital_currency_address:'Адрес цифровой валюты',
		},
		invite:{
			invite_friends:'Пригласить друзей',
			invitation_code:'Код приглашения',
			copy_invitation_link:'Копировать ссылку приглашения',

		},
		 records:{
			financial_records:'Финансовая отчетность',
			quantity:'Количество',
			time:'Время',
			record:'Запись',
			chain_recharge_account:'По цепочке на счет.'
		},
		service:{
			contact_customer_service:'Связаться с клиентом',
			copy:'Копирование',
			complaints_mailbox:'Почтовый ящик для жалоб '
		},
		real:{
		    real_name_auth	:'Сертификация подлинного имени ',
		    upload_the_front_and_back:'Пожалуйста, загрузите обратную сторону паспорта / документа ',
			front:'Положительный',
			back:'Обратная сторона',
			improve_information:'Пожалуйста, уточните личную информацию.',
			name:'Имя',
			enter_name:'Введите имя',
			passport_idnumber:'Номер паспорта / документа',
			enter_passport_idnumber:'Пожалуйста, введите номер паспорта / документа ',
			submit:'Представлено',
			re_certification:'Переаутентификация',
			under_review:'В процессе проверки',
			upload_success_waiting:'Загрузка прошла успешно, ожидает проверки'
		},
		out:{
			withdrawal_record:'Отчет о выходе',
			number_of_withdrawal:'Количество монет',
			number_of_arrival:'Количество полученных счетов',
			unit:'Единицы',
			hand_fee:'Плата за услуги',
			status:'Статус',
			reipient_account:'Счета получателей',
			remarks:'Примечания',
			withdrawal:'Платежи',
			digital_currency:' Цифровая валюта',
			bank_card:'Банковские карточки',
			currency:'Валюта',
 			fill_quantity:'Количество зарядов',
			enter_number:'Введите количество монет. ',
			details:'детальный',
 			estimated_amount:'Прогнозируемое количество счетов',
			balance:'Остаток',
			withdrawal_:'Поднять монету',
			successful:'Успех',
			rejected:'Отклонено',
			withdrawal_adress:'Адрес монеты ',
			coin_withdrawal_record:'Запись вывода монет',
			prompt:'Подсказка',
			no_withdrawal_address:'У текущей валюты нет адреса вывода, добавить?',
			no_recipient_account:'У текущей валюты нет счета получателя, добавить?',
			please_enter_amount:'Пожалуйста, введите сумму вывода',
			please_select_currency:'Пожалуйста, выберите валюту вывода',
			please_select_address:'Пожалуйста, выберите адрес вывода',
			please_select_account:'Пожалуйста, выберите счет получателя',
			submit_failed:'Ошибка отправки',
			network_error:'Ошибка сети, попробуйте еще раз',
		},
		other:{
			other:'Прочее',
			logout:'Выход из системы',
			alerts:'Подсказка',
            to_log_out:'Определить выход из реестра？',
			cancel:'Отменить',
			confirm:'Определение',
			announcement:'Объявление',

		},
		deposit:{
		deposit_record:'Учет поступлений',
			deposit:' Поступления',
			digital_currency:'Цифровая валюта',
			bank_card:'Банковские карточки',
			select_net:'Выберите сеть',
			wallet_adress:'Адрес кошелька ',
			copy:'Копирование',
			number_of_coins:'Количество зарядов',
			enter_number_coins:'Введите количество монет. ',
			payment_voucher:'Платежные документы',
			upload_image:'Загрузить изображение',
			submit:'Представлено',
			recharge_amount:'Сумма пополнения',
			type:'Тип',
			unit:'Единицы измерения',
			status:'Статус',
			time:'Время',
			completed:'Завершено',
			invalid:'Неверный',
			currency:'Валюта поступлений ',
			recharge_account:'Валютный счет',


		},
		trade:{
			order_confirmed:' Заказ подтвержден. ',
			view_order:'Посмотреть заказ',
			confirm:'Подтверждение',
			contracts:'Контракт',
			second_contract:' Секундный контракт',
			open_number:'Количество открытых складов ',
			opening_time:'Время открытия склада ',
			profit_rate:'Процентная ставка',
			estimated_commission:'Сметные расходы',
			sheet:'1sheet',
			buy_down:'Купить',
			buy_up:'повышение',
			trade:'Сделка по заказу ',
			pending_order:'Операции по заказу',
			price:'Цены',
			multiplier:'Удвоение',
			stop_loss:'Остановить',
			take_prodit:'Ликвидация',
			buy_quantity:'Количество',
			each:'Каждый',
 			estimated_margin:' Предполагаемый залог',
			balance:'Остаток',
			buy:'Купить',
			sell:'Продать',
			minutes:'Минуты',
			hour:'Часы',
			more:'Больше'
		}

}
