import $C from '@/common/config.js'
export default {
	// 请求拦截器 原理：利用微任务.then 让所有使用请求拦截器的函数 在拦截器函数之后执行
	config: {
		// 请求拦截器(给请求统一添加 公共请求头、baserUrl、method)
		beforeRequest(options = {}) {
			return new Promise((resolve, reject) => {
				options.header = {
						// "Authorization":"Bearer "+uni.getStorageSync('token'),
						"Authorization": "Bearer " + uni.getStorageSync('token') || '',
						"Content-Type": "application/json"
					},
					options.data['lang'] = uni.getStorageSync('lang') || 'zh';
				resolve(options)
			})
		},
		// 响应拦截器 (接收参数请求后得到的数据，处理非成功数据reject，并将数据剥离返回resolve)
		responseRequest(data) {
			return new Promise((resolve, reject) => {
				// const [error, res] = data
				if (data.data.code == '401') {
					// uni.$u.toast(this.$t("real.re_certification"));
					setTimeout(() => {
						return uni.navigateTo({
							// url:'/',
							url: '/pages/login/login'
						})
					}, 1000)
					return false;
				}
				if (data.data.code == '403') {
					return resolve(data)
				}
				if (data.data.data.type == "ok") {
					// 	const msg = data.data.data.message || '请求成功'			
					// uni.$u.toast(msg);


				} else if (data.data.data.type == "error") {
					const msg = data.data.data.message || '请求失败'
					uni.$u.toast(msg);
				}

				// console.log(error, res)
				// if (res.data.msg !== 'ok') {
				// 	const msg = res.data.data || '请求失败'
				// 	uni.showToast({
				// 		title: msg,
				// 		icon: 'none'
				// 	})
				// 	if(msg === 'Token 令牌不合法，请重新登录') {
				// 		store.dispatch('loginOut')
				// 		uni.navigateTo({
				// 			url:'/pages/login/login'
				// 		})
				// 	}
				// 	return reject(msg)
				// }
				return resolve(data.data.data)
			})
		}
	},
	common: {
		method: 'GET',
		header: {
			"content-type": "application/json"
		},
		data: {

		}
	},



	request(options = {}) {
		options.url = $C.webUrl + options.url
		options.method = options.method || this.common.method
		options.header = {
			// "Authorization":"Bearer "+uni.getStorageSync('token'),
			"Authorization": "Bearer " +
				"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
			"Content-Type": "application/json"
		}
		// return this.config.beforeRequest(options).then(opt => {
		// 			return uni.request(opt)
		// 		}).then(this.config.responseRequest)
		return uni.request(options)
	},

	requestThen(options = {}) {
		options.url = $C.webUrl + options.url
		options.method = options.method || this.common.method
		options.header = {
			// "Authorization":"Bearer "+uni.getStorageSync('token'),
			"Authorization": "Bearer " + "",
			"Content-Type": "application/json"
		}
		return this.config.beforeRequest(options).then(opt => {
			return uni.request(opt)
		}).then(this.config.responseRequest)

	},


	get(url, isThen = false, data = {}, options = {}) {
		options.url = url
		options.data = data
		options.method = 'GET'
		if (isThen) {
			return this.request(options)
		}
		return this.requestThen(options)
	},
	post(url, isThen = false, data = {}, options = {}) {
		options.url = url
		options.data = data
		options.method = 'POST'
		if (isThen) {
			return this.request(options)
		}
		return this.requestThen(options)
	}

}