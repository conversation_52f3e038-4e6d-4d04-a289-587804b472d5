@mixin bg_color($color) { //更换背景颜色
	background-color: $color ;
	[data-theme = "theme1"] & {
		background-color: #1A1C24 !important;
		position: fixed;
		   overflow: hidden;
		   height: 100%;
	}
	[data-theme = "theme2"] & {
		background-color: #ffffff !important;
		position: fixed;
		   overflow: hidden;
		   height: 100%;
	}		
}
@mixin sbg_color($color) { //更换背景颜色
	background-color: $color ;
	[data-theme = "theme1"] & {
		background: #12151B !important;
		
	}
	[data-theme = "theme2"] & {
	background: #12151B !important;
		
	}		
} 
