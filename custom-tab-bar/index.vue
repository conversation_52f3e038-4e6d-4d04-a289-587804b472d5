<template>
	<view>
		<view v-if="$store.state.bgColor=='while'">
			<u-tabbar :value="index" @change="tabBarChange" :fixed="true" :placeholder="true"
				:safeAreaInsetBottom="true" activeColor="#3f47f4 ">
				<u-tabbar-item :text="i18n.rw1">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[0].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[0].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw2">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[1].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[1].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item>
					<template #active-icon>
						<image class="u-page__item__slot-icon-big" :src="list[2].selectedIconPath">
						</image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon-big" :src="list[2].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw3">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[3].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[3].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw4">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[4].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[4].iconPath"></image>
					</template>
				</u-tabbar-item>
			</u-tabbar>
			<view>
				<u-popup :overlayOpacity="0.6" :round="10" :show="show" @close="close" @open="open">
					<view class="issue-item">
						<view class="issue-item-buy" @click="toBuy">
							<text>我要卖</text>
						</view>
						<view class="issue-item-sell">
							<text>我要买</text>
						</view>
					</view>
				</u-popup>
			</view>
		</view>

		<view v-if="$store.state.bgColor=='black'">
			<u-tabbar :value="index" @change="tabBarChange" :fixed="true" :placeholder="true"
				:safeAreaInsetBottom="true" activeColor="#3f47f4 ">
				<u-tabbar-item :text="i18n.rw1" style="background-color: #22252f;">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[0].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[0].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw2" style="background-color: #22252f;">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[1].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[1].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item style="background-color: #22252f;">
					<template #active-icon>
						<image class="u-page__item__slot-icon-big" :src="list[2].selectedIconPath">
						</image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon-big" :src="list[2].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw3" style="background-color: #22252f;">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[3].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[3].iconPath"></image>
					</template>
				</u-tabbar-item>

				<u-tabbar-item :text="i18n.rw4" style="background-color: #22252f;">
					<template #active-icon>
						<image class="u-page__item__slot-icon" :src="list[4].selectedIconPath"></image>
					</template>
					<template #inactive-icon>
						<image class="u-page__item__slot-icon" :src="list[4].iconPath"></image>
					</template>
				</u-tabbar-item>
			</u-tabbar>
			<view>
				<u-popup :overlayOpacity="0.6" :round="10" :show="show" @close="close" @open="open">
					<view class="issue-item">
						<view class="issue-item-buy" @click="toBuy">
							<text>我要卖</text>
						</view>
						<view class="issue-item-sell">
							<text>我要买</text>
						</view>
					</view>
				</u-popup>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				show: false,
				list: [{
						"pagePath": "/pages/index/index",
						"text": "行情",
						"iconPath": "/static/tabbar/<EMAIL>",
						"selectedIconPath": "/static/tabbar/<EMAIL>"
					},
					{
						"pagePath": "/pages/order/order",
						"text": "订单",
						"iconPath": "/static/tabbar/<EMAIL>",
						"selectedIconPath": "/static/tabbar/<EMAIL>"
					},
					{
						"pagePath": "/pages/stock/stock",
						"text": "交易",
						"iconPath": "/static/tabbar/<EMAIL>",
						"selectedIconPath": "/static/tabbar/<EMAIL>"
					},
					{
						"pagePath": "/pages/news/news",
						"text": "资讯",
						"iconPath": "/static/tabbar/<EMAIL>",
						"selectedIconPath": "/static/tabbar/<EMAIL>"
					},
					{
						"pagePath": "/pages/users/users",
						"text": "我的",
						"iconPath": "/static/tabbar/<EMAIL>",
						"selectedIconPath": "/static/tabbar/<EMAIL>"
					}
				]
			}
		},
		computed: {
			index() {
				return this.$store.state.tabbarIndex
			},
			i18n() {
				return this.$t("order")
			},
			...mapState({
				socket: state => state.socket,
				token: state => state.token
			})
		},
		methods: {
			sertNavs() {
				this.navs = []
				this.navsList = []
				this.cnavsList = []
				const {
					i18n
				} = this
				this.navs.push(i18n.contract)
				this.navs.push(i18n.second_contract)
			
				this.navsList.push(i18n.position)
				this.navsList.push(i18n.listing)
				this.navsList.push(i18n.history)
			
				this.cnavsList.push(i18n.position)
				this.cnavsList.push(i18n.history)
			},
			tabBarChange(e) {
				// console.log(1111)
				// if (e !== 2) {
				uni.navigateTo({
					url: this.list[e].pagePath
				})
				// }
			},
			//点击发布按钮的弹出层
			open() {
				console.log('open');
			},
			close() {
				this.show = false;
				console.log('close');
			},
			//点击我要卖
			toBuy() {
				console.log("点击了");
				uni.switchTab({
					url: '/pages/issue/issue'
				})
			}
		}
	}
</script>

<style>
	/* 	.u-tabbar__content__item-wrapper{
		height: 100px !important;
	} */
	.u-page__item__slot-icon {
		width: 70rpx;
		height: 70rpx;
	}

	.u-page__item__slot-icon-big {
		width: 110rpx;
		height: 110rpx;
		margin-bottom: 100rpx;
	}

	.whiles {
		background-color: black !important;
	}

	.blacks {
		background-color: black !important;
	}

	.issue-item {
		height: 600rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		.issue-item-buy,
		.issue-item-sell {
			width: 30%;
			height: 100rpx;
			font-size: 28rpx;
			border-radius: 20rpx;
			background-color: pink;
			margin: 40rpx;
			line-height: 100rpx;
			text-align: center;
		}
	}
</style>