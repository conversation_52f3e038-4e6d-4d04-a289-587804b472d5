<script>
	// #ifndef H5
	import {
		JSConsole
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.console.wechat.js'
	//禁用日志
	JSConsole.Complier.Log = () => {};
	JSConsole.Chart.Log = () => {};
	// #endif

	export default {
		onLaunch: function() {
			console.log('App Launch')
			this.$store.dispatch('connectSocket')
			this.$store.commit('initUser')
			this.$utils.setTabbar(this)
			window.document.documentElement.setAttribute('lang', 'en');
		},
		onShow: function() {
			if (this.$store.state.bgColor == 'black') {
				window.document.documentElement.setAttribute('data-theme', 'theme1');
				uni.setTabBarStyle({
					color: '#24b124',
					selectedColor: '#fff',
					backgroundColor: '#2F3142'
				})
				uni.setTabBarItem({
					index: 0,
					iconPath: '/static/<EMAIL>',
					selectedIconPath: '/static/<EMAIL>'
				})

				uni.setTabBarItem({
					index: 1,
					iconPath: '/static/<EMAIL>',
					selectedIconPath: '/static/dd1 <EMAIL>'
				})
				uni.setTabBarItem({
					index: 2,
					iconPath: '/static/<EMAIL>',
					selectedIconPath: '/static/jy1 <EMAIL>'
				})
				uni.setTabBarItem({
					index: 3,
					iconPath: '/static/<EMAIL>',
					selectedIconPath: '/static/zx1 <EMAIL>'
				})
				uni.setTabBarItem({
					index: 4,
					iconPath: '/static/<EMAIL>',
					selectedIconPath: '/static/wd1 <EMAIL>'
				})
			} else {
				window.document.documentElement.setAttribute('data-theme', 'theme2');
				uni.setTabBarStyle({
					color: '#0C0C0C',
					selectedColor: '#3F47F4',
					backgroundColor: '#FFFFFF'
				})
				uni.setTabBarItem({
					index: 0,
					"iconPath": "static/tabbar/<EMAIL>",
					"selectedIconPath": "static/tabbar/<EMAIL>"
				})
				uni.setTabBarItem({
					index: 1,
					"iconPath": "static/tabbar/<EMAIL>",
					"selectedIconPath": "static/tabbar/<EMAIL>"
				})
				uni.setTabBarItem({
					index: 2,
					"iconPath": "static/tabbar/<EMAIL>",
					"selectedIconPath": "static/tabbar/<EMAIL>"
				})
				uni.setTabBarItem({
					index: 3,
					"iconPath": "static/tabbar/<EMAIL>",
					"selectedIconPath": "static/tabbar/<EMAIL>"
				})
				uni.setTabBarItem({
					index: 4,
					"iconPath": "static/tabbar/<EMAIL>",
					"selectedIconPath": "static/tabbar/<EMAIL>"
				})
			}
			console.log('App Show')
		},
		watch: {
			'$store.state.bgColor'(val) {
				if (val == 'black') {
					window.document.documentElement.setAttribute('data-theme', 'theme1');
					uni.setTabBarStyle({
						color: '#9194B1',
						selectedColor: '#fff',
						backgroundColor: '#2F3142'
					})
					uni.setTabBarItem({
						index: 0,
						iconPath: '/static/<EMAIL>',
						selectedIconPath: '/static/<EMAIL>'
					})
					uni.setTabBarItem({
						index: 1,
						iconPath: '/static/<EMAIL>',
						selectedIconPath: '/static/dd1 <EMAIL>'
					})
					uni.setTabBarItem({
						index: 2,
						iconPath: '/static/<EMAIL>',
						selectedIconPath: '/static/jy1 <EMAIL>'
					})
					uni.setTabBarItem({
						index: 3,
						iconPath: '/static/<EMAIL>',
						selectedIconPath: '/static/zx1 <EMAIL>'
					})
					uni.setTabBarItem({
						index: 4,
						iconPath: '/static/<EMAIL>',
						selectedIconPath: '/static/wd1 <EMAIL>'
					})
				} else if (val == 'while') {
					window.document.documentElement.setAttribute('data-theme', 'theme2');
					uni.setTabBarStyle({
						color: '#0C0C0C',
						selectedColor: '#3F47F4',
						backgroundColor: '#FFFFFF'
					})
					uni.setTabBarItem({
						index: 0,
						"iconPath": "static/tabbar/<EMAIL>",
						"selectedIconPath": "static/tabbar/<EMAIL>"
					})
					uni.setTabBarItem({
						index: 1,
						"iconPath": "static/tabbar/<EMAIL>",
						"selectedIconPath": "static/tabbar/<EMAIL>"
					})
					uni.setTabBarItem({
						index: 2,
						"iconPath": "static/tabbar/<EMAIL>",
						"selectedIconPath": "static/tabbar/<EMAIL>"
					})
					uni.setTabBarItem({
						index: 3,
						"iconPath": "static/tabbar/<EMAIL>",
						"selectedIconPath": "static/tabbar/<EMAIL>"
					})
					uni.setTabBarItem({
						index: 4,
						"iconPath": "static/tabbar/<EMAIL>",
						"selectedIconPath": "static/tabbar/<EMAIL>"
					})
				}
			}
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods:{
			setUser(){
				this.$H.get('/api/v1/user', false).then(result => {
					this.$store.commit('setUser', result.data)
				})
			}
		}
	}
</script>
<style>
	@import "colorui/main.css";
	@import "colorui/icon.css";
	@import "common/iconfont.css";
</style>
<style lang="scss">
	.clamp-3 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
	}

	.clamp-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.clamp-1 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	@import "uview-ui/index.scss";

	.cu-list.menu>.cu-item.arrow>.cu-item.isactive {
		background: #222;
	}

	/deep/.cu-list.menu>.cu-item.arrow .isactive {
		background: #222;
	}

	page[data-theme='pink'] {
		background: #1A1C24 !important;
		position: fixed;
		overflow: hidden;
	}

	page {
		// background: #ffff7f !important;

	}

	page {
		@include bg_color(theme)
	}
</style>
