import App from './App'
import store from './store/index.js'

// #ifndef VUE3
import Vue, {
	normalizeClass
} from 'vue'
import './uni.promisify.adaptor'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.config.productionTip = false
import uView from 'uview-ui'
Vue.use(uView)

// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置
// 需要在Vue.use(uView)之后执行
uni.$u.setConfig({
	// 修改$u.config对象的属性
	config: {
		// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
		unit: 'rpx'
	},
	// 修改$u.props对象的属性
	props: {
		// 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
		radio: {
			size: 15
		}
		// 其他组件属性配置
		// ......
	}
})

Vue.prototype.$store = store
// 引入配置文件
import $C from './common/config.js';
Vue.prototype.$C = $C
import $H from './common/request.js';
Vue.prototype.$H = $H

import utils from './common/utils'
Vue.prototype.$utils = utils

import * as filters from '@/common/filters.js'

Object.keys(filters).forEach(key => {
	Vue.filter(key, filters[key]) //插入过滤器名和对应方法
})

import VueClipBoard from 'vue-clipboard2'

Vue.use(VueClipBoard)
import i18n from '@/common/locales/config.js'
import customTabBar from './custom-tab-bar/index.vue'
Vue.component('tab-bar', customTabBar)
Vue.prototype._i18n = i18n;

// import $cs from './common/black.css';

Vue.use(ElementUI);

Vue.prototype.auths = (callbacks) => {
	if (!store.state.lgStatus || !store.state.users) {

		if (uni.getStorageSync('user')) {

			store.state.users = JSON.parse(uni.getStorageSync('user'))
			store.state.lgStatus = true
			store.state.token = uni.getStorageSync('token')
			callbacks()

		} else {
			// uni.showToast({
			// 	title:this.$t("real.re_certification"),
			// 	icon:'none'
			// })

			// setTimeout(() => {
			// 	return uni.reLaunch({
			// 		url: '/'
			// 	})
			// }, 1000)
			// callbacks()
		}
 
	} else {
		callbacks()
	}

}
App.mpType = 'app'
const app = new Vue({
	i18n,
	 el: '#app',
	store,
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import uView from 'uview-ui'
Vue.use(uView)
// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置
// 需要在Vue.use(uView)之后执行
uni.$u.setConfig({
	// 修改$u.config对象的属性
	config: {
		// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
		unit: 'rpx'
	},
	// 修改$u.props对象的属性
	props: {
		// 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
		radio: {
			size: 15
		}
		// 其他组件属性配置
		// ......
	}
})



export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif