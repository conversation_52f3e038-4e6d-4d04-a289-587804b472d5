                     HQChart用户协议

用户应当充分阅读本协议，用户使用HQChart插件亦视为接受本协议。
一、定义
   HQChart：由本人开发的专业的K线图展示及数据计算引擎插件。
   用户： 以任何形式使用HQChart插件的自然人、法人或其他组织。
二、用户保证
   1. 用户同意并保证，合法并如约使用HQChart插件。用户对其行为产生的任何法律责任自行独立承担，与HQChart插件作者无关。
三、用户的权利与义务
   1. 用户有权通过修改HQChart源码进行2次开发,无需经得HQChart插件作者授权.
   2. 用户在使用HQChart插件期间，承诺并保证：
    （1） 遵守所有中华人民共和国法律法规和国际上有关互联网和短信的协议、规定、程序和惯例.
    （2） 不利用HQChart插件进行金融诈骗.
    （3） 不行使其他可能给HQChart插件带来任何不利影响的行为或者法律及政策禁止的行为。
  3. 用户不得利用HQChart插件发送以下内容的短信：
    （1）接受方事先没有提出要求或者同意接受的广告等宣传性或营销性内容；
    （2）含有病毒、恶意代码、色情、反动等不良信息或有害信息
    （3）地产、医疗、教育、留学、移民等相关内容
    （4）冒充任何人或机构，或以虚伪不实的方式谎称或使人误认为与任何人或任何机构有关。
    （5）侵犯他人著作权或其他知识产权，或违反保密、雇佣或不披露协议披露他人商业秘密或保密信息。
   4. 用户应当按照本协议，妥善使用HQChart插件并为其行为负责。因用户行为导致HQChart插件作者遭受诉讼、索赔及/或处罚的，用户应赔偿HQChart插件作者因此造成的全部损失。
四、HQChart作者的权利与义务
   1. HQChart作者对用户行为无审核监督义务。因用户行为给HQChart作者造成的一切损失，HQChart作者有权向用户追偿。
   2. HQChart插件对其提供给用户的质量做出如下承诺
     1）. 源码全部开源
     2）. 在作者技术能力范围以内，提供免费或有偿的技术支持服务
   3.HQChart作者有权对HQChart插件进行定期或不定期的升级或完善，用户可以选择升级新版本或不升级使用原有版本。如用户因此受到影响，HQChart作者无需为此承担任何责任。
   4. HQChart插件作者有权根据自己的判断对用户是否违反本协议约定。用户若有违反的，HQChart插件作者有权随时中止或终止本协议。HQChart作者无需就此征得用户同意或提前通知用户
   5. 鉴于互联网服务的特殊性，对因不可抗力、第三方服务、黑客攻击、政策影响及其他任何非. HQChart插件作者原因引起的技术支持中断等，HQChart插件作者均无需承担任何责任。

五、 免责声明
   1. 用户明确同意其使用HQChart插件所存在的风险及其后果将完全由其自己承担，HQChart插件作者对用户不承担任何责任。如因用户违反有关法律、法规或本协议项下的任何条款而给HQChart插件作者或任何其他第三人造成损失，用户同意承担由此造成的损害赔偿责任。

六、其他
  1. 本协议的版权归HQChart插件作者，HQChart插件作者保留对本协议的一切解释和修改权利。
  2. 本协议的订立、执行和争议的解决均应适用中华人民共和国法律。如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向HQChart插件作者所在地有管辖权的人民法院提起诉讼。
  3. HQChart插件作者行使本协议的任何权利或规定，不构成对前述权利之放弃。
  4. 如本协议中的任何条款完全或部分无效，本协议的其余条款仍有效并且有约束力


