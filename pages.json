{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/order/order",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"

			}

		}, {
			"path": "pages/stock/stock",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"meta": {
					"cache": false // 设置此页面不缓存
				},
				"app-plus": {

				}
			}

		}, {
			"path": "pages/news/news",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"onReachBottomDistance": 100
			}
		}, {
			"path": "pages/news/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 100
			}
		}, {
			"path": "pages/users/users",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}, {
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/article/article",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#ffffff"

			}

		}

		, {
			"path": "pages/users/financial/financial",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#ffffff",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/withdraw/withdraw",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 100
			}

		}, {
			"path": "pages/users/cjrecord/cjrecord",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 100
			}

		}, {
			"path": "pages/users/amount/amount",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "",
							"float": "right",
							"fontSize": "14px",
							"fontWeight": "bold",
							"width": "auto"
						}]
					}
				}
			}

		}, {
			"path": "pages/users/rjrecord/rjrecord",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 100
			}

		}, {
			"path": "pages/users/authen/authen",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#ffffff",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/uppwd/uppwd",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/news/financialApplication/financialApplication",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/news/record/record",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/news/earnings/earnings",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/wallet/wallet",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#ffffff",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/digital/digital",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "+Add",
							"color": "#3F47F4",
							"float": "right",
							"fontSize": "15px",
							"fontWeight": "bold",
							"width": "auto"
						}]
					}
				}
			}

		}, {
			"path": "pages/users/digital/addDigital/addDigital",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/banks/banks",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "+Add",
							"color": "#3F47F4",
							"float": "right",
							"fontSize": "15px",
							"fontWeight": "bold",
							"width": "auto"
						}]
					}
				}
			}

		}, {
			"path": "pages/users/banks/addBanks/addBanks",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/notice/notice",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"onReachBottomDistance": 100
			}

		}, {
			"path": "pages/users/contact/contact",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/others/others",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/invite/invite",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/users/language/language",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/detail/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": false
				}
			}

		}, {
			"path": "pages/test/test",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/socket/socket",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/register/register",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": false
				}
			}

		}, {
			"path": "pages/resetPass/resetPass",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": false
				}
			}

		}, {
			"path": "pages/registerTwo/registerTwo",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": false
				}
			}

		}, {
			"path": "pages/stockT/stockT",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/users/complaint/complaint",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/articles/articles",
			"style":

			{
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/language/language",
			"style": {
				"navigationBarTitleText": ""
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "FX7",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#ffffff",
		"app-plus": {
			"bounce": "none",
			"scrollIndicator": "none"
		}
	},
	"tabBar": {
		"custom": true,
		"color": "#020c07",
		"selectedColor": "#3F47F4",
		"backgroundColor": "#4fff1a",
		"borderStyle": "black",
		"height": 900 // 这里设置TabBar的高度，单位默认为px
		// "list": [{
		// 	"pagePath": "pages/index/index",
		// 	"iconPath": "static/tabbar/<EMAIL>",
		// 	"selectedIconPath": "static/tabbar/<EMAIL>",
		// 	"text": "行情"
		// }, {
		// 	"pagePath": "pages/order/order",
		// 	"iconPath": "static/tabbar/<EMAIL>",
		// 	"selectedIconPath": "static/tabbar/<EMAIL>",
		// 	"text": "订单"

		// }, {
		// 	"pagePath": "pages/stock/stock",
		// 	"iconPath": "static/tabbar/<EMAIL>",
		// 	"selectedIconPath": "static/tabbar/<EMAIL>",
		// 	"text": "交易"
		// }
		// // {
		// // 	"pagePath": "pages/news/news",
		// // 	"iconPath": "static/tabbar/<EMAIL>",
		// // 	"selectedIconPath": "static/tabbar/<EMAIL>",
		// // 	"text": "资讯"
		// // }
		// , {
		// 	"pagePath": "pages/users/users",
		// 	"iconPath": "static/tabbar/<EMAIL>",
		// 	"selectedIconPath": "static/tabbar/<EMAIL>",
		// 	"text": "我的"
		// }]
	},
	"uniIdRouter": {}

}
