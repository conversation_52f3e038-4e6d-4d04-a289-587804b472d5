<template>
	<view style="display: flex; flex-direction: column; " :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<!-- 	<view v-if="$store.state.bgColor=='black'"
			style="display: flex; background: #1A1B24; justify-content: center; align-items: center; width: 100%;line-height: 55px; border-bottom: 2px solid #22252F;">
			<view class="u-fade-enter-active"></view>
			<u-subsection :list="navs" inactiveColor="#828397" activeColor="#ffffff" :current="currentNav"
				style="flex:1" @change="sectionChange"></u-subsection>
			<uni-icons @click="searchs()" size="30"></uni-icons>
		</view> -->


		<!--
		<view v-if="$store.state.bgColor=='while'"
			style="display: flex; background: #fff; justify-content: center; align-items: center; width: 100%;line-height: 55px; border-bottom: 2px solid #F4F4F4;">
		 <view class="u-fade-enter-active"></view>
			<u-subsection:list="navs" :current="currentNav" style="flex:1; " @change="sectionChange"></u-subsection>
			<uni-icons@click="searchs()" size="30"></uni-icons>

		</view> -->


		<view v-show="currentNav == 0" class="jypzs">
			<view style="height: 15px;"></view>
			<!-- 顶部选项卡 -->

			<view class="scroll-view" v-if="$store.state.bgColor=='while'">
				<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
					:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
					:id="'tab'+index">{{item}}</view>
			</view>
			<view style="height: 0px;"></view>

			<!-- 持有 -->
			<block v-if="currentNav==0">
				<view class="prtxts" v-if="tabIndex==0"
					style="display: flex; margin: 0 auto; flex-direction: column; justify-content: center; padding: 7px 0; align-items: center; width: 95%; margin-top: 12px; box-shadow: 0 0 6.4vw rgba(112, 112, 112, 0.1);">
					<text style="margin: 10px 0;">{{i18n.profit_Loss}}</text>
					<text style="font-size: 60rpx;font-weight: 700;"
						:class="{'positive': hylist.yks > 0, 'negative': hylist.yks < 0}">{{hylist.yks}}</text>
					<view
						style="display: flex; justify-content: space-between;align-items: flex-start; width: 100%; margin: 15px 0px 5px;">
						<text style="text-align: center;margin-left: 15px;color: #AFAFAF;">{{mine.balance}}</text>
						<!-- <text style=" text-align: center; font-weight: 700; margin-right: 15px;">
							{{(parseFloat(this.$store.state.users.usdt)+parseFloat(hylist.yks)).toFixed(2)}}
						</text> -->
						<text style=" text-align: center; font-weight: 700; margin-right: 15px;">
							{{(users.usdt).toFixed(2)}}
						</text>
					</view>
					<view
						style="display: flex;justify-content: space-between;align-items: flex-start; width: 100%;margin: 5px 0 5px 0;">
						<text
							style="text-align: center; margin-left: 15px;color: #AFAFAF;">{{i18n.current_deposit}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{hylist.caution_money}}</text>
					</view>
					<view
						style="display: flex;justify-content: space-between;align-items: flex-start; width: 100%;margin: 5px 0 5px 0;">
						<text style="text-align: center; margin-left: 15px;color: #AFAFAF;">{{i18n.risk_rate}}</text>
						<text style=" text-align: center; font-weight: 700; margin-right: 15px;"
							v-if="$store.state.users.usdt">{{parseFloat((hylist.caution_money) / ($store.state.users.usdt)*100).toFixed(4)}}%
						</text>
						<text style=" text-align: center; font-weight: 700; margin-right: 15px;" v-else>0%
						</text>
					</view>

				</view>
				<!-- 挂单 -->
				<view class="prtxts" v-if="tabIndex==1"
					style="display: flex;margin: 0 auto; flex-direction: column; justify-content: center; padding: 7px 0; align-items: center; width: 95%;margin-top: 12px; box-shadow: 0 0 6.4vw rgba(112, 112, 112, 0.1);">
					<!-- <text style="margin: 10px 0;">{{i18n.profit_Loss}}</text>
					<text style="font-size: 60rpx;font-weight: 700;"
						:class="{'positive': hylist1.yks > 0, 'negative': hylist1.yks < 0}">{{hylist1.yks}}</text>
					<view
						style="display: flex; justify-content: space-between;align-items: flex-start; width: 100%; margin: 15px 0px 5px;">
						<text style="text-align: center;margin-left: 15px;color: #AFAFAF;">{{mine.balance}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{(parseFloat(this.$store.state.users.usdt)+parseFloat(hylist1.yks)).toFixed(2)}}</text>

					</view> -->
					<view
						style="display: flex;justify-content: space-between;align-items: flex-start; width: 100%;margin: 5px 0 5px 0;">
						<text
							style="text-align: center; margin-left: 15px;color: #AFAFAF;">{{i18n.current_deposit}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{hylist1.caution_money}}</text>

					</view>
					<!-- <view
						style="display: flex;justify-content: space-between;align-items: flex-start; width: 100%;margin: 5px 0 5px 0;">
						<text style="text-align: center; margin-left: 15px;color: #AFAFAF;">{{i18n.risk_rate}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{parseFloat((hylist1.caution_money) / (this.$store.state.users.usdt)*100).toFixed(4)}}%</text>
					</view> -->
				</view>
				<!-- 历史 -->
				<view class="prtxts" v-if="tabIndex==2"
					style="display: flex;margin: 0 auto; flex-direction: column; justify-content: center; padding: 7px 0; align-items: center; width: 95%; margin-top: 12px;box-shadow: 0 0 6.4vw rgba(112, 112, 112, 0.1);">
					<text style="margin: 10px 0;">{{i18n.profit_Loss}}</text>
					<text style="font-size: 60rpx;font-weight: 700;"
						:class="{'positive': hylist2.yks > 0, 'negative': hylist2.yks < 0}">{{hylist2.yks}}</text>
					<view
						style="display: flex; justify-content: space-between;align-items: flex-start; width: 100%; margin: 15px 0px 5px;">
						<text style="text-align: center;margin-left: 15px;color: #AFAFAF;">{{mine.balance}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{(users.usdt).toFixed(2)}}</text>
					</view>
					<view
						style="display: flex; margin: 15px 0px 5px; justify-content: space-between;align-items: flex-start; width: 100%;margin: 15px 0 15px 0;">
						<text
							style=" margin-left: 15px;text-align: center; color: #AFAFAF;">{{i18n.current_deposit}}</text>
						<text
							style=" text-align: center; font-weight: 700; margin-right: 15px;">{{hylist.caution_money}}</text>

					</view>
				</view>
			</block>

			<!-- 持仓信息 -->
			<swiper :duration="150" :current="tabIndex" @change="onChangeTab"
				:style="'height:'+scrollH+'px;overflow: hidden;width:95%;padding-left:15px; '">
				<swiper-item>
					<scroll-view scroll-y enable-flex="true" class="section-area" catchtouchmove="true"
						:style="'height:'+scrollH+'px;'" @scrolltolower="scrolltolower1()">
						<view style="width: 100%; " ref="ulists0">
							<view>
								<u-list class="sfds" style=" width:100%; ">
									<u-list-item style=" padding-right: 20px;" v-for="(orderItem, index) in indexList"
										:key="index">
										<u-cell>
											<view slot="title"
												style="display: flex; flex-direction: column;font-weight: bold;"
												@click.stop="clicks(orderItem)">
												<!-- 仓 -->
												<view style="display: flex; flex-direction: column;"
													v-if="$store.state.bgColor=='while'">
													<!-- 币名 -->
													<view style="height: 25px; margin-bottom: 10px;">
														<text class="u-cell-text"
															style="font-weight: 500;color: #000;">{{orderItem.currency_info.alias}}
														</text>
													</view>
													<!-- 数据 -->
													<view
														style="display: flex; justify-content: space-between;height: 16px;">
														<view style="display: flex; ">
															<span
																style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">
																{{parseFloat(orderItem.price)}}→{{parseFloat(orderItem.update_price)}}</span>
														</view>
														<view class="">
															<view class="quantity_to_buy"
																v-if="orderItem.type_name=='买入'">
																<view class="left-side"
																	v-if="orderItem.type_name=='买入'">
																	{{$t('trade.buy_up')}}
																</view>
																<view class="right-side">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
															<view class="quantity_to_buy2"
																v-if="orderItem.type_name=='卖出'">
																<view class="left-side2"
																	v-if="orderItem.type_name=='卖出'">
																	{{$t('trade.buy_down')}}
																</view>
																<view class="right-side2">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
														</view>

													</view>
													<!-- 时间 -->
													<view style="display: flex; justify-content: space-between;">
														<view style="display: flex;flex-direction: column;">
															<view class=""
																style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
																{{i18n.order_num}}
																<view style="width: 10rpx;"></view>
																{{orderItem.id}}
															</view>
															<text
																style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{orderItem.transaction_time}}
															</text>

														</view>
														<text style="color: #0166fc; font-size: 22px;"
															v-if="orderItem.upsdowns>=0">{{orderItem.upsdowns}}</text>
														<text v-if="orderItem.upsdowns<0"
															style="color: #F23C48; font-size: 22px;">{{orderItem.upsdowns}}</text>
													</view>
												</view>
											</view>
										</u-cell>
										<view v-if="!orderItem"
											style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
											<image src="../../static/notAvailable.png" style="width:250px; "></image>
											<text style="color: #676767;">
												{{i18n.no}}
											</text>
										</view>
									</u-list-item>
									<u-list-item style=" padding-right: 20px;height: 60px;">
									</u-list-item>
									<!-- 没有数据时 -->
									<view v-if="!indexList.length"
										style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
										<image src="../../static/notAvailable.png" style="width:250px; "></image>
										<text style="color: #676767;">
											{{i18n.no}}
										</text>
									</view>
								</u-list>
							</view>
						</view>
						<view style="height: 15%"></view>
					</scroll-view>
				</swiper-item>
				<swiper-item>
					<scroll-view scroll-y enable-flex="true" class="section-area" :style="'height:'+scrollH+'px;'"
						@scrolltolower="scrolltolower2()">
						<view style="width: 100%; " ref="ulists1">
							<view>
								<u-list class="sfds" style="width: 100%; ">
									<u-list-item style=" padding-right: 20px;" v-for="(orderItem, index2) in indexList1"
										:key="index2">
										<u-cell>
											<view slot="title" style="display: flex; flex-direction: column;"
												@click.stop="gclicks(orderItem)">
												<view style="display: flex; font-weight: bold;color: #ffffff;"
													v-if="$store.state.bgColor=='black'">
													<text class="u-cell-text">{{orderItem.currency_info.alias}}<span
															v-if="orderItem.type_name=='卖出'"
															class="mcs">{{$t('trade.buy_down')}}</span><span
															v-if="orderItem.type_name=='买入'"
															class="mrs">{{$t('trade.buy_up')}}</span></text>
													<text class="u-cell-text"
														style="flex: 1; justify-content: center; text-align: left; padding-left: 12px;"><span
															style="font-weight:1; margin-right: 5px; font-size: 12px;">{{i18n.quantity}}</span>{{parseFloat(orderItem.number).toFixed(2)}}</text>
													<text class="u-cell-text" style="color: #0166fc;"
														v-if="orderItem.upsdowns>=0"><!-- {{orderItem.upsdowns}} -->0</text>
													<text class="u-cell-text" v-if="orderItem.upsdowns<0"
														style="color: #F23C48;"><!-- {{orderItem.upsdowns}} -->0</text>
												</view>
												<view style="display: flex; flex-direction: column;"
													v-if="$store.state.bgColor=='while'">
													<!-- 币名 -->
													<view style="height: 25px; margin-bottom: 10px;">
														<text class="u-cell-text"
															style="font-weight: 500;color: #000;">{{orderItem.currency_info.alias}}
														</text>
													</view>
													<!-- 数据 -->
													<view
														style="display: flex; justify-content: space-between;height: 16px;">
														<view style="display: flex; ">
															<span
																style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">{{parseFloat(orderItem.price)}}→
																{{parseFloat(orderItem.update_price)}}</span>
														</view>
														<view class="">
															<view class="quantity_to_buy"
																v-if="orderItem.type_name=='买入'">
																<view class="left-side"
																	v-if="orderItem.type_name=='买入'">
																	{{$t('trade.buy_up')}}
																</view>
																<view class="right-side">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
															<view class="quantity_to_buy2"
																v-if="orderItem.type_name=='卖出'">
																<view class="left-side2"
																	v-if="orderItem.type_name=='卖出'">
																	{{$t('trade.buy_down')}}
																</view>
																<view class="right-side2">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
														</view>
													</view>
													<!-- 时间 -->
													<view style="display: flex; justify-content: space-between;">
														<view style="display: flex;flex-direction: column;">
															<view class=""
																style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
																{{i18n.order_num}}
																<view style="width: 10rpx;"></view>
																{{orderItem.id}}
															</view>
															<text
																style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{orderItem.transaction_time}}
															</text>
														</view>
														<!-- <text style="color: #0166fc; font-size: 22px;"
															v-if="orderItem.upsdowns>=0">{{orderItem.upsdowns}}</text>
														<text v-if="orderItem.upsdowns<0"
															style="color: #F23C48; font-size: 22px;">{{orderItem.upsdowns}}</text> -->
													</view>
												</view>
											</view>
										</u-cell>
										<!-- <view v-if="orderItem"
											style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
											<image src="../../static/notAvailable.png" style="width:250px; ">
											</image>
											<text style="color: #676767;">
												{{i18n.no}}
											</text>
										</view> -->
									</u-list-item>
									<u-list-item style=" padding-right: 20px;height: 60px;">
									</u-list-item>
								</u-list>
								<!-- 没有数据时 -->
								<view v-if="!indexList1.length"
									style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
									<image src="../../static/notAvailable.png" style="width:250px; "></image>
									<text style="color: #676767;">
										{{i18n.no}}
									</text>
								</view>
							</view>
						</view>
						<view style="height: 15%;"></view>
					</scroll-view>
				</swiper-item>

				<!-- 历史 -->
				<swiper-item>
					<scroll-view scroll-y enable-flex="true" class="section-area" :style="'height:'+scrollH+'px;'"
						@scrolltolower="scrolltolower3()" @scroll="onScroll3" lower-threshold="50" catchtouchmove="true">
						<view style="width: 100%; " ref="ulists1">
							<view>
								<u-list class="sfds" style="width: 100%; ">
									<u-list-item style=" padding-right: 20px;" v-for="(orderItem, index2) in indexList2"
										:key="index2">
										<u-cell>
											<view slot="title" style="display: flex; flex-direction: column;"
												@click.stop="lclicks(orderItem)">
												<view style="display: flex; font-weight: bold;color: #ffffff;"
													v-if="$store.state.bgColor=='black'">
													<text class="u-cell-text">{{orderItem.currency_info.alias}}<span
															v-if="orderItem.type_name=='卖出'"
															class="mcs">{{$t('trade.buy_down')}}</span><span
															v-if="orderItem.type_name=='买入'"
															class="mrs">{{i18n.buy}}</span></text>
													<text class="u-cell-text"
														style="flex: 1; justify-content: center; text-align: left; padding-left: 12px;"><span
															style="font-weight:1; margin-right: 5px; font-size: 12px;">{{i18n.quantity}}</span>{{parseFloat(orderItem.number).toFixed(2)}}</text>
													<text class="u-cell-text" style="color: #0166fc;"
														v-if="orderItem.upsdowns>=0">{{orderItem.upsdowns}}</text>
													<text class="u-cell-text" v-if="orderItem.upsdowns<0"
														style="color: #F23C48;">{{orderItem.upsdowns}}</text>
												</view>

												<view style="display: flex; flex-direction: column;"
													v-if="$store.state.bgColor=='while'">
													<!-- 币名 -->
													<view style="height: 25px; margin-bottom: 10px;">
														<text class="u-cell-text"
															style="font-weight: 500;color: #000;">{{orderItem.currency_info.alias}}
														</text>
													</view>
													<!-- 数据 -->
													<view
														style="display: flex; justify-content: space-between;height: 16px;">
														<view style="display: flex; ">
															<span
																style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">{{parseFloat(orderItem.price)}}→
																{{parseFloat(orderItem.update_price)}}</span>
														</view>
														<view class="">
															<view class="quantity_to_buy"
																v-if="orderItem.type_name=='买入'">
																<view class="left-side"
																	v-if="orderItem.type_name=='买入'">
																	{{$t('trade.buy_up')}}
																</view>
																<view class="right-side">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
															<view class="quantity_to_buy2"
																v-if="orderItem.type_name=='卖出'">
																<view class="left-side2"
																	v-if="orderItem.type_name=='卖出'">
																	{{$t('trade.buy_down')}}
																</view>
																<view class="right-side2">
																	{{i18n.quantity}}{{parseFloat(orderItem.number).toFixed(2)}}
																</view>
															</view>
														</view>
													</view>
													<!-- 时间 -->
													<view style="display: flex; justify-content: space-between;">
														<view style="display: flex;flex-direction: column;">
															<view class=""
																style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
																{{i18n.order_num}}
																<view style="width: 10rpx;"></view>
																{{orderItem.id}}
															</view>
															<text
																style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{orderItem.transaction_time}}
															</text>
														</view>
														<text style="color: #0166fc; font-size: 22px;"
															v-if="orderItem.upsdowns>=0">{{orderItem.upsdowns}}</text>
														<text v-if="orderItem.upsdowns<0"
															style="color: #F23C48; font-size: 22px;">{{orderItem.upsdowns}}</text>
													</view>
												</view>
											</view>
										</u-cell>
									</u-list-item>
									<!-- 加载状态显示 -->
									<u-list-item v-if="loding3" style="padding-right: 20px;height: 60px; display: flex; justify-content: center; align-items: center;">
										<text style="color: #999;">{{loding3}}</text>
									</u-list-item>

									<u-list-item style=" padding-right: 20px;height: 60px;">
									</u-list-item>
									<view v-if="!indexList2.length && !loding3"
										style="display: flex;justify-content: center;align-items: center; flex-direction: column;">
										<image src="../../static/notAvailable.png" style="width:250px; "></image>
										<text style="color: #676767;">
											{{i18n.no}}
										</text>
									</view>
								</u-list>
							</view>
						</view>
						<view style="height: 15%;"></view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>

		<view v-show="currentNav == 1" class="jypzs">
			<view class="scroll-view" style="background-color: #2F3142;" v-if="$store.state.bgColor=='black'">
				<view v-for="(item,index) in cnavsList" :key="index" class="scroll-item"
					:class="tabsIndex===index?'scroll-itembs':'scroll-item'" style="width: 49.5%;"
					@click="mchangesTabs(index)" :id="'tabs'+index">{{item}}</view>
			</view>
			<!-- 顶部选项卡 -->
			<view class="scroll-view" v-if="$store.state.bgColor=='while'">
				<view v-for="(item,index) in cnavsList" :key="index" class="scroll-item"
					:class="tabsIndex===index?'scroll-items':'scroll-item'" style="width: 49.5%;"
					@click="mchangesTabs(index)" :id="'tabs'+index">{{item}}</view>
			</view>
			<view style="height: 10px;"></view>
			<block v-if="currentNav==1">
				<view class="prtxts" v-if="tabsIndex==0"
					style="display: flex; margin: 0 auto; flex-direction: column; justify-content: center; padding: 7px 0; align-items: center; width: 95%; ">
					<text style="margin: 10px 0;">{{i18n.profit_Loss}}</text>
					<text>{{mhyhylist1.yks}}</text>
					<view style="display: flex; width: 100%;margin: 15px 0 15px 0;">
						<!-- 	<text style="flex: 1; text-align: center;">当前保证金：{{hylist.caution_money}}</text>
								<text style="flex: 1; text-align: center;">风险率：{{hylist.fxl}}</text> -->
						<text style="flex: 1; text-align: center;">{{mine.balance}}：{{(users.usdt).toFixed(2)}}</text>
					</view>
				</view>

				<view class="prtxts" v-if="tabsIndex==1"
					style="display: flex;margin: 0 auto; flex-direction: column; justify-content: center; padding: 7px 0; align-items: center; width: 95%; ">
					<text style="margin: 10px 0;">{{i18n.profit_Loss}}</text>
					<text>{{mhyhylist2.yks}}</text>
					<view style="display: flex; width: 100%;margin: 15px 0 15px 0;">

						<text style="flex: 1; text-align: center;">{{mine.balance}}：{{(users.usdt).toFixed(2)}}</text>
					</view>
				</view>
			</block>
			<swiper :duration="150" :current="tabsIndex" @change="monChangeTab"
				:style="'height:'+scrollH+'px;overflow: hidden;width:95%;padding-left:15px'">
				<swiper-item>
					<scroll-view scroll-y enable-flex="true" class="section-area" :style="'height:'+scrollH+'px;'">
						<view style="height: 12px;"></view>
						<view style="width: 100%; " ref="ulists0">
							<view>
								<u-list class="sfds" style="width: 100%; ">
									<u-list-item style=" padding-right: 20px;"
										v-for="(orderItem, index) in mhyIndexList" :key="index">
										<u-cell>
											<view slot="title" style="display: flex; flex-direction: column;"
												@click.stop="mhyclicks(orderItem)">
												<view style="display: flex; font-weight: bold;">
													<text class="u-cell-text">
														{{orderItem.currency_info.alias}}
														<span v-if="orderItem.type_name=='跌' "
															class="mcs">{{$t('trade.buy_down')}}</span>
														<span v-if="orderItem.type_name=='涨'"
															class="mrs">{{$t('trade.buy_up')}}</span>
													</text>
													<text class="u-cell-text"
														style="flex: 1; justify-content: center; text-align: left; padding-left: 12px;">
														<span
															style="font-weight:1; margin-right: 5px; font-size: 12px;">{{i18n.quantity}}</span>
														{{parseFloat(orderItem.number).toFixed(2)}}
													</text>
													<text class="u-cell-text" style="color: #0166fc;"
														v-if="orderItem.profit_result>=0">
														{{orderItem.price}}
													</text>
													<text class="u-cell-text" v-if="orderItem.profit_result<0"
														style="color: #F23C48;">
														-{{orderItem.price}}
													</text>
												</view>
												<view
													style="display: flex;font-size: 12px; color: #747F92; width: 100%;">
													<view style="display: flex; flex: 1;">
														<text class="u-cell-text" style="flex: 1;display: block;">
															{{parseFloat(orderItem.end_price)}}->{{parseFloat(orderItem.update_prices)}}
														</text>
													</view>
													<view>
														<text class="u-cell-text">{{orderItem.seconds}}S </text>
													</view>
												</view>
											</view>
										</u-cell>
									</u-list-item>
									<u-list-item style=" padding-right: 20px;height: 60px;">
									</u-list-item>
								</u-list>

							</view>
						</view>
						<view style="height: 15%;"></view>
					</scroll-view>
				</swiper-item>


				<swiper-item>
					<scroll-view scroll-y enable-flex="true" class="section-area" :style="'height:'+scrollH+'px;'"
						@scrolltolower="scrolltolower4()">
						<view style="height: 12px;"></view>
						<view style="width: 100%; " ref="ulists1">
							<view>
								<u-list class="sfds" style="width: 100%; ">
									<u-list-item style=" padding-right: 20px;"
										v-for="(orderItem, index2) in mhyLIndexList" :key="index2">
										<u-cell>
											<view slot="title" style="display: flex; flex-direction: column;"
												@click.stop="mhyclicks(orderItem)">
												<view style="display: flex; font-weight: bold;">
													<text class="u-cell-text">{{orderItem.currency_info.alias}}<span
															v-if="orderItem.type_name=='跌'"
															class="mcs">{{$t('trade.buy_down')}}</span><span
															v-if="orderItem.type_name=='涨'"
															class="mrs">{{$t('trade.buy_up')}}</span></text>
													<text class="u-cell-text"
														style="flex: 1; justify-content: center; text-align: left; padding-left: 12px;"><span
															style="font-weight:1; margin-right: 5px; font-size: 12px;">{{i18n.quantity}}</span>{{parseFloat(orderItem.number).toFixed(2)}}</text>
													<text class="u-cell-text" style="color: #0166fc;"
														v-if="orderItem.profit_result>=0">{{orderItem.price}}</text>
													<text class="u-cell-text" v-if="orderItem.profit_result<0"
														style="color: #F23C48;">-{{orderItem.price}}</text>

												</view>
												<view
													style="display: flex;font-size: 12px; color: #747F92; width: 100%;">
													<view style="display: flex; width: 100%;">
														<text class="u-cell-text"
															style="flex: 1;display: block;">{{parseFloat(orderItem.end_price)}}->{{parseFloat(orderItem.end_price2)}}</text>
														<text class="u-cell-text" s>{{orderItem.seconds}}S </text>
													</view>
												</view>
											</view>

										</u-cell>
									</u-list-item>
									<u-list-item style=" padding-right: 20px;height: 60px;">
									</u-list-item>
								</u-list>
								<view
									style=" width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #AFAFAF;">
									<text>{{loding4}}</text>
								</view>
							</view>
						</view>

						<view style="height: 15%;"></view>
					</scroll-view>
				</swiper-item>
			</swiper>

		</view>


		<u-popup :show="mhycshow" style="z-index: 1;" :round="30" :duration="350" :closeable="true" @close="mhyclose"
			@open="gopen">

			<view style="height:200px;">
				<view
					style="height: 45px;line-height: 40px; font-size: 16px; font-weight: bold;padding-left: 15px; margin-top: 5px;">
					{{i18n.order_detail}}
				</view>
				<view style="width: 90%; padding: 0px 15px 15px 15px;">
					<view style="display: flex; flex-direction: column; height: 66px;">
						<view style="display: flex; flex-direction: column;">
							<view style="display: flex; font-weight: bold;">
								<text class="u-cell-text">{{items.symbol}}<span v-if="items.type_name=='跌'"
										class="mcs">{{$t('trade.buy_down')}}</span><span v-if="items.type_name=='涨'"
										class="mrs">{{$t('trade.buy_up')}}</span></text>
								<text class="u-cell-text"
									style="flex: 1; justify-content: center; text-align: left; padding-left: 12px;"><span
										style="font-weight:1; margin-right: 5px; font-size: 12px;">{{i18n.quantity}}</span>{{parseFloat(items.number).toFixed(2)}}</text>
								<text class="u-cell-text" style="color: #0166fc;"
									v-if="items.type_name=='涨'">{{items.price}}</text>
								<text class="u-cell-text" v-if="items.type_name=='跌'" </view>
									<view style="display: flex;font-size: 12px; color: #747F92; margin-top: 20px;">
										<text class="u-cell-text"
											style="flex: 1;display: block;">{{parseFloat(items.end_price)}} </text>
										<text class="u-cell-text">{{items.seconds}}S </text>
									</view>
							</view>
						</view>
						<view style="height: 3rpx;background-color: #F5F5F4;"></view>
						<view style="width: 100%; font-size: 12px;color: #747F92;">
							<view style="display: flex; height: 28px; line-height: 28px;"><text
									style="flex: 1;">{{i18n.opening_time}}</text><text>{{items.created_at}}</text>
							</view>
							<view style="display: flex; height: 28px; line-height: 28px;"><text
									style="flex: 1;">{{i18n.closing_time}}</text><text>{{items.complete_at}}</text>
							</view>
						</view>
						<view style="display: flex; height: 28px; line-height: 28px;font-size: 12px;color: #747F92;">
							<text style="flex: 1;">{{i18n.hanling_fee}}</text><text>{{items.fee}}</text>
						</view>
					</view>
				</view>
		</u-popup>

		<u-popup style="z-index: 999;" :show="showTp" mode="center" :round="30" :duration="350" :closeable="true"
			@close="closeTp" @open="openTp">
			<view
				:style="this.$store.state.bgColor=='black'?'height:330px; width: 300px; padding: 6px;background: #22252F; color: #fff;':'height:330px; width: 300px; padding: 6px;' ">
				<view
					style="height: 45px;line-height: 40px; font-size: 16px; font-weight: bold; margin-top: 5px; margin-left: 8px;">
					{{i18n.modify_TP_SL}}
				</view>
				<view class="composings" style="width: 95%; display: flex; flex-direction: column; margin: 8px; ">
					<view style="display: flex; flex: 1; width: 50%; ">
						<text>{{i18n.stop_profit}}</text>
					</view>
					<view style=" width: 100%; margin-top: 20px;">
						<text><u-input inputWidth="100%" :bgColor="this.$store.state.bgColor=='black'?'#22252F':''"
								v-model="target_profit_price" inputtype="digit" iconStyle=""
								style="border: 1px solid #D1D5E8;" :min="0" button-size="70"></u-input></text>
					</view>
				</view>
				<view class="composings"
					style="width: 95%; display: flex; flex-direction: column; margin: 8px; margin-top: 20px; ">
					<view style="display: flex; flex: 1; width: 50%; ">
						<text>{{i18n.stop_loss}}</text>
					</view>
					<view style=" width: 100%; margin-top: 20px;">
						<view><u-input inputWidth="100%" :bgColor="this.$store.state.bgColor=='black'?'#22252F':''"
								inputtype="digit" iconStyle="" v-model="stop_loss_price"
								style="border: 1px solid #D1D5E8; " :min="0" button-size="70"></u-input></view>
					</view>
				</view>
				<button @click.stop="confClicksTp()" style="background: #0166fc;
						border: 0;width:95%; padding: 8px 8px !important; margin-top: 30px;">{{i18n.modify_TP_SL}}</button>

			</view>
		</u-popup>

		<!-- 平仓 -->
		<u-popup :show="show" style="z-index: 1;" :round="30" :duration="350" :closeable="true" @close="close"
			@open="open">
			<view :style="$store.state.bgColor=='black'?'height:50%; background: #22252F; color: #fff;':'height:50%;'">
				<view
					style="height: 45px;line-height: 40px; font-size: 16px; font-weight: bold;padding-left: 15px; margin-top: 5px;">
					{{i18n.order_detail}}
				</view>
				<view style="width: 90%; padding: 0px 15px 10px 15px;">
					<view style="display: flex; flex-direction: column;" v-if="$store.state.bgColor=='while'">
						<view style="height: 25px; margin-bottom: 10px;">
							<text class="u-cell-text" style="font-weight: 500;color: #000;">{{items.symbol}}
							</text>
						</view>
						<!-- 数据 -->
						<view style="display: flex; justify-content: space-between;height: 16px;">
							<view style="display: flex; ">
								<span
									style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">{{parseFloat(items.price)}}→
									{{parseFloat(items.update_price)}}</span>
							</view>
							<view class="">
								<view class="quantity_to_buy" v-if="items.type_name=='买入'">
									<view class="left-side" v-if="items.type_name=='买入'">
										{{$t('trade.buy_up')}}
									</view>
									<view class="right-side">
										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}
									</view>
								</view>
								<view class="quantity_to_buy2" v-if="items.type_name=='卖出'">
									<view class="left-side2" v-if="items.type_name=='卖出'">
										{{$t('trade.buy_down')}}
									</view>
									<view class="right-side2">
										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}
									</view>
								</view>
							</view>
						</view>
						<!-- 时间 -->
						<view style="display: flex; justify-content: space-between;align-items: center;">
							<view style="display: flex;flex-direction: column;">
								<view class=""
									style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
									{{i18n.order_num}}
									<view style="width: 10rpx;"></view>
									{{items.id}}
								</view>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{items.transaction_time}}
								</text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.stop_profit_loss}}:{{parseFloat(items.target_profit_price).toFixed(2)}}
										/
										{{parseFloat(items.stop_loss_price).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.deposit}}:{{parseFloat(items.caution_money).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.hanling_fee}}:{{parseFloat(items.trade_fee).toFixed(2)}}</span></text>
							</view>

							<text style="color: #0166fc; font-size: 24px;"
								v-if="items.upsdowns>=0">{{items.upsdowns}}</text>
							<text v-if="items.upsdowns<0"
								style="color: #F23C48; font-size: 24px;">{{items.upsdowns}}</text>
						</view>
					</view>

					<view style="height: 3rpx;background-color: #F5F5F4;"></view>
					<!-- <view style="width: 100%; font-size: 12px;color: #747F92;">
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.order_num}}</text><text>{{items.id}}</text></view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.stop_profit_loss}}</text><span>{{parseFloat(items.target_profit_price).toFixed(2)}}
								/
								{{parseFloat(items.stop_loss_price).toFixed(2)}}</span></view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.deposit}}</text><text>{{parseFloat(items.caution_money).toFixed(2)}}</text>
						</view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.hanling_fee}}</text><text>{{parseFloat(items.trade_fee).toFixed(2)}}</text>
						</view>
					</view> -->
					<view style="display: flex;align-items: center;justify-content: space-between;">
						<view class="" style="width: 70%;">
							<view class="composings"
								style="width: 100%; display: flex; margin: 8px;align-items: center;">
								<view style="display: flex; flex: 1;">
									<text>{{i18n.stop_profit}}</text>
								</view>
								<view style=" width: 80%; ">
									<u-input inputWidth="100%"
										:bgColor="this.$store.state.bgColor=='black'?'#22252F':''"
										v-model="target_profit_price" inputtype="digit" iconStyle=""
										style="border: 1px solid #D1D5E8;" :min="0" button-size="70"></u-input>
								</view>
							</view>
							<view class="composings"
								style="width:100%; display: flex; margin: 8px;align-items: center;">
								<view style="display: flex; flex: 1;">
									<text>{{i18n.stop_loss}}</text>
								</view>
								<view style=" width: 80%; ">
									<u-input inputWidth="100%"
										:bgColor="this.$store.state.bgColor=='black'?'#22252F':''" inputtype="digit"
										iconStyle="" v-model="stop_loss_price" style="border: 1px solid #D1D5E8; "
										:min="0" button-size="70"></u-input>
								</view>
							</view>
						</view>
						<button @click.stop="confClicksTp()" style="background: #0166fc;
								border: 0;width20%; padding: 8px !important; width: 20%;margin: 0 !important;">{{i18n.modify_TP_SL}}</button>
					</view>

					<view style="width: 100%; ">
						<view style="display: flex; width: 100%;">
							<!-- <button @click.stop="clicksTp()" style="background: #0166fc;
								 border: 0;width:49%; padding: 8px 0px !important;border-radius: 100px;">{{i18n.modify_TP_SL}}</button> -->
							<button @click="showModal"
								style="background: #0166fc;
								 border: 0;width: 100%; padding: 8px 0px !important; margin-left: 13px;border-radius: 100px;">{{i18n.closing_positions}}</button>
						</view>

					</view>
				</view>
			</view>
		</u-popup>


		<u-popup :show="gshow" style="z-index: 6;" :round="30" :duration="350" :closeable="true" @close="gclose"
			@open="gopen">
			<view style="height:50%;">
				<view
					style="height: 45px;line-height: 40px; font-size: 16px; font-weight: bold;padding-left: 15px; margin-top: 5px;">
					{{i18n.order_detail}}
				</view>

				<view style="width: 90%; padding: 0px 15px 15px 15px;">
					<view style="display: flex; flex-direction: column;" v-if="$store.state.bgColor=='while'">
						<!-- 币名 -->
						<view style="height: 25px; margin-bottom: 10px;">
							<text class="u-cell-text" style="font-weight: 500;color: #000;">{{items.symbol}}
							</text>

						</view>
						<!-- 数据 -->
						<view style="display: flex; justify-content: space-between;height: 16px;">
							<view style="display: flex; ">
								<span
									style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">{{parseFloat(items.price)}}→
									{{parseFloat(items.update_price)}}</span>


							</view>
							<view class="">
								<view class="quantity_to_buy" v-if="items.type_name=='买入'">

									<view class="left-side" v-if="items.type_name=='买入'">
										{{$t('trade.buy_up')}}
									</view>

									<view class="right-side">

										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}

									</view>


								</view>
								<view class="quantity_to_buy2" v-if="items.type_name=='卖出'">

									<view class="left-side2" v-if="items.type_name=='卖出'">
										{{$t('trade.buy_down')}}
									</view>
									<view class="right-side2">

										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}

									</view>


								</view>
							</view>

						</view>
						<!-- 时间 -->
						<view style="display: flex; justify-content: space-between;align-items: center;">
							<view style="display: flex;flex-direction: column;">
								<view class=""
									style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
									{{i18n.order_num}}
									<view style="width: 10rpx;"></view>
									{{items.id}}
								</view>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{items.transaction_time}}
								</text>

								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.stop_profit_loss}}:{{parseFloat(items.target_profit_price).toFixed(2)}}
										/
										{{parseFloat(items.stop_loss_price).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.deposit}}:{{parseFloat(items.caution_money).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.hanling_fee}}:{{parseFloat(items.trade_fee).toFixed(2)}}</span></text>
							</view>

							<text style="color: #0166fc; font-size: 24px;"
								v-if="items.upsdowns>=0">{{items.upsdowns}}</text>
							<text v-if="items.upsdowns<0"
								style="color: #F23C48; font-size: 24px;">{{items.upsdowns}}</text>

						</view>

					</view>

					<view style="height: 3rpx;background-color: #F5F5F4;"></view>
					<!-- 	<view style="width: 100%; font-size: 12px;color: #747F92;">
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.order_num}}</text><text>{{items.id}}</text></view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.stop_profit_loss}}</text><text>{{parseFloat(items.target_profit_price).toFixed(2)}}
								/
								{{parseFloat(items.stop_loss_price).toFixed(2)}}</text></view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.deposit}}</text><text>{{parseFloat(items.caution_money).toFixed(2)}}</text>
						</view>
						<view style="display: flex; height: 28px; line-height: 28px;"><text
								style="flex: 1;">{{i18n.hanling_fee}}</text><text>{{parseFloat(items.trade_fee).toFixed(2)}}</text>
						</view>
					</view> -->
					<view style="width: 100%; margin-top: 13px;">
						<view style="display: flex; width: 100%; padding-top: 10px;">
							<button @click.stop="clicksCd()" style="background: #0166fc;
								 border: 0;width:98%; padding: 8px 0px !important;border-radius: 100px;">{{i18n.cancellation}}</button>

						</view>
					</view>
				</view>
			</view>
		</u-popup>



		<u-popup :show="lshow" style="z-index: 6;" :round="30" :duration="350" :closeable="true" @close="lclose"
			@open="lopen">
			<view style="height:50%;">
				<view
					style="height: 45px;line-height: 40px; font-size: 16px; font-weight: bold;padding-left: 15px; margin-top: 5px;">
					{{i18n.order_detail}}
				</view>

				<view style="width: 90%; padding: 0px 15px 10px 15px;">

					<view style="display: flex; flex-direction: column;" v-if="$store.state.bgColor=='while'">
						<!-- 币名 -->
						<view style="height: 25px; margin-bottom: 10px;">
							<text class="u-cell-text" style="font-weight: 500;color: #000;">{{items.symbol}}
							</text>

						</view>
						<!-- 数据 -->
						<view style="display: flex; justify-content: space-between;height: 16px;">
							<view style="display: flex; ">
								<span
									style="font-weight: 500;color: #000; font-size: 12px; height: 18px; line-height: 18px;">{{parseFloat(items.price)}}→
									{{parseFloat(items.update_price)}}</span>


							</view>
							<view class="">
								<view class="quantity_to_buy" v-if="items.type_name=='买入'">

									<view class="left-side" v-if="items.type_name=='买入'">
										{{$t('trade.buy_up')}}
									</view>

									<view class="right-side">

										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}

									</view>


								</view>
								<view class="quantity_to_buy2" v-if="items.type_name=='卖出'">

									<view class="left-side2" v-if="items.type_name=='卖出'">
										{{$t('trade.buy_down')}}
									</view>
									<view class="right-side2">

										{{i18n.quantity}}{{parseFloat(items.number).toFixed(2)}}

									</view>


								</view>
							</view>

						</view>
						<!-- 时间 -->
						<view style="display: flex; justify-content: space-between;align-items: center;">
							<view style="display: flex;flex-direction: column;">
								<view class=""
									style="display: flex;align-items: center;font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">
									{{i18n.order_num}}
									<view style="width: 10rpx;"></view>
									{{items.id}}
								</view>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;">{{items.transaction_time}}
								</text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.stop_profit_loss}}:{{parseFloat(items.target_profit_price).toFixed(2)}}
										/
										{{parseFloat(items.stop_loss_price).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.deposit}}:{{parseFloat(items.caution_money).toFixed(2)}}</span></text>
								<text
									style="font-weight: 500;color: #9f9f9f; font-size: 12px; height: 18px; line-height: 18px;"><span>{{i18n.hanling_fee}}:{{parseFloat(items.trade_fee).toFixed(2)}}</span></text>
							</view>


							<text style="color: #0166fc; font-size: 24px;"
								v-if="items.upsdowns>=0">{{items.upsdowns}}</text>
							<text v-if="items.upsdowns<0"
								style="color: #F23C48; font-size: 24px;">{{items.upsdowns}}</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>



		<u-modal :show="showMess" :style="$store.state.bgColor=='black'?'background: #222;':''" :confirmText="cfmtext"
			:cancelText="celtext" :showCancelButton="true" @confirm="confirm" @cancel="cancel" @close="close"
			confirmColor="#0166fc" :title="titleMess" :content='contentMess'>
		</u-modal>

		<view>
			<!-- 提示窗示例 -->
			<uni-popup ref="alertDialog" type="dialog">
				<uni-popup-dialog :type="msgType" :cancelText="i18n.cancel" :confirmText="i18n.ok" :title="i18n.prompt"
					:content="i18n.confirm_cancellation" @confirm="dialogConfirm"
					@close="dialogClose"></uni-popup-dialog>
			</uni-popup>
		</view>

		<tab-bar v-if="!( show || gshow||lshow)"></tab-bar>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				showMess: false,
				titleMess: this.$t('order.prompt'),
				contentMess: this.$t('order.position_closed'),
				scrollH: 690,
				cfmtext: this.$t('order.ok'),
				celtext: this.$t('order.cancel'),
				scrollInto: "",
				tabIndex: 0,
				tabsIndex: 0,
				msgType: 'success',
				lshow: false,
				scrollHight1: 500,
				scrollHight2: 500,
				scrollHight3: 500,
				navs: [],
				currentNav: 0,
				currentNavcc: 0,
				gshow: false,
				navsList: [],
				cnavsList: [],
				newsList: [],
				indexList: [],
				indexList1: [],
				indexList2: [],
				mhyIndexList: [],
				mhyLIndexList: [],
				mhycshow: false,
				mhyhylist1: {},
				mhyhylist2: {},
				target_profit_price: 0,
				stop_loss_price: 0,
				hylist: {},
				hylist1: {},
				hylist2: {},
				show: false,
				timer: null,
				Datatimer: '',
				showTp: false,
				each_piece: 100,
				items: {},
				page1: 1,
				page2: 1,
				page3: 1,
				page4: 1,
				page5: 1,
				status: false,
				status2: false,
				status3: false,
				status4: false,
				status5: false,
				loding: "",
				loding2: "",
				loding3: "",
				loding4: "",
				loding5: "",
				lastWarningTime: null // 上次风险警告时间
			}
		},
		onPullDownRefresh() {
			this.refresh()
		},
		onShow() {
			getApp().setUser()
			this.$store.commit('changeTabbarIndex', 1)
			this.auths(() => {})
			this.sertNavs()
			this.getData()
			this.SetData()
			this.getMhyData()
			if (this.$store.state.orderNum >= 1) {
				if (this.$store.state.orderNum == 1) {
					this.tabIndex = 1
				} else if (this.$store.state.orderNum == 2) {
					this.currentNav = 1
				}
			}
		},
		tabBarChange() {
			this.$store.commit('changeTabbarIndex', 1)
		},
		onHide() {
			// this.socket.removeListener('daymarket')

			// window.addEventListener("popstate", function(){

			//       uni.switchTab({

			//         url: '/pages/order/order'

			//       });

			//     }, false)
			if (this.Datatimer) {
				clearInterval(this.Datatimer)
				this.Datatimer = ''
			}
			this.$store.commit('setOrNum', -1)
		},
		onUnload() {
			if (this.Datatimer) {
				clearInterval(this.Datatimer)
				this.Datatimer = ''
			}
			this.socket.removeListener('daymarket')
		},
		onLoad() {
			// 根据选项生成列表
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
			// this.getData()
		},
		mounted() {
			this.$nextTick(() => {
				let systemInfo = uni.getSystemInfoSync();
				// this.scrollH = systemInfo.screenHeight+100
				this.scrollH = systemInfo.windowHeight - 200
			})
		},
		watch: {
			currentNav: function(val) {
				if (val == 1) {
					this.getMhyData()
				}
			},
			tabsIndex: function(val) {
				this.getMhyData()
			}
		},
		computed: {
			i18n() {
				return this.$t("order")
			},
			mine() {
				return this.$t("mine")
			},
			...mapState({
				socket: state => state.socket,
				token: state => state.token,
				users: state => state.users
			})
		},
		methods: {
			// 执行完全爆仓处理
			executeFullLiquidation(orders) {
				let completedCount = 0;
				const totalOrders = orders.length;
				
				// 逐个平仓所有订单
				orders.map(item => {
					this.$H.post('/api/v1/lever/close', false, {
						id: item.id
					}).then(res => {
						completedCount++;
						console.log(`订单 ${item.id} 平仓完成`);
						
						// 所有订单平仓完成后
						if (completedCount === totalOrders) {
							uni.showToast({
								title: '爆仓处理完成',
								icon: 'success',
								duration: 2000
							});
							
							// 刷新页面数据
							setTimeout(() => {
								this.refreshAfterLiquidation();
							}, 1000);
						}
					}).catch(err => {
						console.error(`订单 ${item.id} 平仓失败:`, err);
						completedCount++;
						
						if (completedCount === totalOrders) {
							this.refreshAfterLiquidation();
						}
					});
				});
			},
			
			// 爆仓后刷新数据
			refreshAfterLiquidation() {
				// 重新获取用户信息
				getApp().setUser();
				// 刷新订单列表
				this.getData();
				// 清空当前显示的持仓
				this.indexList = [];
				this.hylist = {
					"yks": "0.00",
					"caution_money": "0.00",
					"fxl": "0.00"
				};
			},
			
			// 显示风险警告
			showRiskWarning(netAssets, totalAssets, currentLoss) {
				// 避免频繁弹窗，每60秒最多弹一次
				const now = Date.now();
				if (!this.lastWarningTime || now - this.lastWarningTime > 60000) {
					this.lastWarningTime = now;
					
					const riskPercentage = ((totalAssets - netAssets) / totalAssets * 100).toFixed(1);
					
					uni.showModal({
						title: '⚠️ 高风险警告',
						content: `当前亏损 ${currentLoss.toFixed(2)}，净资产仅剩 ${netAssets.toFixed(2)}（风险度: ${riskPercentage}%）。继续亏损可能导致爆仓，建议及时减仓或止损！`,
						showCancel: true,
						cancelText: '继续持有',
						confirmText: '查看持仓',
						success: (res) => {
							if (res.confirm) {
								// 跳转到持仓页面
								this.tabIndex = 0;
							}
						}
					});
				}
			},
			
			sertNavs() {
				this.navs = []
				this.navsList = []
				this.cnavsList = []
				const {
					i18n
				} = this
				this.navs.push(i18n.contract)
				this.navs.push(i18n.second_contract)

				this.navsList.push(i18n.position)
				this.navsList.push(i18n.listing)
				this.navsList.push(i18n.history)

				this.cnavsList.push(i18n.position)
				this.cnavsList.push(i18n.history)
			},
			refresh() {
				this.getData()
				if (this.currentNav == 1) {
					this.getMhyData()
				}
				setTimeout(() => {
					uni.stopPullDownRefresh()
				}, 3000)
			},
			getMhyData() {
				this.$H.post('/api/v1/MicroOrder/lists', false, {
					status: 1
				}).then(res => {
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						res.message.data.map(item => {
							let date = new Date(Date.parse(new Date(item.created_at)))
							item.created_at = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm:ss')
							let date1 = new Date(Date.parse(new Date(item.updated_at)))
							item.updated_at = this.$u.test.formatDate(date1, 'yyyy-MM-dd hh:mm:ss')
							currency.push({
								"symbol": item.symbol_name,
								"type_name": item.type_name,
								"number": item.number,
								"seconds": item.seconds,
								"price": ((item.profit_ratio / 100) * item.number).toFixed(2),
								"currency_id": item.currency_match.currency_id,
								"fee": parseFloat(item.fee).toFixed(2),
								"end_price": item.open_price,
								"profit_result": item.profit_result,
								"created_at": item.created_at,
								"complete_at": item.complete_at,
								"update_prices": item.end_price
							})
							if (item.type_name == "卖出") {
								yks += parseFloat((item.profit_ratio / 100)) * item.number
							} else {
								yks += (item.profit_ratio / 100) * item.number
							}
						})
						this.mhyhylist1 = {
							"yks": parseFloat(yks).toFixed(2)
						}
						this.mhyIndexList = currency;
					}
				})

				this.$H.post('/api/v1/MicroOrder/lists', false, {
					status: 3
				}).then(res => {
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						res.message.data.map(item => {
							let date = new Date(Date.parse(new Date(item.created_at)))
							item.created_at = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm:ss')
							let date1 = new Date(Date.parse(new Date(item.updated_at)))
							item.updated_at = this.$u.test.formatDate(date1, 'yyyy-MM-dd hh:mm:ss')
							currency.push({
								"symbol": item.symbol_name,
								"type_name": item.type_name,
								"number": item.number,
								"seconds": item.seconds,
								"price": ((item.profit_ratio / 100) * item.number).toFixed(2),
								"currency_id": item.currency_match.currency_id,
								"fee": parseFloat(item.fee).toFixed(2),
								"end_price": item.open_price,
								"end_price2": item.end_price,
								"profit_result": item.profit_result,
								"created_at": item.created_at,
								"complete_at": item.complete_at,
								"update_prices": item.end_price
							})
							if (item.type_name == "卖出") {
								yks += parseFloat('-' + (item.profit_ratio / 100)) * item.number
							} else {
								yks += parseFloat(item.profit_ratio / 100) * item.number
							}
						})
						this.mhyhylist2 = {
							"yks": parseFloat(yks).toFixed(2)
						}
						this.mhyLIndexList = currency;
					}
				})
			},
			confirm() {
				this.showMess = false;
				this.show = false
				const res = this.$H.post('/api/v1/lever/close', false, {
					id: this.items.id
				}).then(res => {
					if (res.type == "ok") {
						uni.$u.toast(this.$t("deposit.completed"));
						location.reload()
						// const has = this.indexList.findIndex(item => item.id == this.items.id)
						// if (has > -1) {
						// 	this.indexList.splice(has, 1)
						// }
					}
				})
			},
			cancel() {
				this.showMess = false;
			},
			close() {
				this.showMess = false;
			},
			showModal() {
				this.showMess = true;

			},
			mhyclicks(item) {
				this.items = item
				this.mhycshow = true
				uni.hideTabBar();
			},
			clicks(item) {
				this.show = true
				this.items = item
				uni.hideTabBar();
			},
			gclicks(item) {
				this.gshow = true
				this.items = item
				uni.hideTabBar();
			},
			lclicks(item) {
				this.lshow = true
				this.items = item
				uni.hideTabBar();
			},
			clicksTp() {
				this.target_profit_price = 0
				this.stop_loss_price = 0
				this.showTp = true
				uni.hideTabBar();
			},
			clicksCd() {
				this.$refs.alertDialog.open()
			},
			open() {

			},
			gopen() {

			},
			lopen() {

			},
			mhyclose() {
				this.mhycshow = false
				this.timer = setTimeout(() => {
					uni.showTabBar()
				}, 10)
			},
			close() {
				this.show = false
				this.timer = setTimeout(() => {
					uni.showTabBar()
				}, 10)
			},
			gclose() {
				this.gshow = false
				this.timer = setTimeout(() => {
					uni.showTabBar()
				}, 10)
			},
			lclose() {
				this.lshow = false
				this.timer = setTimeout(() => {
					uni.showTabBar()
				}, 10)
			},
			openTp() {

			},
			closeTp() {
				this.showTp = false
				// this.timer = setTimeout(() => {
				// 	uni.showTabBar()
				// }, 400)

			},
			autoTruncate(num) {
				// 将数字转换为字符串
				const numStr = num.toString();
				// 分割整数部分和小数部分
				const [integerPart, decimalPart] = numStr.split('.');
				// 如果没有小数部分，直接返回原值
				if (!decimalPart) {
					return num;
				}
				// 找到第一个非零数字的位置（从小数点后开始）
				let significantDigits = 0;
				for (let i = 0; i < decimalPart.length; i++) {
					if (decimalPart[i] !== '0') {
						significantDigits = i + 1; // 保留到第一个非零数字后的下一位
						break;
					}
				}
				// 如果全是 0，则只保留整数部分
				if (significantDigits === 0) {
					return parseFloat(integerPart);
				}
				// 计算需要保留的小数位数
				const decimalPlaces = Math.min(significantDigits, decimalPart.length, 4);
				// 使用四舍五入截取到指定的小数位数
				const factor = Math.pow(10, decimalPlaces);
				return Math.round(num * factor) / factor;
			},
			scrolltolower1() {
				if (this.status) return
				this.loding = "............."
				this.page1 += 1
				this.status = true

				this.$H.post('/api/v1/lever/myTrade', false, {
					status: 1,
					page: this.page1
				}).then(res => {
					this.status = false
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						var caution_money = 0
						var fxl = 0
						if (res.data.message.data.length <= 0) {
							this.status = false
							this.loding = ""
							this.page1 -= 1
							return
						}
						let nums = ''
						if (item.type_name == "卖出") {
							nums = (Number(item.price) - Number(item.now_price)) * item.number * item.multiple
						} else {
							nums = (Number(item.now_price) - Number(item.price)) * item.number * item.multiple
						}
						let upsdowns = this.autoTruncate(nums)
						res.data.message.data.map(item => {
							currency.push({
								"symbol": item.symbol,
								"type_name": item.type_name,
								"number": item.number,
								"price": item.price,
								"update_price": item.update_price,
								"transaction_time": item.transaction_time,
								"upsdowns": upsdowns,
								"currency_id": item.currency,
								"caution_money": item.caution_money,
								"id": item.id,
								"target_profit_price": item.target_profit_price,
								"stop_loss_price": item.stop_loss_price,
								"trade_fee": item.trade_fee,
								"currency_info": item.currency_info
							})
							yks = Number(yks) + Number(upsdowns)
							caution_money += Number(item.caution_money)
							fxl += 0
						})

						if (this.indexList.length > 0) {
							this.indexList = [...this.indexList, ...currency]
							this.hylist.yks = (parseFloat(this.hylist.yks) + parseFloat(yks)).toFixed(2)
							this.hylist.caution_money = (parseFloat(this.hylist.caution_money) + parseFloat(
								caution_money)).toFixed(2)
							this.hylist.fxl = (parseFloat(this.hylist.fxl) + parseFloat(fxl)).toFixed(2)
						} else {
							this.indexList = currency
							this.hylist = {
								"yks": parseFloat(yks).toFixed(2),
								"caution_money": parseFloat(caution_money).toFixed(2),
								"fxl": parseFloat(fxl).toFixed(2)
							}
						}
						// this.indexList=currency;
					}
				})

			},
			scrolltolower2() {
				if (this.status2) return
				this.loding2 = "............."
				this.page2 += 1
				this.status2 = true

				this.$H.post('/api/v1/lever/myTrade', false, {
					status: 0,
					page: this.page2
				}).then(res => {
					this.status2 = false
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						var caution_money = 0
						var fxl = 0
						if (res.data.message.data.length <= 0) {
							this.status2 = false
							this.loding2 = ""
							this.page2 -= 1
							return
						}
						// let upsdowns
						// if (item.type_name == "卖出") {
						// 	upsdowns = (parseFloat(item.price - item.now_price) * item.number *
						// 		item
						// 		.currency_info.each_piece).toFixed(2)
						// } else {
						// 	upsdowns = (parseFloat(item.now_price - item.price) * item.number *
						// 		item
						// 		.currency_info.each_piece).toFixed(2)
						// }
						let nums = ''
						if (item.type_name == "卖出") {
							nums = (Number(item.price) - Number(item.now_price)) * item.number * item.multiple
						} else {
							nums = (Number(item.now_price) - Number(item.price)) * item.number * item.multiple
						}
						let upsdowns = this.autoTruncate(nums)
						res.data.message.data.map(item => {
							currency.push({
								"symbol": item.symbol,
								"type_name": item.type_name,
								"number": item.number,
								"price": item.price,
								"update_price": item.update_price,
								"transaction_time": item.transaction_time,
								"upsdowns": upsdowns,
								"currency_id": item.currency,
								"caution_money": item.caution_money,
								"id": item.id,
								"target_profit_price": item.target_profit_price,
								"stop_loss_price": item.stop_loss_price,
								"trade_fee": item.trade_fee,
								"currency_info": item.currency_info
							})
							// yks += (item.update_price - item.price) * item.number * item.currency_info
							// 	.each_piece
							yks = Number(yks) + Number(upsdowns)
							caution_money += Number(item.caution_money)
							fxl += 0
						})
						if (this.indexList1.length > 0) {
							this.indexList1 = [...this.indexList1, ...currency]
							this.hylist1.yks = (parseFloat(this.hylist1.yks) + parseFloat(yks)).toFixed(2)
							this.hylist1.caution_money = (parseFloat(this.hylist1.caution_money) + parseFloat(
								caution_money)).toFixed(2)
							this.hylist1.fxl = (parseFloat(this.hylist1.fxl) + parseFloat(fxl)).toFixed(2)
						} else {
							this.indexList1 = currency
							this.hylist1 = {
								"yks": parseFloat(yks).toFixed(2),
								"caution_money": parseFloat(caution_money).toFixed(2),
								"fxl": parseFloat(fxl).toFixed(2)
							}
						}
						// this.indexList=currency;
					}
				})

			},
			onScroll3(e) {
				console.log('历史tab滚动事件:', e.detail)
			},
			scrolltolower3() {

				if (this.status3) {
					return
				}
				this.loding3 = "............."
				this.page3 += 1
				this.status3 = true

				this.$H.post('/api/v1/lever/myTrade', false, {
					status: 3,
					page: this.page3
				}).then(res => {
					this.status3 = false
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						var caution_money = 0
						var fxl = 0
						if (res.data.message.data.length <= 0) {
							this.status3 = false
							this.loding3 = ""
							this.page3 -= 1
							return
						}

						res.data.message.data.map(item => {
							// 计算每个订单的盈亏
							let nums = ''
							if (item.type_name == "卖出") {
								nums = (Number(item.price) - Number(item.now_price)) * item.number * item.multiple
							} else {
								nums = (Number(item.now_price) - Number(item.price)) * item.number * item.multiple
							}
							let upsdowns = this.autoTruncate(nums)
							currency.push({
								"symbol": item.symbol,
								"type_name": item.type_name,
								"number": item.number,
								"price": item.price,
								"update_price": item.update_price,
								"transaction_time": item.transaction_time,
								"upsdowns": this.autoTruncate(item.fact_profits),
								"currency_id": item.currency,
								"caution_money": item.caution_money,
								"id": item.id,
								"target_profit_price": item.target_profit_price,
								"stop_loss_price": item.stop_loss_price,
								"trade_fee": item.trade_fee,
								"currency_info": item.currency_info
							})

							yks = Number(item.fact_profits) + Number(yks)
							caution_money += Number(item.caution_money)
							fxl += 0
						})
						// 合并分页数据
						this.indexList2 = [...this.indexList2, ...currency]

						// 累加统计数据
						this.hylist2.yks = (parseFloat(this.hylist2.yks) + parseFloat(yks)).toFixed(2)
						this.hylist2.caution_money = (parseFloat(this.hylist2.caution_money) + parseFloat(caution_money)).toFixed(2)
						this.hylist2.fxl = (parseFloat(this.hylist2.fxl) + parseFloat(fxl)).toFixed(2)

						this.status3 = false
						this.loding3 = ""
					}
				})

			},
			scrolltolower4() {
				if (this.status4) return
				this.loding4 = "............."
				this.page5 += 1
				this.status4 = true
				this.$H.post('/api/v1/MicroOrder/lists', false, {
					status: 3,
					page: this.page5
				}).then(res => {
					if (res.type == "ok") {
						var currency = []
						var yks = 0
						if (res.message.data.length <= 0) {
							this.status4 = false
							this.loding4 = ""
							this.page5 -= 1
							return
						}
						res.message.data.map(item => {
							let date = new Date(Date.parse(new Date(item.created_at)))
							item.created_at = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm:ss')
							let date1 = new Date(Date.parse(new Date(item.updated_at)))
							item.updated_at = this.$u.test.formatDate(date1, 'yyyy-MM-dd hh:mm:ss')
							currency.push({
								"symbol": item.symbol_name,
								"type_name": item.type_name,
								"number": item.number,
								"seconds": item.seconds,
								"price": ((item.profit_ratio / 100) * item.number).toFixed(2),
								"currency_id": item.currency_id,
								"open_price": item.open_price,
								"created_at": item.created_at,
								"complete_at": item.complete_at
							})

							if (item.type_name == "卖出") {
								yks += parseFloat('-' + (item.profit_ratio / 100)) * item.number
							} else {
								yks += parseFloat(item.profit_ratio / 100) * item.number
							}
						})


						if (this.mhyLIndexList.length > 0) {
							this.mhyLIndexList = [...this.mhyLIndexList, ...currency]
							this.mhyhylist2.yks = (parseFloat(this.mhyhylist2.yks) + parseFloat(yks)).toFixed(2)
						} else {
							this.mhyhylist2 = {
								"yks": parseFloat(yks).toFixed(2)
							}
							this.mhyLIndexList = currency;
						}
						this.status4 = false
					}
				})
			},
			SetData() {
				if (this.Datatimer) {
					clearInterval(this.Datatimer)
					this.Datatimer = ''
				}
				this.Datatimer = setInterval(() => {
					this.getData()
				}, 2000)
			},
			getData(type) {
				// 如果是history tab且已有分页数据，跳过重新加载（避免覆盖分页数据）
				if (this.tabIndex == 2 && this.page3 > 1) {
					console.log('跳过history tab的定时刷新，避免覆盖分页数据')
					return
				}

				if (this.tabIndex == 0) {
					this.$H.post('/api/v1/lever/myTrade', false, {
						status: 1
					}).then(res => {
						if (res.type == "ok") {
							var currency = []
							var yks = 0
							var caution_money = 0
							var fxl = 0
							res.data.message.data.map(item => {
								this.each_piece = item.currency_info.each_piece
								// let upsdowns
								// if (item.type_name == "卖出") {
								// 	upsdowns = (parseFloat(item.price - item.now_price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(2)
								// } else {
								// 	upsdowns = (parseFloat(item.now_price - item.price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(2)
								// }
								let nums = ''
								if (item.type_name == "卖出") {
									nums = (Number(item.price) - Number(item.now_price)) * item.number *
										item.multiple
								} else {
									nums = (Number(item.now_price) - Number(item.price)) * item.number *
										item.multiple
								}
								let upsdowns = this.autoTruncate(nums)
								let query = {
									"symbol": item.symbol,
									"type_name": item.type_name,
									"number": item.number,
									"price": item.price,
									"update_price": item.update_price,
									"transaction_time": item.transaction_time,
									"upsdowns": upsdowns,
									"currency_id": item.currency,
									"caution_money": item.caution_money,
									"id": item.id,
									"target_profit_price": item.target_profit_price,
									"stop_loss_price": item.stop_loss_price,
									"trade_fee": item.trade_fee,
									"currency_info": item.currency_info
								}
								currency.push(query)
								// yks += (item.update_price - item.price) * item.number * item.currency_info
								// 	.each_piece
								yks = Number(yks) + Number(upsdowns)
								caution_money += Number(item.caution_money)
								fxl += 0
								if (this.items && this.items.id == item.id) {
									this.items = query
								}
							})
							// 修正爆仓逻辑：只有在亏损且净资产不足以维持保证金时才触发爆仓
							// 净资产 = 账户余额 + 当前盈亏
							const netAssets = Number(this.users.usdt) + Number(yks);
							const totalAssets = Number(this.users.usdt) + Number(caution_money);
							
							// 风险预警：当净资产小于总资产的20%时发出警告
							// if (Number(yks) < 0 && netAssets > 0 && netAssets < totalAssets * 0.2) {
							// 	this.showRiskWarning(netAssets, totalAssets, Math.abs(Number(yks)));
							// }
							
							// 爆仓条件：
							// 1. 当前为亏损状态 (yks < 0)
							// 2. 净资产小于等于0 (完全爆仓，余额+保证金全部清零)
							if (Number(yks) < 0 && netAssets <= 0) {
								console.log('触发完全爆仓条件（余额+保证金将全部清零）：', {
									yks: Number(yks),
									usdt: Number(this.users.usdt),
									netAssets: netAssets,
									caution_money: Number(caution_money),
									totalLoss: Math.abs(Number(yks)),
									totalAssets: Number(this.users.usdt) + Number(caution_money)
								});
								
								// // 显示爆仓提示
								// uni.showModal({
								// 	title: '爆仓提醒',
								// 	content: `亏损已超过总资产，系统将强制平仓并清零账户余额。亏损: ${Math.abs(Number(yks)).toFixed(2)}, 总资产: ${(Number(this.users.usdt) + Number(caution_money)).toFixed(2)}`,
								// 	showCancel: false,
								// 	confirmText: '确认',
								// 	success: () => {
								// 		// 用户确认后再执行平仓
								// 		this.executeFullLiquidation(res.data.message.data);
								// 	}
								// });
								
								// 立即更新前端显示的用户余额（预期爆仓后的状态）
								this.$store.commit('updateUserBalance', {
									usdt: 0, // 余额将被清零
									lever_balance: 0 // 保证金将被清零
								});
							}
							this.hylist = {
								"yks": parseFloat(yks).toFixed(2),
								"caution_money": parseFloat(caution_money).toFixed(2),
								"fxl": parseFloat(fxl).toFixed(2)
							}
							this.indexList = currency;
							// this.startSocket()
						}
					})
				} else if (this.tabIndex == 1) {
					this.$H.post('/api/v1/lever/myTrade', false, {
						status: 0
					}).then(res => {
						if (res.type == "ok") {
							var currency = []
							var yks = 0
							var caution_money = 0
							var fxl = 0
							res.data.message.data.map(item => {
								// this.each_piece = item.currency_info.each_piece
								// if (item.type_name == "卖出") {
								// 	upsdowns = (parseFloat(item.price - item.now_price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(
								// 		2)
								// } else {
								// 	upsdowns = (parseFloat(item.now_price - item.price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(
								// 		2)
								// }
								let nums = ''
								if (item.type_name == "卖出") {
									nums = (Number(item.price) - Number(item.now_price)) * item.number *
										item.multiple
								} else {
									nums = (Number(item.now_price) - Number(item.price)) * item.number *
										item.multiple
								}
								let upsdowns = this.autoTruncate(nums)
								currency.push({
									"symbol": item.symbol,
									"type_name": item.type_name,
									"number": item.number,
									"price": item.price,
									"update_price": item.update_price,
									"transaction_time": item.transaction_time,
									"upsdowns": upsdowns,
									"currency_id": item.currency,
									"caution_money": item.caution_money,
									"id": item.id,
									"target_profit_price": item.target_profit_price,
									"stop_loss_price": item.stop_loss_price,
									"trade_fee": item.trade_fee,
									"currency_info": item.currency_info
								})
								// yks += (item.update_price - item.price) * item.number * item.currency_info
								// 	.each_piece
								yks = Number(yks) + Number(upsdowns)
								caution_money = caution_money + Number(item.caution_money)
								fxl += 0

							})
							this.hylist1 = {
								"yks": parseFloat(yks).toFixed(2),
								"caution_money": caution_money,
								"fxl": parseFloat(fxl).toFixed(2)
							}
							this.indexList1 = currency;
						}
					})
				} else {
					this.$H.post('/api/v1/lever/myTrade', false, {
						status: 3,
						page: 1
					}).then(res => {
						if (res.type == "ok") {
							var currency = []
							var yks = 0
							var caution_money = 0
							var fxl = 0
							res.data.message.data.map(item => {
								// let upsdowns
								// if (item.type_name == "卖出") {
								// 	upsdowns = (parseFloat(item.price - item.now_price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(2)
								// } else {
								// 	upsdowns = (parseFloat(item.now_price - item.price) * item.number *
								// 		item
								// 		.currency_info.each_piece).toFixed(2)
								// }
								currency.push({
									"symbol": item.symbol,
									"type_name": item.type_name,
									"number": item.number,
									"price": item.price,
									"update_price": item.update_price,
									"transaction_time": item.transaction_time,
									"upsdowns": this.autoTruncate(item.fact_profits),
									"currency_id": item.currency,
									"caution_money": item.caution_money,
									"id": item.id,
									"target_profit_price": item.target_profit_price,
									"stop_loss_price": item.stop_loss_price,
									"trade_fee": item.trade_fee,
									"currency_info": item.currency_info
								})
								// yks += (item.update_price - item.price) * item.number * item.currency_info
								// 	.each_piece
								yks = Number(item.fact_profits) + Number(yks)
								caution_money += Number(item.caution_money)
								fxl += 0
							})

							this.hylist2 = {
								"yks": parseFloat(yks).toFixed(2),
								"caution_money": parseFloat(caution_money).toFixed(2),
								"fxl": parseFloat(fxl).toFixed(2)
							}
							this.indexList2 = currency;
						}
					})
				}
			},
			startSocket() {
				this.socket.on('daymarket', res => {
					this.onhylist(res)
					this.onhylist1(res)
					this.onhyMhylist1(res)
				})
			},
			onhylist(res) {
				const _this = this
				var yks = 0
				var caution_money = 0
				var fxl = 0
				this.indexList.map(item => {
					if (item.currency_info.id == res.currency_id) {
						// item.update_price = res.now_price
						// if (item.type_name == "卖出") {
						// 	item.upsdowns = (parseFloat(item.price - res.now_price) * item.number * item
						// 		.currency_info.each_piece).toFixed(2)
						// } else {
						// 	item.upsdowns = (parseFloat(res.now_price - item.price) * item.number * item
						// 		.currency_info.each_piece).toFixed(2)
						// }
						// item.upsdowns = ((Number(res.now_price) - Number(item.price)) * item.number * item.multiple).toFixed(4)
					}
					// if (item.type_name == "卖出") {
					// 	yks += (item.price - item.update_price) * item.number * item.currency_info.each_piece
					// } else {
					// 	yks += (item.update_price - item.price) * item.number * item.currency_info.each_piece
					// }
					yks = Number(yks) + Number(item.upsdowns)
					caution_money += Number(item.caution_money)
					fxl += 0
				})

				this.hylist = {
					// "yks": parseFloat(yks).toFixed(2),
					"yks": this.hylist.yks,
					"caution_money": parseFloat(caution_money).toFixed(2),
					"fxl": parseFloat(fxl).toFixed(2)
				}
			},
			onhyMhylist1(res) {
				this.mhyIndexList.map(item => {
					if (item.currency_id.id == res.currency_id) {
						item.update_prices = res.now_price
					}
				})
			},

			onhylist1(res) {
				const _this = this
				var yks = 0
				var caution_money = 0
				var fxl = 0
				this.indexList1.map(item => {
					if (item.currency_id.id == res.currency_id) {
						item.update_price = res.now_price
						// if (item.type_name == "卖出") {
						// 	item.upsdowns = (parseFloat(item.price - res.now_price) * item.number * item
						// 		.currency_info.each_piece).toFixed(2)
						// } else {
						// 	item.upsdowns = (parseFloat(res.now_price - item.price) * item.number * item
						// 		.currency_info.each_piece).toFixed(2)
						// }
						let nums = ''
						if (item.type_name == "卖出") {
							nums = (Number(item.price) - Number(item.now_price)) * item.number * item.multiple
						} else {
							nums = (Number(item.now_price) - Number(item.price)) * item.number * item.multiple
						}
						item.upsdowns = this.autoTruncate(nums)
					}
					// if (item.type_name == "卖出") {
					// 	yks += (item.price - item.update_price) * item.number * item.currency_info.each_piece
					// } else {
					// 	yks += (item.update_price - item.price) * item.number * item.currency_info.each_piece
					// }
					yks = Number(yks) + Number(item.upsdowns)
					caution_money += Number(item.caution_money)
					fxl += 0
				})

				this.hylist1 = {
					"yks": parseFloat(yks).toFixed(2),
					"caution_money": parseFloat(caution_money).toFixed(2),
					"fxl": parseFloat(fxl).toFixed(2)
				}

			},
			confClicksTp() {
				const res = this.$H.post('/api/v1/lever/setStopPrice', false, {
					id: this.items.id,
					target_profit_price: this.target_profit_price,
					stop_loss_price: this.stop_loss_price
				}).then(res => {
					this.loading = false
					if (res.type == "ok") {
						uni.$u.toast(this.$t("deposit.completed"));
						this.items.target_profit_price = this.target_profit_price
						this.items.stop_loss_price = this.stop_loss_price
						const has = this.indexList.findIndex(item => item.id == this.items.id)

						if (has > -1) {
							const item = {
								...this.indexList[has],
								...this.items
							}
							this.indexList.splice(has, 1, item)

						}
						this.showTp = false

					}
				})

			},
			onChangeTab(e) {
				this.changesTabs(e.detail.current)
			},
			monChangeTab(e) {


				this.mchangesTabs(e.detail.current)
			},
			dialogClose() {
				this.$refs.alertDialog.close()
			},
			dialogConfirm() {
				const res = this.$H.post('/api/v1/lever/cancelTrade', false, {
					id: this.items.id
				}).then(res => {
					this.loading = false
					if (res.type == "ok") {
						uni.$u.toast(this.$t("deposit.completed"));
						const has = this.indexList1.findIndex(item => item.id == this.items.id)
						if (has > -1) {
							this.indexList1.splice(has, 1)
						}
						this.$refs.alertDialog.close()
						this.gclose()
					}
				})

			},
			sectionChange(index) {
				this.currentNav = index;
			},
			changesTabs(id) {
				if (this.tabIndex === id) {
					return;
				}
				this.tabIndex = id;
				this.scrollInto = 'tab' + id;

				// 切换到历史tab时重置分页状态
				if (id === 2) {
					this.page3 = 1;
					this.status3 = false;
					this.loding3 = "";
					this.indexList2 = [];
					this.hylist2 = {
						"yks": "0.00",
						"caution_money": "0.00",
						"fxl": "0.00"
					};
				}

				this.getData()
				this.getMhyData()
			},
			mchangesTabs(id) {
				if (this.tabsIndex === id) {
					return;
				}
				this.tabsIndex = id;
				this.scrollInto = 'tabs' + id;
			}
		}
	}
</script>
<style>
	.dhbts {
		display: inline-block;
		background: #fff;
		margin: 5px;
		width: 80px;
		height: 30px;
		line-height: 30px;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
		font-weight: 700 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
		border-radius: 8vw !important;
		text-align: center;
	}

	.cdhbts {
		display: inline-block;
		background: #0166fc;
		margin: 5px;
		width: 80px;
		height: 30px;
		line-height: 30px;
		--tw-bg-opacity: 1 !important;

		font-weight: 700 !important;
		--tw-text-opacity: 1 !important;
		color: #ffffff;
		border-radius: 8vw !important;
		text-align: center;
	}

	/deep/.u-cell__title {
		flex: 1;
		line-height: 58px;
	}

	.blacks /deep/.u-popup__content {
		background: #222;
		color: #fff;
	}

	.whiles /deep/.u-popup__content {}

	.blacks /deep/.u-modal__title {
		color: #fff;
	}

	.blacks /deep/.u-modal__content__text {
		color: #fff;
	}

	/deep/.u-subsection--button__bar[data-v-244377f2] {

		background-color: inherit !important;

	}

	.blacks /deep/.u-input__plus--hover {
		background: #22252F !important;
	}

	.blacks /deep/.u-input__plus {
		background: #22252F !important;
		color: #fff;
	}

	.blacks /deep/.u-icon__icon {
		color: #fff !important;
	}

	.blacks /deep/.u-input__minus {

		background: #22252F !important;
	}


	.whiles /deep/.u-input__plus--hover {}

	.whiles /deep/.u-input__plus {}

	.whiles /deep/.u-icon__icon {}

	.whiles /deep/.u-input__minus {}



	.u-fade-enter-active,
	.u-fade-leave-active {

		width: 25%;
	}

	/deep/.u-image {

		/* padding-left: 15px; */
	}

	/deep/.u-subsection--button {
		background-color: inherit !important;
		height: 50px;
		margin-left: -5px;

	}

	/deep/.uni-button-color {
		color: #007aff !important;
	}

	/deep/.uni-icons {

		width: 20%;
		text-align: right;
		padding-right: 20px;
	}

	.blacks /deep/.uni-popup-dialog {
		background-color: #22252F;
	}

	.blacks /deep/.uni-dialog-button-text {
		color: #fff;
	}

	.blacks /deep/.u-cell__body__content {
		color: #fff;
	}

	.positive {
		color: #0166fc;
	}

	.negative {
		color: #fc0004;
	}

	/deep/.u-subsection__item__text {

		line-height: 30px !important;
		display: flex;
		flex-direction: row;
		align-items: center;
		transition-property: color;
		transition-duration: 0.3s;
		font-size: 15px !important;
		width: 90px;
		text-align: center;
		justify-content: center;
	}

	/deep/ .u-subsection--button__bar {
		background-color: inherit !important;
		border-bottom: 2px solid #0166fc;
		/* width: 85px !important; */
		height: 45px !important;
		border-radius: 0px !important;
	}

	.scroll-view {
		width: 95% !important;
		height: 100rpx;
		line-height: 100rpx;
		white-space: nowrap;
		overflow: hidden;
		background: #ffffff;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 6px;
		margin: 0 auto;
	}

	uni-button {
		line-height: 30px !important;
		color: #FFFFFF !important;
		font-size: 14px !important;
	}

	.scroll-view .scroll-items {
		display: inline-block;
		width: 33.3%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #0166fc;
		border-radius: 40rpx !important;
		color: #ffffff !important;

	}

	.scroll-view .scroll-itembs {
		display: inline-block;
		width: 33.3%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #1A1B24;
		border-radius: 40rpx;
		color: #ffffff !important;
	}

	.scroll-view .scroll-itemb {
		display: inline-block;
		width: 33.3%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;

	}

	.scroll-view .scroll-item {
		display: inline-block;
		width: 33.3%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;
		color: #828397;
	}

	.section-area ::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;

	}

	.prtxts {
		/* background: url(../../static/<EMAIL>); */
		/* background-size: cover; */
		--tw-text-opacity: 1 !important;
		color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
		--tw-bg-opacity: 1 !important;

		border-radius: 10px;
	}

	.mcs {
		border: 1px solid rgb(248, 160, 166);
		color: rgb(239, 38, 61);
		padding: 1px 7px;
		border-radius: 10%;
		font-size: 12px;
		cursor: pointer;
		margin-left: 5px;
		margin-right: 5px;
	}

	.mrs {
		border: 1px solid rgb(1, 102, 252);
		color: rgb(1, 102, 252);
		padding: 1px 7px;
		border-radius: 10%;
		font-size: 12px;
		cursor: pointer;
		margin-left: 5px;
		margin-right: 5px;
	}

	.mrsDetal {
		border: 1px solid rgb(1, 102, 252);
		color: rgb(1, 102, 252);
		padding: 1px 8px;
		border-radius: 10%;
		font-size: 10px;
		cursor: pointer;
		margin-left: 10px;
		margin-right: 5px;
	}

	/deep/.uicon-close {
		font-size: 19px !important;
		top: 5px !important;
	}

	/deep/.u-cell__title {
		flex: 1;
		line-height: 36px;
	}

	/* 	/deep/.uni-scroll-view,
			.uni-scroll-view-content {
				overflow: hidden !important;
			} */

	/deep/.u-list {
		height: 100% !important;
	}

	uni-page-wrapper {
		overflow: hidden;
	}

	.quantity_to_buy {
		display: flex;
		align-items: center;
		border: 1px solid #0166fc;
		justify-content: space-between;
		/* width: 150px; 设置药丸的总宽度 */
		height: 14px;
		border-radius: 5px !important;
		/* 较大的border-radius值使得角更圆 */
		overflow: hidden;
		/* 隐藏超出边界的元素 */
	}


	.left-side {
		/* width: 50%; */
		display: block;
		/* 左侧占据一半的宽度 */
		height: 100%;
		background-color: #0166fc;
		/* 左侧背景颜色 */
		color: #ffffff;
		/* 左侧文字颜色 */
		text-align: center;
		font-size: 12px;
		font-weight: 300;
		padding: 0px 6px;
		/* 文字居中 */
		line-height: 15px;
		;
		/* 文字垂直居中 */
	}

	.right-side {
		display: block;
		/* width: 50%; */
		/* 右侧占据另一半的宽度 */
		height: 100%;
		;
		/* background-color: #4caf50; */
		/* 右侧背景颜色 */
		color: #0061e7;
		/* 右侧文字颜色 */
		text-align: center;
		font-size: 12px;
		font-weight: 500;
		padding: 1px 6px;
		/* 文字居中 */
		line-height: 15px;
		;
		/* 文字垂直居中 */
	}

	.quantity_to_buy2 {
		display: flex;
		align-items: center;
		border: 1px solid #f00004;
		justify-content: space-between;
		/* width: 150px; 设置药丸的总宽度 */
		height: 14px;
		border-radius: 5px !important;
		/* 较大的border-radius值使得角更圆 */
		overflow: hidden;
		/* 隐藏超出边界的元素 */
	}


	.left-side2 {
		/* width: 50%; */
		display: block;
		/* 左侧占据一半的宽度 */
		height: 100%;
		background-color: #f00004;
		/* 左侧背景颜色 */
		color: #ffffff;
		/* 左侧文字颜色 */
		text-align: center;
		font-size: 12px;
		font-weight: 300;
		padding: 0px 6px;
		/* 文字居中 */
		line-height: 15px;
		;
		/* 文字垂直居中 */
	}

	.right-side2 {
		display: block;
		/* width: 50%; */
		/* 右侧占据另一半的宽度 */
		height: 100%;
		;
		/* background-color: #4caf50; */
		/* 右侧背景颜色 */
		color: #f00004;
		/* 右侧文字颜色 */
		text-align: center;
		font-size: 12px;
		font-weight: 500;
		padding: 1px 6px;
		/* 文字居中 */
		line-height: 15px;
		;
		/* 文字垂直居中 */
	}

	/deep/.u-fade-zoom-enter-active,
	.u-fade-zoom-leave-active {
		z-index: 111 !important;
		background-color: rgba(0, 0, 0, 0.5);
	}

	/deep/.uni-input-input {
		background: #ffffff;
	}


	.blacks /deep/.uni-input-input {
		background: #1A1C24;
		color: #fff;
	}

	/deep/.u-fade-enter-active,
	.u-fade-leave-active {

		z-index: 10 !important;
	}

	/deep/.u-slide-up-enter-active {
		z-index: 10 !important;
	}

	/deep/.u-modal__title {
		text-align: inherit;
		margin-left: 25px !important;
	}

	/deep/.u-modal__content[data-v-713d0fd3] {
		text-align: center;
	}

	/deep/.uni-popup .uni-popup__wrapper {
		z-index: 999;
	}

	/deep/.u-subsection--button {
		height: 50px;
		background-color: inherit !important;

	}

	/deep/.u-subsection__item__text {
		font-size: 15px !important;
	}

	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
</style>
