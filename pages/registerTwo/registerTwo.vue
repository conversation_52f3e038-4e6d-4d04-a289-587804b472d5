<template>
	<view>
		<view>
			<view
				style="width:93%;display: flex; padding-top: 10px; padding-left: 10px; justify-content: center;align-items: center;">
				<view style="flex: 1;" @click="backs()">
					<image src="../../static/<EMAIL>" style="width: 50rpx; height: 50rpx;"></image>
				</view>
				<view style="width: 124px;
height: 35px;
background: #F5F5F5;
border-radius: 388px 388px 388px 388px;
opacity: 1; width: 40px; padding: 2px 0px; ; display: flex; justify-content: center; align-items: center;"
					@click="showModal" data-target="viewModal">
					<view>
						<image :src="clangs.img" style="width:25px; height: 20px;"></image>
					</view>
					<!-- <view style="height: 20px; margin: 0 5px; line-height: 20px; font-size: 14px;">
							<text>{{clangs.name}}</text>
						</view> -->
					<!-- <view>
							<image src="../../static/<EMAIL>" style="width:15px; height: 13px;"></image>
						</view> -->
				</view>
			</view>
		</view>
		<view style="width: 225px; margin: 0 auto; padding: 10rpx 0 25rpx 0; text-align: center;">
			<view>
				<image src="../../static/logo.png" style="width:300px; height: 80px;"></image>
			</view>
			<!-- <text style="font-size: 18px; font-weight: bold;">{{i18n.welcome_register}}</text> -->
		</view>
		<uni-forms :modelValue="formData">

			<view style="height: 430px;

		opacity: 1; width: 90%; margin: 0 auto; ">
				<view class="wrap">
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">
							<text class="wrap_text" style="color: #0166fc;">{{i18n.enter_email}}</text>
							<u--input :placeholder="i18n.enter_email" class="inps " style="background: #FFFFFF;"
								v-model="usestring" type="text"></u--input>
						</view>

					</view>
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">
							<text class="wrap_text">{{i18n.pwd}}</text>
							<uni-easyinput class="uni-mt-5" :type="types" :passwordIcon="false" :suffixIcon="suffixIcon"
								v-model="passwords" :placeholder="i18n.enter_password"
								@iconClick="suffix"></uni-easyinput>
						</view>

					</view>
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">

							<text class="wrap_text">{{i18n.enter_password_again}}</text>
							<uni-easyinput class="uni-mt-5 " :type="confirmtypes" :passwordIcon="false"
								:suffixIcon="confirmsuffixIcon" v-model="confirmpasswords"
								:placeholder="i18n.enter_password_again" @iconClick="confirmsuffix"></uni-easyinput>

							<!-- <u-input class="inps"  :type="types"
			                            placeholder="请再次输入密码"
										@iconClick="suffix()"
			                            v-model="formData.password"
			                            :suffixIcon="suffixIcon"
			                            :password='passwordShow'

			                        ></u-input> -->
						</view>

					</view>
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">
							<text class="wrap_text">{{i18n.enter_invite_code}}</text>
							<u--input :placeholder="i18n.enter_invite_code" v-model="invitecode" disabled="disabled"
								class="inps" type="text"></u--input>
						</view>

					</view>
				</view>

				<view class="wrap">

					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-bottom: 15px;">




					</view>

					<!-- #ifndef APP-NVUE -->
					<text class="wrap_text">{{i18n.enter_vercode}}</text>
					<u-input class="inps" v-model="code" style=" padding: 10px !important; background: #FFFFFF;"
						:placeholder="i18n.enter_vercode">
					<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						<u--input class="inps" v-model="code" style="padding: 10px !important; background: #FFFFFF;"
							:placeholder="i18n.enter_vercode">
						<!-- #endif -->
							<template slot="suffix">
								<u-code ref="uCode" @change="codeChange" seconds="60"
									:changeText="'X'+i18n.get_ver_code"></u-code>
								<u-button class="my_u_button" @tap="getCode" :text="tips"
									size="mini">{{i18n.get_ver_code}}</u-button>

							</template>
					<!-- #ifndef APP-NVUE -->
					</u-input>
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					</u--input>
					<!-- #endif -->
					<view style=" display: flex; justify-content: center; align-items: center;">
						<view style="  text-align: center; margin-right: 2px;">







						</view>
						<!-- 	<view style="display: flex;justify-content: left; align-items: center; margin-top: 15px;">
							<view style="height: 16px;line-height: 16px">
								<u-checkbox-group shape="circle" iconSize="1" v-model="checkboxValue1"
									placement="column" @change="checkboxChange">
									<u-checkbox size="35" iconSize="1" :customStyle="{marginBottom: '8px'}"
										:name="xyname">
									</u-checkbox>
								</u-checkbox-group>
							</view>
							<view style="flex: 1; justify-content: left;">&nbsp;<span
									style="color: #9B9EAF;">{{i18n.read_agress}}</span><span
									@click="ystz(0)">《{{i18n.user_service}}》</span><text
									style="color: #9B9EAF;">{{i18n.and}}</text><span
									@click="ystz(1)">《{{i18n.privacy}}》</span></view>

						</view> -->

					</view>
					<view style="width:100%;margin-top: 20px;">
						<u-button :loading="loading" loadingText="" @click="obuttons()" loadingSize="30" style="background: #3F47F4; margin-top: 5vh;
														border-radius: 45px;		    border: 0;   font-size: 14px;  height: 45px; color: #FFFFFF;
						line-height: 55px !important;">{{i18n.register}}</u-button>
						<!-- 	<view style="width: 225px; margin: 0 auto; margin-top: 15px; text-align: center;"
							@click="backs()">
							<text>{{$t("reset.have_account")}}</text>
							<text style="color: #3F47F4;font-size: 12px; font-weight: bold; padding-left: 3px;">
								{{$t("reset.sign_in")}}</text>
						</view> -->

					</view>

				</view>
			</view>


		</uni-forms>


		<view style="height: 25px;"></view>
		</scroll-view>
		<view class="DrawerClose" :class="modalName=='viewModal'?'show':''" @tap="hideModal">
			<text class="cuIcon-pullright"></text>
		</view>




	</view>
</template>

<script>
	import {
		langs
	} from "@/common/language.js"
	export default {
		data() {
			return {
				formData: {
					"password": ""
				},
				types: "password",
				confirmtypes: "password",
				usestring: '',
				invitecode: "",
				code: "",
				getcode: null,

				loading: false,
				tips: '',
				seconds: "",
				// loading:false,
				values: null,
				value: null,
				passwordShow: true,
				confirmpasswords: "",
				passwords: "",
				confirmpasswordShow: true,
				xyname: null,
				language: [],
				checkboxValue1: [],
				modalName: null,
				clangs: {}

			}
		},
		onLoad(e) {
			if (e) {
				this.invitecode = e.invite_code
			}
			this.language = langs
			this.setLoads()

		},
		onShow() {
			this.setLgs()
		},
		methods: {
			ystz(id) {
				if (id) {
					let urls = "/pages/articles/articles?item=" + encodeURIComponent(JSON.stringify(id))
					if (urls.indexOf('%') > -1) {
						urls = urls.replace(/%/g, '%25');
					}

					uni.navigateTo({
						url: urls
					})
				} else {
					let urls = "/pages/articles/articles?item=" + encodeURIComponent(JSON.stringify(id))
					if (urls.indexOf('%') > -1) {
						urls = urls.replace(/%/g, '%25');
					}

					uni.navigateTo({
						url: urls
					})
				}
			},
			obutton() {
				this.loading = true
			},
			backs() {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			},
			setLoads() {
				let lgs = uni.getStorageSync('lang') || 'en'
				this.language.map(el => {
					el.selected = false
					if (el.value == lgs) el.selected = true
					return el
				})
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})

				console.log(langs)
				this.language = langs
				this._i18n.locale = item.value
				console.log(item.value)
				uni.setStorageSync('lang', item.value)

				this.$utils.setTabbar(this)

				this.clangs = item
				this.hideModal()
				document.querySelector('.u-button__text').innerText = this.$t("reset.get_ver_code");
				// this.$store.commit('setLang', item.value)

				// setTimeout(() => {
				// 	this.showLanguage = false
				// }, 200)
			},
			isShow() {
				this.types = this.types === "password" ? "text" : "password"
			},
			checkboxChange() {

			},
			obuttons() {

				let {
					usestring,
					code,
					invitecode,
					passwords,
					confirmpasswords,
					checkboxValue1
				} = this


				if (!this.$u.test.email(usestring)) {

					uni.$u.toast(this.$t("register.enter_email"));

					return false
				}

				if (!passwords) {

					uni.$u.toast(this.$t("register.enter_password"));
					return false
				}
				// if(this.$utils.charTest(passwords) ){
				// uni.showToast({
				// 	title: '密码有特殊字符，请重新填写密码' || '校验错误',
				// 	icon: 'none'
				// });
				// 	// this.$utils.showToast(this.$t("common.specialChart"))
				// 	return false
				// }
				// if(passwords != confirmpasswords){
				// 	uni.showToast({
				// 		title: '两次密码不一致，请重新填写密码' || '校验错误',
				// 		icon: 'none'
				// 	});
				// 	return false
				// }

				if (!this.getcode) {


					uni.$u.toast(this.$t("register.get_ver_code"));

					return false
				}
				if (!code) {
					uni.$u.toast(this.$t("register.enter_vercode"));
					return false
				}

				//if (this.checkboxValue1.length == 0) {
				//	uni.$u.toast(this.$t("register.read_agress"));
				//	return false
				//}
				this.loading = true
				const res = this.$H.post('/api/v1/user/register', false, {
					user_string: usestring,
					code: code,
					password: passwords,
					re_password: confirmpasswords,
					type: "email",
					extension_code: this.invitecode
				}).then(res => {
					// console.log(res)?

					if (res.type == "ok") {
						setTimeout(() => {
							this.loading = false
							return uni.reLaunch({
								url: '/pages/login/login'
							})
						}, 300)
					} else {
						this.loading = false
					}
				})


			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'en'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})
				console.log(langs)
				this.language = langs
				this._i18n.locale = item.value
				console.log(item.value)
				uni.setStorageSync('lang', item.value)

				this.$utils.setTabbar(this)

				this.clangs = item
				this.hideModal()
				document.querySelector('.u-button__text').innerText = this.$t("reset.get_ver_code");
				// this.$store.commit('setLang', item.value)

				// setTimeout(() => {
				// 	this.showLanguage = false
				// }, 200)
			},
			hideModal(e) {
				this.modalName = null

			},
			codeChange(text) {
				this.tips = text;
			},
			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
			},
			getCode() {

				let {
					usestring,
					code,
					invitecode,
					passwords,
					confirmpasswords
				} = this


				// if(!this.$u.test.email(usestring)){

				// 	console.log('gggdsdfdfdsfds')
				// 	uni.showToast({
				// 		title: '邮箱格式不正确' || '校验错误',
				// 		icon: 'none'
				// 	});
				// 	return false
				// }

				// const res = await this.$H.post('/api/v1/user/register',{
				// 				   user_string:usestring,
				// 				   code:code,
				// 				   password:passwords,
				// 				   re_password:confirmpasswords,
				// 				   type:"email"
				// 				   }).then(res => {

				// 					})

				if (this.$refs.uCode.canGetCode) {
					uni.showLoading({
						// title: '正在获取验证码...'
					})
					const res = this.$H.post('/api/v1/sms_mail', false, {
						user_string: usestring
					}).then(res => {
						uni.hideLoading();
						if (res.type == "ok") {
							// uni.$u.toast('验证码已发送');
							console.log(res)
							this.getcode = 1;
							this.$refs.uCode.start();
						}
					})

				} else {
					// uni.$u.toast('倒计时结束后再发送');
				}
				// if (this.$refs.uCode.canGetCode) {
				// 	// 模拟向后端请求验证码
				// 	uni.showLoading({
				// 		title: '正在获取验证码...'
				// 	})












				// 	setTimeout(() => {
				// 		uni.hideLoading();
				// 		// 这里此提示会被this.start()方法中的提示覆盖
				// 		uni.$u.toast('验证码已发送');
				// 		// 通知验证码组件内部开始倒计时
				// 		this.$refs.uCode.start();
				// 	}, 2000);
				// } else {
				// 	uni.$u.toast('倒计时结束后再发送');
				// }
			},

			suffix() {

				this.types = this.types === "password" ? "text" : "password"
				this.passwordShow = !this.passwordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			},
			confirmsuffix() {

				this.confirmtypes = this.confirmtypes === "password" ? "text" : "password"
				this.confirmpasswordShow = !this.confirmpasswordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			}

		},
		computed: {
			i18n() {
				return this.$t("register")
			},
			suffixIcon() {

				if (this.passwordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			},
			confirmsuffixIcon() {

				if (this.confirmpasswordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			}
		}
	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}

	.u-border {
		border-color: #D2D6E8 !important;
	}

	.u-button--info[data-v-3bf2dba7] {
		color: #4B53F5;
		border-color: #f8f8f8;
		border-width: 0px;
		font-weight: bold;
	}

	.inps {
		background-color: #f8f8f8 !important;
		border: none;
		padding: 10px !important;
		height: 6vh;
		border-radius: 55px;
	}

	.my_u_button {
		background-color: #0166fc;
		height: 6vh;
		border-radius: 3vh;
		color: #FFFFFF !important;
	}

	.wrap_text {
		display: block;
		margin-bottom: 2vh;
		font-size: 1vh;

	}

	/deep/.u-icon__img {
		width: 20px !important;
		height: 20px !important;
	}

	/* /deep/.u-icon__icon[data-v-172979f2] {

	    font-size: 22px !important;
	} */
	/deep/.content-clear-icon {
		color: initial !important;
	}

	/deep/.uni-easyinput__content[data-v-abe12412] {
		border-radius: 6vh;
		background-color: #f8f8f8 !important;
		border: none;
		padding: 4px;
	}

	/deep/.uni-easyinput__placeholder-class {

		font-size: 15px;
		color: rgb(192, 196, 204);
		padding-left: 3px !important;
	}

	/deep/.uni-easyinput__content-input {

		padding-left: 3px !important;
	}

	.DrawerPage {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		background-color: #ffffff;
		transition: all 0.4s;
	}

	.DrawerPage.show {
		transform: scale(0.9, 0.9);
		left: 65vw;
		box-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);
		transform-origin: 0;
	}

	.DrawerWindow {
		position: absolute;
		width: 65vw;
		height: 100vh;
		left: 0;
		top: 0;
		transform: scale(0.9, 0.9) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
		padding: 100upx 0;
	}

	.DrawerWindow.show {
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	.DrawerClose {
		position: absolute;
		width: 40vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30upx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50upx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 35vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.cuIcon {
		width: 64upx;
		height: 64upx;
		line-height: 64upx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10upx;
		height: 10upx;
		background-color: currentColor;
		position: absolute;
		bottom: 10upx;
		border-radius: 10upx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}

	.DrawerWindow view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	.DrawerClose view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	view,
	image {
		box-sizing: border-box;
	}

	/*#ifdef MP*/
	view,
	image {
		box-sizing: content-box !important;
	}

	/*#endif*/
	.uni-scroll-view-content view,
	image {
		box-sizing: content-box !important;
	}

	/* 	/deep/.u-checkbox__icon-wrap--square{
	    border-radius: 50%;
	} */
</style>