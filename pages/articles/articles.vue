<template >
	<view style="padding: 10px; background: #fff;">
		
		<view style="margin-top: 5px; padding: 10px; font-size: 13px; font-weight: bold; line-height: 30px; background: #fff;">
			<u-parse :content="article.content" :lazyLoad="true" ></u-parse>
		</view>
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				article:''
			}
		},
		onLoad(e){
			
			console.log(e)
		
			if(e.item){
				 let userInfo = JSON.parse(decodeURIComponent(e.item));
				 
				 if(userInfo)
				 {
					 uni.setNavigationBarTitle({
					 	title:this.$t('register.privacy')
					 })
					 const res =  this.$H.get('/api/v1/getPrivacyPolicy',false,{
					  	 language:uni.getStorageSync('lang') || 'en'
					  }).then(res => {
					 	 if(res.type=="ok"){
							 
							 this.__init(res.data)
							 
						}
					 	 
					 })
				 }else{
					 uni.setNavigationBarTitle({
					 	title:this.$t('register.user_service')
					 })
					 const res =  this.$H.get('/api/v1/getUserAgreement',false,{
					  	language:uni.getStorageSync('lang') || 'en'
					  }).then(res => {
					 	 if(res.type=="ok"){
							 
							 								
							 								 this.__init(res.data)
							 
					 	 }
					 	 
					 })
				 }
				
				 
				 
				 
				 
				
				
			}
			// if(this.$store.state.bgColor=='black')
			// {
			// 			  uni.setNavigationBarColor({
			// 			    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
			// 			    backgroundColor: '#22252F' // 导航栏背景颜色
			// 			  });
			// }
		},
		methods: {
			__init(data){
				this.article=data
				// uni.setNavigationBarTitle({
				// 	title:data.title
				// })
			}
		}
	}
</script>

<style>
 page{
	 position: inherit;
	 overflow: initial;
	 background: #fff !important;
 }
</style>
