<template >
	<view :style="$store.state.bgColor=='black'?'padding: 10px; color: #fff;background: #1A1C24;':' background:#fff;padding: 10px;' ">
		<view style="margin-top: 10px;display: flex; flex-direction: column; justify-content: space-between; ">
			<view style="font-size: 32rpx; font-weight:bold; padding-left: 5px;">{{ article.title }}</view>
			<text style="font-size: 12px;color: #A0A6BE;padding-left: 5px; line-height: 30px;">{{article.create_time}}</text>
		</view>
		<view :style="$store.state.bgColor=='black'?'background: #22252F;font-size: 14px; padding: 20px; color: #6F6F72;margin-top: 5px;':'background: #F9F8F9;font-size: 14px; padding: 20px; color: #6F6F72;margin-top: 5px;'">
		  <text>{{article.abstract}}</text>			
		</view>
		<view style="margin-top: 5px; padding: 10px; font-size: 13px; font-weight: bold; line-height: 30px;">
			<u-parse :content="article.content" :lazyLoad="true" ></u-parse>
		</view>
		<view :style="$store.state.bgColor=='black'?'background: #22252F;font-size: 14px; font-weight: bold; padding: 20px; color: #6F6F72;margin-top: 20px;':'background: #F9F8F9;font-size: 14px; font-weight: bold; padding: 20px; color: #6F6F72;margin-top: 20px;'">
		  <text>免责声明：本文观点仅代表作者个人观点，不构成本平台的投资建议,本平台不对文章信息准确性、完整性和及时性作出任何保证,亦不对因使用或信赖文章信息引发的任何损失承担责任。</text>			
		</view>
		<!-- <view>热门资讯</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				article:''
			}
		},
		onLoad(e){
			uni.setNavigationBarTitle({
					title:this.$t('news.news_detail')
			})
			console.log(e)
		
			if(e.item){
				 let userInfo = JSON.parse(decodeURIComponent(e.item));
				 
				 
				
				this.__init(userInfo)
			}
			if(this.$store.state.bgColor=='black')
			{
						  uni.setNavigationBarColor({
						    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
						    backgroundColor: '#22252F' // 导航栏背景颜色
						  });
			}
		},
		methods: {
			__init(data){
				this.article=data
				// uni.setNavigationBarTitle({
				// 	title:data.title
				// })
			}
		}
	}
</script>

<style>
 page{
	 position: inherit;
	 overflow: initial;
	
 }
</style>
