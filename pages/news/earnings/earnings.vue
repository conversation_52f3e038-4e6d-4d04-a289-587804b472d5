<template>
	<view>
		<u-loading-icon v-if="loading" size="66"></u-loading-icon>
		<u-list class="page" scroll-y="true" v-if="!loading">
			<u-list-item class="list_title">
				<view class="">
					{{i18n.interest}}
				</view>
				<view class="">
					{{i18n.time}}
				</view>
			</u-list-item>
			<u-list-item class="list_item" v-for="(item, index) in list" :key="index">
				<view class="">
					{{item.is_lock}}
				</view>
				<view class="">
					{{item.created_time}}

				</view>
			</u-list-item>
			<view class="van-list__finished-text">{{i18n.no_more}}</view>
			<view class="van-list__placeholder"></view>
		</u-list>
		<u-modal :show="show" @confirm="confirm" @cancel="cancel" ref="uModal" :asyncClose="true"
			:showCancelButton="true" :confirmText="confirmText" :cancelText="cancelText">
			{{i18n.payment}}{{wyj}}{{i18n.liquidated_damages}}
		</u-modal>
	</view>
</template>

<script>
	export default {
		name: 'SubscriptionList',
		data() {
			return {
				list: [],
				loading: true,
				show: false,
				wyj: '',
				orderId: '',
				confirmText: this.$t("other.confirm"),
				cancelText: this.$t("other.cancel")
			}
		},
		mounted() {
			const hash = window.location.hash.substring(1); // 去掉 # 符号
			const urlParams = new URLSearchParams(hash.split('?')[1]);
			const id = urlParams.get('id');
			// this.purchase.id = parseFloat(id)
			let data = {
				orderId: id,
			}
			this.$H.get('/api/v1/project/order/profit', false, data).then(result => {
				this.list = result.data
				this.loading = false

			})
		},
		computed: {
			i18n() {
				return this.$t("mining")
			},

		},
		onLoad() {

			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("mining.subscription_list")

			})

		},
		methods: {
			goBack() {
				uni.navigateBack();

			},
			confirm() {
				let orderId = this.orderId
				let data = {
					orderId,
				}
				this.$H.post('/api/v1/project/order/applyRefund', false, data).then(result => {
					this.item = result.data
					this.show = false
				})
			},
			cancel() {
				this.show = false
			},
			onRedemptionClick(item) {

				this.orderId = item.orderId
				this.wyj = item.wyj
				this.show = true

			},
			onViewEarningsClick() {
				// 查看收益列表操作
			}
		}
	};
</script>

<style scoped>
	.page {
		position: relative;
		width: 100vw;
		height: 100%;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;
	}

	.list_title {
		display: flex;
		flex-direction: row;
		justify-content: space-around;

		padding-top: 10px;
	}

	.list_item {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
	}

	.disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10px;
		/* width: 100%; */
	}

	.van-nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
		height: 44px;
		background-color: #fff;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.van-nav-bar__content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.van-nav-bar__left {
		display: flex;
		align-items: center;
	}

	.van-nav-bar__title {
		font-size: 36px;
		font-weight: bold;
	}

	.van-nav-bar__right {
		display: flex;
		align-items: center;
	}

	.normal-shadow {
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.rounded-16 {
		border-radius: 16px;
	}

	.p-12 {
		padding: 12px;
	}

	.text-12 {
		font-size: 12px;
	}

	.mt-12 {
		margin-top: 12px;
	}

	.mx-10 {
		margin-left: 10px;
		margin-right: 10px;
	}

	.text-center {
		text-align: center;
	}

	.text-18 {
		font-size: 18px;
	}

	.text-16 {
		font-size: 16px;
	}

	.flex {
		display: flex;
		/* align-items: center; */
	}

	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.mt-10 {
		margin-top: 10px;
	}

	.px-20 {
		padding-left: 20px;
		padding-right: 20px;
	}

	.mt-20 {
		margin-top: 20px;
	}

	.text-14 {
		font-size: 14px;
	}

	.flex-1 {
		flex: 1;
	}

	.van-button {
		width: 88%;
		border-radius: 100px;
		color: white;
		background: rgb(1, 102, 252);
		border-color: rgb(1, 102, 252);
	}

	.van-button--normal {
		margin-top: 10px;
		padding: 2px 16px;
	}

	.van-button--block {
		/* width: 100%; */
	}

	.h-40 {
		height: 33px;
		line-height: 33px;
	}

	.van-list__finished-text {
		text-align: center;
		font-size: 14px;
		color: #999;
	}

	.van-list__placeholder {
		height: 50px;
	}
</style>