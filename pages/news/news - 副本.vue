<template>
	<view class="page">
		<view class="van-nav-bar van-hairline--bottom">
			<view class="van-nav-bar__content">
				<view class="van-nav-bar__title " style="width: 20px;"></view>
				<view class="van-nav-bar__title van-ellipsis">理財</view>
				<view class="van-nav-bar__right van-haptics-feedback" @click="onApplicationList()">
					<img src="@/static/application_record.png" alt="" srcset="" />
					<!-- <i class="van-icon van-icon-orders-o crx-assistant-active"
						style="color: #000; font-size: 1.6rem;">历史</i> -->
				</view>
			</view>
		</view>
		<u-loading-icon v-if="loading" size="66"></u-loading-icon>
		<u-list class="px-10" v-if="!loading" >
			<u-list-item class="px-10-item" v-for="(item, index) in items" :key="index">
				<view class="normal-shadow rounded-16 p-12 text-12 mt-12">
					<view class="text-center text-18">{{ item.project_name }}</view>

					<view class="flex items-center justify-between px-20 mt-20">
						<view class="flex items-center">
							<image class="min-w-60 w-60 h-60" :src="item.project_img"></image>
							<view class="text-18 mx-10">{{ item.currency }}</view>
						</view>
						<view>
							<view class="text-14">{{i18n.expected_nissan}}: {{ item.project_lixi }}%</view>
							<view class="text-12 break-all">{{i18n.mining_machine_rental}} {{ item.project_amount_min }}</view>
						</view>
					</view>
					<button type="primary" @click="onFinancialApplication(item)"
						class="van-button van-button--normal van-button--block mt-20 rounded-lg h-40 text-14 text-white"
						style="background-color: #0166FC; color: white;">
						<view class="van-button__content">
							<span class="van-button__text">{{i18n.subscribe}}</span>
						</view>
					</button>
				</view>
			</u-list-item>
			<view class="van-list__finished-text">{{i18n.no_more}}</view>
		</u-list>

		<tab-bar @click="onShowTabBar()"></tab-bar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activeTab: 3,
				loading: true,
				items: [
				],

			};
		},
		onShow() {
			this.$store.commit('changeTabbarIndex', 3)
		},
		computed: {
			i18n() {
				return this.$t("mining")
			},

		},
		mounted() {
			this.gteList()
		},
		methods: {
			switchTab(index) {
				this.activeTab = index;
			},
			gteList() {
				let data = {
					limit: 100,
					page: 1
				}

				this.$H.get('/api/v1/projectList', false, data).then(result => {
					this.items= result.data.data
					 this.loading=false
				})
			},
			onFinancialApplication(item) {
				uni.navigateTo({
					url: `/pages/news/financialApplication/financialApplication?id=${item.id}`
				});
			},
			onApplicationList() {
				uni.navigateTo({
					url: `/pages/news/record/record`
				});
			}
		}
	};
</script>

<style scoped>
	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
	.normal-shadow {
		box-shadow: 0 0 2.93333vw rgba(161, 161, 161, .18);
		height: 177px;
		margin: 12px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.rounded-16 {
		border-radius: 16px;
	}

	.van-nav-bar__content {
		margin: 15px;
		margin-bottom: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;

	}

	.px-10 {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;

	}

	.p-12 {
		padding: 12px;
	}

	.text-12 {
		font-size: 12px;
	}

	.mt-12 {
		margin-top: 12px;
	}

	.text-18 {
		font-size: 18px;
	}

	.flex {
		display: flex;
	}
	.van-ellipsis{
		font-size: 18px
	}
	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.px-20 {
		padding-left: 20px;
		padding-right: 20px;
	}

	.min-w-60 {
		min-width: 60px;
	}

	.w-60 {
		width: 60px;
	}

	.h-60 {
		height: 60px;
	}

	.mx-10 {
		margin-left: 10px;
		margin-right: 10px;
	}

	.text-14 {
		font-size: 14px;
	}

	.break-all {
		word-break: break-all;
	}

	.van-list__finished-text {
		text-align: center;
		font-size: 14px;
	}

	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		z-index: 1000;
	}

	.van-tabbar-item {
		flex: 1;
		text-align: center;
		padding: 12px 0;
	}

	.van-tabbar-item__icon {
		width: 50px;
		height: 50px;
		margin: 0 auto 8px;
	}

	.svg-icon {
		width: 100%;
		height: 100%;
	}

	.van-tabbar-item--active .svg-icon use {
		fill: #0166FC;
	}

	.van-button {
		width: 88%;
		border-radius: 88px;
	}
</style>