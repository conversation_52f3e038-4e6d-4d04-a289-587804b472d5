<template>
	<view>
		<h2>理财</h2>
		<view class="">
			<text>
				HRM2G-150K
			</text>
			<view class="">
				<img src="" alt="" />
				<view src="">
					
				</view>
			</view>
		</view>
		<tab-bar @click="onShowTabBar()"></tab-bar>

	</view>

</template>

<script>
	export default {
		components: {

		},
		data() {
			return {
				messageList: [],
				pages: 1,
				status: false,
				loding: "",
				scrollH: 0,
				src: 'https://test.lklee.org/', // 替换为你想要嵌入的外部网址
				width: window.innerWidth,
				height: window.innerHeight
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: 'priectw',
				backIcon: '@/static/<EMAIL>'
			})

		},
		mounted() {},
		onShow() {
			this.$store.commit('changeTabbarIndex', 3)

		},
		onReachBottom() {},
		methods: {

		}
	}
</script>

<style>
	iframe {
		border: none;
		/* 移除边框 */
		padding: 0;
		/* 移除内边距 */
		margin: 0;
		/* 移除外边距 */
		overflow: hidden;
		/* 隐藏溢出内容 */
		display: block;
		/* 让它作为一个块级元素显示 */
	}

	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
</style>