<template>
	
		<scroll-view v-if="!loading" scroll-y="true" class="page">
			<view class="p-12">
				<view class="flex items-center mb-20">
					
					<view class="flex  mb-20" style="width: 100%;">
						<image class="w-60 h-60 ml-10" :src="item.project_img" mode="aspectFill"></image>
						<view class="ml-20">
							<view class="text-18">{{ item.project_name }}</view>
							<view class="text-14 mt-16">
								<span class="pr-10 text-16">{{ currency }}</span>
								<span class="bg-up text-white rounded-8 px-10 py-4">{{ item.project_lixi }}%</span>
								</div>
							</view>
						</view>
					</view>
					<view class="bg-#383838 rounded-16 p-18 text-16 mt-20">
						<view class="flex items-box justify-between">
							<view class="text-#888">{{i18n.mining_machine_rental}}</view>
							<view>{{ item.project_amount }}</view>
						</view>
						<view role="separator" class="van-divider van-divider--hairline mt-12"></view>
						<view class="flex items-box justify-between mt-12">
							<view class="text-#888">{{i18n.mining_life}}</view>
							<view>{{ item.lock_dividend_days }}</view>
						</view>
						<view role="separator" class="van-divider van-divider--hairline mt-12"></view>
						<view class="flex items-box justify-between mt-12">
							<view class="text-#888">{{i18n.maximum_subscription}}</view>
							<view>{{ item.project_amount_max }}</view>
						</view>
						<view role="separator" class="van-divider van-divider--hairline mt-10"></view>
						<input class="text-16 items-box mt-12 w-full" type="number" :placeholder="i18n.max_number"
							v-model="lockAmount" />
						<view role="separator" class="van-divider van-divider--hairline mt-10"></view>
						<button type="primary"
							class="van-button van-button--normal van-button--block mt-20 rounded-lg h-40 text-14"
							style="color: white; background: #0166FC; border-color: #0166FC;" @click="onPurchaseClick">
							{{i18n.subscribe}}
						</button>
						<view class="mt-20">{{i18n.product_presentation}}</view>
						<view class="container-text" v-html="item.project_desc"></view>
					</view>
				</view>
			</view>
			<u-toast ref="uToast"></u-toast>
			<u-popup :show="show" :round="30" mode="bottom" @close="close" @open="openss">
				<u-toast ref="uToast2"></u-toast>
				<view class="container ">
					<view class="p-12 container-box">
						<view class="flex  mb-20 ">
							<view class="text-18">{{item.project_name}}</view>
							<!-- <view class="text-14 mt-16">
									<span class="pr-10 text-16">USD</span>
									<span class="bg-up text-white rounded-8 px-10 py-4">預計日產:0.06%</span>
								</view> -->
						</view>
						<view class="bg-#383838 rounded-16 p-18 text-16 mt-20">
							<view class="flex  justify-between">
								<view class="text-#888">{{i18n.mining_machine_rental}}</view>
								<view>{{item.project_amount}}</view>
							</view>
							<view class="flex  justify-between mt-12">
								<view class="text-#888">{{i18n.mining_life}}</view>
								<view>{{item.lock_dividend_days}}</view>
							</view>
							<view class="flex  justify-between mt-12">
								<view class="text-#888">{{i18n.expected_nissan}}</view>
								<view>{{parseFloat((lockAmount*item.project_lixi/100).toFixed(2))}}</view>
							</view>
							<view class="flex  justify-between mt-12">
								<view class="text-#888">{{i18n.projected_total_revenue}}</view>
								<view>
									{{parseFloat((lockAmount*item.project_lixi*item.lock_dividend_days/100).toFixed(2))}}
								</view>
							</view>
							<view class="flex  justify-between mt-12">
								<view class="text-#888">{{i18n.number_application}}</view>
								<view>{{lockAmount}}</view>
							</view>

							<button type="primary"
								class="van-button van-button--default van-button--normal van-button--block mt-20 rounded-lg h-40 text-14"
								style="color: white; background: rgb(1, 102, 252); border-color: rgb(1, 102, 252);"
								@click="onPurchase">
								<view class="van-button__content">
									<span class="van-button__text">{{i18n.subscribe}}</span>
								</view>
							</button>
						</view>
					</view>
				</view>
			</u-popup>
		</scroll-view>


</template>

<script>
	export default {
		data() {
			return {

				item: {},
				purchase: {},
				currency: 'USD',
				loading: true,
				lockAmount: '',
				show: false,
				params: {
					type: 'default',
					title: '默认主题',
					message: "請輸入鎖倉數量",
					iconUrl: 'https://cdn.uviewui.com/uview/demo/toast/default.png'
				},
				params1: {
					type: 'default',
					title: '默认主题',
					message: "成功",
					iconUrl: 'https://cdn.uviewui.com/uview/demo/toast/default.png'
				},
				params2: {
					type: 'default',
					title: '默认主题',
					message: "起购数不足",
					iconUrl: 'https://cdn.uviewui.com/uview/demo/toast/default.png'
				},
				
			};
		},
		computed: {
			i18n() {
				return this.$t("mining")
			},
		},
		mounted() {
			const hash = window.location.hash.substring(1); // 去掉 # 符号
			const urlParams = new URLSearchParams(hash.split('?')[1]);
			const id = urlParams.get('id');
			this.purchase.id = parseFloat(id)
			let data = {
				id,
			}
			this.$H.get('/api/v1/project/info', false, data).then(result => {
				this.item = result.data
				this.loading = false
				this.params.message=this.i18n.max_number
				this.params1.message1=this.i18n.succeed
				this.params2.message1=this.i18n.lazy_weight
			})
		},
		onLoad() {

			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("mining.subscription")

			})
		},
		methods: {
			onClickLeft() {
				// 返回上一页
				uni.navigateBack();
			},
			close() {
				this.show = false
			},
			openss() {

			},
			onPurchase() {
				if (this.lockAmount < parseFloat(this.item.project_amount_min)) {
					this.$refs.uToast2.show({
						...this.params2,
						complete() {
						}
					})
					return
				}
				const data = this.purchase
				this.$H.post('/api/v1/project/buy', false, data).then(result => {
					if (result.type == 'ok') {
						this.$refs.uToast.show({
							...this.params1,
							complete() {}
						})
						this.lockAmount = ''
						this.show = false
					}
				})
			},
			onPurchaseClick() {
				// 处理购买逻辑
				if (this.lockAmount == 0) {
					this.$refs.uToast.show({
						...this.params,
						complete() {
							this.params.url && uni.navigateTo({
								url: this.params.url
							})
						}
					})
					return
				} else {
					this.purchase.amount = parseFloat(this.lockAmount)
					this.show = true
				}

			}
		}
	};
</script>

<style scoped>
	.page {
		position: relative;
		width: 100vw;
		height: 100%;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;
	}

	.items-center {
		flex-direction: column;
	}

	.w-60 {
		width: 60px;
	}

	.h-60 {
		height: 60px;
	}

	.bg-up {
		background-color: #0166FC;
	}

	.van-nav-bar__title {
		color: #000;
	}

	.van-nav-bar__left .van-icon {
		color: #000;
		font-size: 1.6rem;
	}

	/* 其他样式可以按照需求继续添加 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin: 15px;
	}

	.container-box {
		width: 100%;
	}

	.p-12 {
		padding: 15px;
	}

	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.mb-20 {
		margin-bottom: 20px;
	}

	.ml-20 {
		margin-left: 20px;
	}

	.w-60 {
		width: 60px;
	}

	.h-60 {
		height: 60px;
	}

	.ml-10 {
		margin-left: 10px;
	}

	.text-18 {
		font-size: 18px;
	}

	.text-14 {
		font-size: 14px;
	}

	.text-16 {
		font-size: 16px;
	}

	.mt-16 {
		margin-top: 16px;
	}

	.pr-10 {
		padding-right: 10px;
	}

	.bg-up {
		background-color: #0166fc;
	}

	.rounded-8 {
		border-radius: 8px;
	}

	.px-10 {
		padding-left: 10px;
		padding-right: 10px;
	}

	.py-4 {
		padding-top: 4px;
		padding-bottom: 4px;
	}

	.mt-12 {
		margin-top: 12px;
	}

	.mt-20 {
		margin-top: 20px;
	}

	.van-divider {
		border-top: 0.1px solid #ebedf0;
		margin-top: 12px;
		margin-bottom: 16px;
	}

	.van-button--normal {
		border-radius: 100px;
		padding: 2px 2px;
	}

	.van-button--block {
		width: 96%;
	}

	.items-box {
		height: 24px;
	}

	.h-40 {
		height: 40px;
	}

	.text-14 {
		font-size: 14px;
	}

	.container-text {
		margin-bottom: 50px;
	}
</style>