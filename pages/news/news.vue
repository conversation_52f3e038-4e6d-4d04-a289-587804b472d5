<template>
	<view class="page">
		<view class="van-nav-bar van-hairline--bottom">
			<view class="van-nav-bar__content">
				<view class="van-nav-bar__title " style="width: 20px;"></view>
				<view class="van-nav-bar__title van-ellipsis">{{i18n1.rw3}}</view>
				<view></view>
				<!-- <view class="van-nav-bar__right van-haptics-feedback" @click="onApplicationList()">
					<img src="@/static/application_record.png" alt="" srcset="" />
				</view> -->
			</view>
		</view>
		<u-loading-icon v-if="loading" size="66"></u-loading-icon>
		<u-list class="px-10" v-if="!loading">
			<u-list-item class="px-10-item" v-for="(item, index) in items" :key="index">
				<view class="" style="display: flex;align-items: center;margin: 20rpx;" @click="Todetail(item)">
					<img :src="item.thumbnail_info" style="height: 140rpx;width: 140rpx;margin-right: 20rpx;" alt="" srcset="" />
					<view>
						<view class="">
							{{item.title}}
						</view>
						<view class="clamp-2" style="color: #ccc;margin-top: 4px;">
							{{item.create_time}}
						</view>
					</view>
				</view>
			</u-list-item>
			<view class="van-list__finished-text">{{i18n.no_more}}</view>
		</u-list>

		<tab-bar @click="onShowTabBar()"></tab-bar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activeTab: 3,
				loading: true,
				items: [],

			};
		},
		onShow() {
			this.$store.commit('changeTabbarIndex', 3)
		},
		computed: {
			i18n() {
				return this.$t("mining")
			},
			i18n1() {
				return this.$t("order")
			},
		},
		mounted() {
			this.gteList()
		},
		methods: {
			switchTab(index) {
				this.activeTab = index;
			},
			gteList() {
				let data = {
					limit: 100,
					page: 1
				}
				this.$H.post('/api/v1/getInformation', false, data).then(result => {
					this.items = result.data.data
					this.loading = false
				})
			},
			Todetail(item){
				console.log(79,item)
				uni.navigateTo({
					url: `/pages/news/detail?id=${item.id}`
				});
			},
			onFinancialApplication(item) {
				uni.navigateTo({
					url: `/pages/news/financialApplication/financialApplication?id=${item.id}`
				});
			},
			onApplicationList() {
				uni.navigateTo({
					url: `/pages/news/record/record`
				});
			}
		}
	};
</script>

<style scoped>
	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}

	.normal-shadow {
		box-shadow: 0 0 2.93333vw rgba(161, 161, 161, .18);
		height: 177px;
		margin: 12px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.rounded-16 {
		border-radius: 16px;
	}

	.van-nav-bar__content {
		margin: 15px;
		margin-bottom: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;

	}

	.px-10 {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;

	}

	.p-12 {
		padding: 12px;
	}

	.text-12 {
		font-size: 12px;
	}

	.mt-12 {
		margin-top: 12px;
	}

	.text-18 {
		font-size: 18px;
	}

	.flex {
		display: flex;
	}

	.van-ellipsis {
		font-size: 18px
	}

	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.px-20 {
		padding-left: 20px;
		padding-right: 20px;
	}

	.min-w-60 {
		min-width: 60px;
	}

	.w-60 {
		width: 60px;
	}

	.h-60 {
		height: 60px;
	}

	.mx-10 {
		margin-left: 10px;
		margin-right: 10px;
	}

	.text-14 {
		font-size: 14px;
	}

	.break-all {
		word-break: break-all;
	}

	.van-list__finished-text {
		text-align: center;
		font-size: 14px;
	}

	.tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		z-index: 1000;
	}

	.van-tabbar-item {
		flex: 1;
		text-align: center;
		padding: 12px 0;
	}

	.van-tabbar-item__icon {
		width: 50px;
		height: 50px;
		margin: 0 auto 8px;
	}

	.svg-icon {
		width: 100%;
		height: 100%;
	}

	.van-tabbar-item--active .svg-icon use {
		fill: #0166FC;
	}

	.van-button {
		width: 88%;
		border-radius: 88px;
	}
</style>