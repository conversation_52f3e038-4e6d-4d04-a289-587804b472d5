<template>
	<view>
		<u-loading-icon v-if="loading" size="66"></u-loading-icon>
		<u-list class="page" scroll-y="true" v-if="!loading">
			<u-list-item class="van-pull-refresh__track" v-for="(item, index) in list" :key="index"
				style="transition-duration: 300ms;">
				<view class="van-pull-refresh__head"></view>
				<view role="feed" class="van-list" aria-busy="false">
					<view class="normal-shadow rounded-16 p-12 text-12 mt-12 mx-10">
						<view class="text-center text-18">{{item.project.project_name}}</view>
						<view role="separator" class="van-divider van-divider--hairline mt-10"></view>
						<view class="flex items-center justify-between mt-10 text-16 px-20">
							<view>USD</view>
							<view v-if="item.status==='have in hand'">{{i18n.underway}}</view>
							<view v-if="item.status==='Apply for refund'">{{i18n.applying_refund}}</view>
							<view v-if="item.status==='deposit'">{{i18n.deposit}}</view>
							<view v-if="item.status==='Refunded'">{{i18n.refund_successful}}</view>
						</view>
						<view class="flex items-center justify-between text-center mt-20 text-14">
							<view class="flex-1 items-center">
								<view>{{item.amount}}</view>
								<view>{{i18n.number_application}}</view>
							</view>
							<view class="flex-1 items-center">
								<view>{{item.interest_gen_next_time}} </view>
								<view>{{i18n.Application_time}}</view>
							</view>
						</view>
						<view class="flex items-center justify-between text-center mt-20 text-14">
							<view class="flex-1 items-center">
								<view>{{item.day_profit}}</view>
								<view>{{i18n.expected_nissan}}</view>
							</view>
							<view class="flex-1 items-center">
								<view>{{item.sum_profit}}</view>
								<view>{{i18n.projected_total_revenue}}</view>
							</view>
						</view>
						<button type="primary" :disabled="item.status!=='have in hand'" :class="[
							        'van-button',
							        'van-button--default',
							        'van-button--normal',
							        'van-button--block',
							        'mt-20',
							        'rounded-lg',
							        'h-40',
							        'text-14',
							        { disabled: item.status == 'have in hand' }
							      ]" style="" @click="onRedemptionClick(item)">
							<view class="van-button__content">
								<span class="van-button__text">{{i18n.redemption_default}}</span>
							</view>
						</button>
						<button type="primary"
							class="van-button van-button--default van-button--normal van-button--block mt-10 rounded-lg h-40 text-14"
							style="color: white; background: rgb(1, 102, 252); border-color: rgb(1, 102, 252);"
							@click="onViewEarningsClick(item)">
							<view class="van-button__content">
								<span class="van-button__text">{{i18n.view_earnings_list}}</span>
							</view>
						</button>
					</view>

				</view>
			</u-list-item>
			<view class="van-list__finished-text">{{i18n.no_more}}</view>
			<view class="van-list__placeholder"></view>
		</u-list>
		<u-modal :show="show" @confirm="confirm" @cancel="cancel" ref="uModal" :asyncClose="true"
			:showCancelButton="true" :confirmText="confirmText" :cancelText="cancelText">
			{{i18n.payment}}{{wyj}}{{i18n.liquidated_damages}}
		</u-modal>
	</view>
</template>

<script>
	export default {
		name: 'SubscriptionList',
		data() {
			return {
				list: [],
				loading: true,
				show: false,
				wyj: '',
				orderId: '',
				confirmText: this.$t("other.confirm"),
				cancelText: this.$t("other.cancel")
			}
		},
		computed: {
			i18n() {
				return this.$t("mining")
			},

		},
		onLoad() {

			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("mining.subscription_list")

			})
			let data = {
				limit: 10,
				page: 1
			}
			this.$H.get('/api/v1/project/order/list', false, data).then(result => {
				this.list = result.data.data
				this.loading = false
			})
		},
		methods: {
			goBack() {
				uni.navigateBack();

			},
			confirm() {
				let orderId = this.orderId
				let data = {
					orderId,
				}
				this.$H.post('/api/v1/project/order/applyRefund', false, data).then(result => {
					this.item = result.data
					this.show = false
				})
			},
			cancel() {
				this.show = false
			},
			onRedemptionClick(item) {
			
				this.orderId= item.orderId
				this.wyj= item.wyj
				this.show = true

			},
			onViewEarningsClick(item) {
				// 查看收益列表操作
				
				uni.navigateTo({
					url: `/pages/news/earnings/earnings?id=${item.id}`
				});
			}
		}
	};
</script>

<style scoped>
	.page {
		position: relative;
		width: 100vw;
		height: 100%;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;
	}

	.disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10px;
		/* width: 100%; */
	}

	.van-nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
		height: 44px;
		background-color: #fff;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.van-nav-bar__content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.van-nav-bar__left {
		display: flex;
		align-items: center;
	}

	.van-nav-bar__title {
		font-size: 36px;
		font-weight: bold;
	}

	.van-nav-bar__right {
		display: flex;
		align-items: center;
	}

	.normal-shadow {
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.rounded-16 {
		border-radius: 16px;
	}

	.p-12 {
		padding: 12px;
	}

	.text-12 {
		font-size: 12px;
	}

	.mt-12 {
		margin-top: 12px;
	}

	.mx-10 {
		margin-left: 10px;
		margin-right: 10px;
	}

	.text-center {
		text-align: center;
	}

	.text-18 {
		font-size: 18px;
	}

	.text-16 {
		font-size: 16px;
	}

	.flex {
		display: flex;
		/* align-items: center; */
	}

	.items-center {
		align-items: center;
	}

	.justify-between {
		justify-content: space-between;
	}

	.mt-10 {
		margin-top: 10px;
	}

	.px-20 {
		padding-left: 20px;
		padding-right: 20px;
	}
	.mt-20{
		margin-top: 20px;
	}
	.text-14 {
		font-size: 14px;
	}

	.flex-1 {
		flex: 1;
	}

	.van-button {
		width: 88%;
		border-radius: 100px;
		color: white;
		background: rgb(1, 102, 252);
		border-color: rgb(1, 102, 252);
	}

	.van-button--normal {
		margin-top: 10px;
		padding: 2px 16px;
	}

	.van-button--block {
		/* width: 100%; */
	}

	.h-40 {
		height: 33px;
		line-height: 33px;
	}

	.van-list__finished-text {
		text-align: center;
		font-size: 14px;
		color: #999;
	}

	.van-list__placeholder {
		height: 50px;
	}
</style>