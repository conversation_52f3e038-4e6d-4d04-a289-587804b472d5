<template>
	<view :class="$store.state.bgColor=='black' ? 'jybs' :'jys'">
		<!-- 头部切换币 -->
		<view
			style="display: flex; justify-content: space-between; align-items: right;  margin: 20px 15px 0;padding: 5px 10px;border-radius: 10px; box-shadow: rgba(204, 204, 204, 0.34) 0px 0px 26px 0px;">
			<!-- <view v-if="$store.state.bgColor=='black'"
				style="flex: 1;height: 20px; line-height: 20px; font-size: 16px; font-weight: bold; color: #fff; "
				@click="bzsClick()">
				<view>{{currency_name}}<uni-icons type="bottom" size="14"
						style="font-weight: bold; color:#fff; "></uni-icons></view>
			</view> -->
			<view v-if="$store.state.bgColor=='while'"
				style="flex: 1;height: 20px; line-height: 20px; font-size: 14px; font-weight: bold;  "
				@click="bzsClick()">
				<view>{{alias_currency_name}}<uni-icons type="bottom" size="14"
						style="font-weight: bold;  "></uni-icons>
				</view>
			</view>
			<!-- 价格显示区域 - 增强稳定性 -->
			<view style="text-align: right; display: flex; justify-content: center; align-items: center;">
				<!-- 主要价格显示 -->
				<view v-if="topPrice"
					:style="{ 'color': symbolQuotation.change > 0 ? '#0164f7' : (symbolQuotation.change < 0 ? '#EF263D' : '#0164f7') }">
					{{ topPrice | setPrecision(symbolQuotation.precision_length) }}
				</view>
				<!-- 备用价格显示 - 当主要数据不可用时 -->
				<view v-else-if="originalQuotation && originalQuotation.length > 0"
					style="font-size: 16px; font-weight: bold; color: #0164f7;">
					{{getCurrentPrice()}}
				</view>
				<!-- 加载状态 -->
<!--				<view v-else style="font-size: 16px; font-weight: bold; color: #999;">-->
<!--					加载中...-->
<!--				</view>-->
			</view>
		</view>
		<view style="width: 95%;  height: 345px;  padding: 0px 10px; 5px">
			<!-- <view class="button-sp-area">
		<!-- <button class="mini-btn" type="default" size="mini" @click="ChangePeriod(-1)">分时</button> -->
			<!-- <button class="mini-btn" type="default" size="mini" @click="ChangePeriod(4)">1M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(5)">5M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(6)">15M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(7)">30M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(8)">1H</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(0)">1D</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(1)">1W</button> -->
			<!-- 	<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(2)">月</button>
		<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(3)">年</button> -->

			<!-- <button class="mini-btn" type="default" size="mini" @click="ChangeDepthChart()">深度图</button> -->
			<!-- </view> -->
			<view style="display: flex; justify-content: center; text-align: center; align-items: center;">
				<!-- <view v-if="$store.state.bgColor=='black'"
					style="padding-right: 10px; font-size:15px; font-weight: bold; width:100px; color: #fff;"
					@click="zbclick()">
					<view>indicator<uni-icons type="bottom" size="14"
							style="font-weight: bold; color: #fff;"></uni-icons></view>
				</view>
				<view v-if="$store.state.bgColor=='while'"
					style="padding-right: 10px; font-size:15px; font-weight: bold; width:100px; " @click="zbclick()">
					<view>indicator<uni-icons type="bottom" size="14" style="font-weight: bold;"></uni-icons></view>
				</view> -->
				<view style="width: 100%;overflow: hidden; overflow-x: scroll;white-space: nowrap; ">
					<!-- 	<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0, 'EMPTY')">EMPTY</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0,'BOLL')">ABOLL</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'BOLL')">BOLL</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'BOLL')">BOLL</button> -->
					<view @click="ChangePeriod(4)" :class="pids==4 ? sdhbts : dhbts" style="border-radius: 100px;">1M
					</view>
					<!-- <view @click="ChangePeriod(5)":class="pids==5 ? sdhbts : dhbts">5M</view> -->
					<view @click="ChangePeriod(6)" :class="pids==6 ? sdhbts : dhbts" style="border-radius: 100px;">15M
					</view>
					<view @click="ChangePeriod(7)" :class="pids==7 ? sdhbts : dhbts" style="border-radius: 100px;">30M
					</view>
					<view @click="ChangePeriod(8)" :class="pids==8 ? sdhbts : dhbts" style="border-radius: 100px;">1H
					</view>
					<view @click="ChangePeriod(0)" :class="pids==0 ? sdhbts : dhbts" style="border-radius: 100px;">1D
					</view>
					<!-- <view @click="ChangePeriod(1)" :class="pids==1 ? sdhbts : dhbts">1W</view> -->

				</view>
			</view>
			<view class='divchart' style='background-color:#0e182e;position: relative;'>
				<!--  #ifdef  H5 -->
				<!-- k线 -->
				<div>
					<div :class="$store.state.bgColor=='black' ? 'klines' :'kline'" id="kline" ref='kline'></div>
				</div>
				<!--  #endif -->
				<!--  #ifndef  H5 -->
				<view>
					<canvas id="kline2" canvas-id='kline2' class='kline2'
						v-bind:style="{width: ChartWidth+'px', height: ChartHeight+'px'}" @touchstart="KLineTouchStart"
						@touchmove='KLineTouchMove' @touchend='KLineTouchEnd'></canvas>
				</view>
				<!--  #endif -->

				<!--<div class="button-sp-area">
			<button class="mini-btn" type="default" size="mini" @click="ChangeSymbol('btcusdt')">btcusdt</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeSymbol('ethusdt')">ethusdt</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0, 'EMPTY')">EMPTY</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
		</div> -->

			</view>


		</view>
		<!-- <view style="height:5px"></view> -->

		<view style="">

			<!--
			<view v-if="$store.state.bgColor=='black'"
				style="display: flex; background: #1A1B24; justify-content: center; align-items: center; width: 100%;line-height: 68px;font-size:15px;border-bottom: 2px solid #22252F;">
				<u-subsection :list="navs" inactiveColor="#828397" activeColor="#ffffff" :current="currentNav"
					@change="sectionChange"></u-subsection>
			</view> -->
			<!-- m秒合约和合约 -->
			<!--
			<view style=" width: 100%; font-size: 15px;" v-if="$store.state.bgColor=='while'">
				<u-subsection :list="navs" :current="currentNav" @change="sectionChange"></u-subsection>
			</view> -->

			<view v-show="currentNav == 0"
				style="width:100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
				<!-- 顶部选项卡 -->
				<!-- 		<view style="height: 8px;"></view>
				<view class="scroll-view" style="background-color: #2F3142;" v-if="$store.state.bgColor=='black'">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
						:class="tabIndex===index?'scroll-itembs':'scroll-itemb'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view>
 -->
				<view class="scroll-viewws" style="background-color: #F5F6FAFF;" v-if="$store.state.bgColor=='while'">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-viewws"
						:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view>

				<!-- <view class="scroll-view">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
						:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view> -->
				<!-- 交易 -->
				<swiper :duration="100" :current="tabIndex" @change="onChangeTab"
					style="overflow: hidden;width:95%;padding-left:15px; height: 430px; ">
					<swiper-item>
						<scroll-view scroll-y="true" enable-flex="true" class="section-area">
							<u--form labelPosition="left" :model="model1" ref="uForm">
								<u-form-item ref="item1">
									<view
										:style="$store.state.bgColor=='black'?'width: 98%; margin-top: 12px; font-size: 12px; color: #ffffff;':'width: 98%; margin-top: 12px; font-size: 12px;'">
										<!-- <view
											style="display: flex; justify-content: center; align-items: center; width: 100%;"
											@click="shows = true">
											<view style="flex: 1;font-size: 12px; font-weight: bold;">
												{{i18n.multiplier}}
											</view>
											<view style="width: 50%;"><u--input v-model="multiple0" :disabled="true"
													disabledColor="#fff" placeholder="" inputAlign="center" style=""
													suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold; "></u--input>
											</view>
										</view> -->
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center; color: ;">
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;"
												class="wnss">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="hyzs" activeColor="#0166fc" size="40"
														inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;"
												class="bnss">
												<view>{{i18n.stop_loss}}</view>
												<view>
													<u-switch space="2" v-model="hyzs" activeColor="#0166fc" size="40"
														inactiveColor="#424656">
													</u-switch>
												</view>
											</view>
											<view style="width: 52%;" v-if="$store.state.bgColor=='black'" class="bnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(1,2)">
														<u-icon name="minus" size="28" :color="hyzsc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="hyzsc" v-model="stop_loss_price"
														:min="0" :disabled="hyzsd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(1,1)">
														<u-icon name="plus" size="28" :color="hyzsc"></u-icon>
													</view>
												</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(1,2)">
														<u-icon name="minus" size="28" :color="hyzsc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="hyzsc" v-model="stop_loss_price"
														:min="0" :disabled="hyzsd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(1,1)">
														<u-icon name="plus" size="28" :color="hyzsc"></u-icon>
													</view>
												</view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="hyzy" activeColor="#0166fc" size="40"
														:min="0" inactiveColor="#424656">
													</u-switch></view>
											</view>
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="hyzy" activeColor="#0166fc" size="40"
														:min="0" inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(2,2)">
														<u-icon name="minus" size="28" :color="hyzyc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="hyzyc" v-model="target_profit_price"
														:min="0" :disabled="hyzyd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(2,1)">
														<u-icon name="plus" size="28" :color="hyzyc"></u-icon>
													</view>
												</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(2,2)">
														<u-icon name="minus" size="28" :color="hyzyc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="hyzyc" v-model="target_profit_price"
														:min="0" :disabled="hyzyd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(2,1)">
														<u-icon name="plus" size="28" :color="hyzyc"></u-icon>
													</view>
												</view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.buy_quantity}}</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='black'" class="bnss">
												<!-- <view>
													<u-input inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="number" :integer="false" :min="0"
														:max="symbolQuotation.lever_max_share" button-size="70">
													</u-input>
												</view> -->
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(3,2)">
														<u-icon name="minus" size="28"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														bgColor="#12151B" inputAlign="center" color="#fff"
														v-model="number" :min="0"
														:max="symbolQuotation.lever_max_share"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(3,1)">
														<u-icon name="plus" size="28"></u-icon>
													</view>
												</view>
											</view>
											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(3,2)">
														<u-icon name="minus" size="28"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														bgColor="#12151B" inputAlign="center" color="#000"
														v-model="number" :min="0"
														:max="symbolQuotation.lever_max_share"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(3,1)">
														<u-icon name="plus" size="28"></u-icon>
													</view>
												</view>
											</view>
										</view>

										<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
											v-if="$store.state.bgColor=='while'"></view>
										<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
											v-if="$store.state.bgColor=='black'"></view>

										<view style="width: 100%; font-size: 12px; font-weight: bold;">
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.each}}</view>
												<view>
													<!-- {{i18n.sheet}}={{each_piece}}{{alias_currency_name}} -->
													{{i18n.sheet}} = 500 {{alias_currency_name}}
												</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_commission}}</view>
												<view>{{ handlingFee }}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_margin}}</view>
												<view>{{margin}}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.balance}}</view>
												<view>{{users.usdt}}</view>
											</view>
										</view>
										<view style="width: 100%; ">
											<view style="display: flex; width: 100%; padding-top: 10px;">
												<!--#ifndef APP-PLUS-->
												<u-button :loading="xdloading" :disabled="mrdis" loadingText=""
													@click="orderClicks(1,1)" style=" border-radius: 100rpx; background: #0166fc;
											    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_up}}</u-button>
												<!--#endif -->
												<!--#ifdef APP-PLUS-->
												<button @click="orderAppClicks()" style=" border-radius: 100rpx; background: #0166fc;
											    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_down}}</button>
												<!--#endif -->
												<u-button :loading="xdmloading" :disabled="mcdis" loadingText=""
													@click="orderClicks(2,1)"
													style=" border-radius: 100rpx; background: #F23C48;
											    border: 0;  width: 49%; height: 35px;
													line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{$t('trade.buy_down')}}</u-button>
											</view>
										</view>
									</view>
								</u-form-item>
							</u--form>
						</scroll-view>
					</swiper-item>

					<swiper-item>
						<scroll-view scroll-y="true" enable-flex="true" class="section-area">
							<u--form labelPosition="left" :model="model1" ref="uForm">
								<u-form-item ref="item1">
									<view
										:style="$store.state.bgColor=='black'?'width: 98%; margin-top: 12px; font-size: 12px; color: #ffffff;':'width: 98%; margin-top: 12px; font-size: 12px;'">


										<view
											style="margin-top: 0px; margin-bottom: 20px; width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view style="font-weight: bold;">{{i18n.price}}</view>
											</view>


											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-input inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" inputAlign="center" v-model="target_price" :min="1"
														button-size="70"></u-input></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-input inputWidth="100%" inputAlign="center"
														v-model="target_price" iconStyle="" bgColor="#FFFFFF" :min="1"
														button-size="70"></u-input></view>
											</view>
										</view>
										<!-- <view
											style="display: flex; justify-content: center; align-items: center; width: 100%;"
											@click="gshows = true">
											<view style="flex: 1;font-size: 12px; font-weight: bold;">
												{{i18n.multiplier}}
											</view>
											<view style="width: 50%;" v-if="$store.state.bgColor=='black'"><u--input
													v-model="multiple1" :disabled="true" disabledColor="#222"
													placeholder="" inputAlign="center" style="height: 25px;"
													class="bsone" color="#ffffff" suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold; "></u--input>

											</view>
											<view style="width: 50%;" v-if="$store.state.bgColor=='while'">
												<u--input v-model="multiple1" :disabled="true" disabledColor="#ffffff"
													placeholder="" inputAlign="center" style="height: 25px;"
													suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
											</view>
										</view> -->
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="ghyzs" activeColor="#0166fc"
														size="40" :min="0" inactiveColor="#424656">
													</u-switch></view>
											</view>
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="ghyzs" activeColor="#0166fc"
														size="40" :min="0" inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='black'" class="bnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(4,2)">
														<u-icon name="minus" size="28" :color="ghyzsc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="ghyzsc" v-model="gstop_loss_price"
														:min="0" :disabled="ghyzsd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(4,1)">
														<u-icon name="plus" size="28" :color="ghyzsc"></u-icon>
													</view>
												</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(4,2)">
														<u-icon name="minus" size="28" :color="ghyzsc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="ghyzsc" v-model="gstop_loss_price"
														:min="0" :disabled="ghyzsd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(4,1)">
														<u-icon name="plus" size="28" :color="ghyzsc"></u-icon>
													</view>
												</view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="ghyzy" activeColor="#0166fc"
														size="40" inactiveColor="#424656">
													</u-switch></view>
											</view>

											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="ghyzy" activeColor="#0166fc"
														size="40" inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>
											<view style="width: 52%;" v-if="$store.state.bgColor=='black'" class="bnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(5,2)">
														<u-icon name="minus" size="28" :color="ghyzyc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="ghyzyc"
														v-model="gtarget_profit_price" :min="0"
														:disabled="ghyzyd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(5,1)">
														<u-icon name="plus" size="28" :color="ghyzyc"></u-icon>
													</view>
												</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(5,2)">
														<u-icon name="minus" size="28" :color="ghyzyc"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														inputAlign="center" :color="ghyzyc"
														v-model="gtarget_profit_price" :min="0"
														:disabled="ghyzyd"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(5,1)">
														<u-icon name="plus" size="28" :color="ghyzyc"></u-icon>
													</view>
												</view>
											</view>

										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.buy_quantity}}</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='black'" class="bnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(6,2)">
														<u-icon name="minus" size="28"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														bgColor="#12151B" inputAlign="center" color="#fff"
														v-model="gnumber" :min="0"
														:max="symbolQuotation.lever_max_share"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(6,1)">
														<u-icon name="plus" size="28"></u-icon>
													</view>
												</view>
											</view>

											<view style="width: 52%;" v-if="$store.state.bgColor=='while'" class="wnss">
												<view class="bnss-flex">
													<view class="bnss-flex-li" @click="SetMoney(6,2)">
														<u-icon name="minus" size="28"></u-icon>
													</view>
													<u--input class="bnss-flex-input" inputWidth="100%"
														bgColor="#12151B" inputAlign="center" color="#000"
														v-model="gnumber" :min="0"
														:max="symbolQuotation.lever_max_share"></u--input>
													<view class="bnss-flex-li" @click="SetMoney(6,1)">
														<u-icon name="plus" size="28"></u-icon>
													</view>
												</view>
											</view>
										</view>

										<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
											v-if="$store.state.bgColor=='while'"></view>
										<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
											v-if="$store.state.bgColor=='black'"></view>

										<view style="width: 100%; font-size: 12px; font-weight: bold;">
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.each}}</view>
												<view>
													<!-- {{i18n.sheet}}={{each_piece}}{{alias_currency_name}} -->
													{{i18n.sheet}} = 500 {{alias_currency_name}}
												</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_commission}}</view>
												<view>{{ ghandlingFee }}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_margin}}</view>
												<view>{{gmargin}}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.balance}}</view>
												<view>
													<!-- {{users.lever_wallet.balance[0].lever_balance}} -->
													{{users.usdt}}
												</view>
											</view>
										</view>
										<view style="width: 100%; margin-bottom: 30px;">
											<view style="display: flex; width: 100%; padding-top: 10px;">
												<!--#ifndef APP-PLUS-->
												<u-button :loading="gdloading" :disabled="gmrdis" loadingText=""
													@click="orderClicks(1,0)" style="background: #0166fc;  border-radius: 100rpx;
															    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_up}}</u-button>
												<!--#endif -->
												<!--#ifdef APP-PLUS-->
												<button @click="orderAppClicks()" style="background: #0166fc;  border-radius: 100rpx;
															    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_down}}</button>
												<!--#endif -->
												<u-button :loading="gdmloading" :disabled="gmcdis" loadingText=""
													@click="orderClicks(2,0)"
													style="background: #F23C48; border-radius: 100rpx;
															    border: 0;  width: 49%; height: 35px;
													line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{$t('trade.buy_down')}}</u-button>
											</view>
										</view>
									</view>
								</u-form-item>
							</u--form>
						</scroll-view>
					</swiper-item>
				</swiper>

			</view>
			<view v-show="currentNav == 1"
				:style="$store.state.bgColor=='black'?'padding: 10px; color:#fff':'padding: 10px;'">
				<view
					style="margin-top: 15px; margin-bottom: 20px; width: 98%; display: flex; justify-content: center; align-items: center;">
					<view style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
						<view style="font-weight: bold; text-align:center; font-size: 12px;">{{i18n.open_number}}</view>
					</view>
					<view style=" width: 50%;" v-if="$store.state.bgColor=='black'" class="bnss">
						<view><u-input inputWidth="100%" iconStyle="" v-model="kcnumber" color="#fff" bgColor="#12151B"
								:min="1" button-size="70"></u-input></view>
					</view>
					<view style=" width: 50%;" v-if="$store.state.bgColor=='while'" class="wnss">
						<view><u-input inputWidth="100%" iconStyle="" v-model="kcnumber" bgColor="#FFFFFF" :min="1"
								button-size="70"></u-input></view>
					</view>
				</view>
				<view style="display: flex; justify-content: center; align-items: center; width: 98%;"
					@click="kcshows = true">
					<view style="flex: 1;font-size: 12px; font-weight: bold; ">{{i18n.opening_time}}</u-icon></view>
					<view style="width: 50%;" v-if="$store.state.bgColor=='black'"><u--input v-model="kcvalues"
							:disabled="true" disabledColor="#222" placeholder="" inputAlign="center" color="#fff"
							class="bsone" style="height: 25px;" suffixIcon="arrow-right"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input></view>
					<view style="width: 50%;" v-if="$store.state.bgColor=='while'"><u--input v-model="kcvalues"
							:disabled="true" disabledColor="#ffffff" placeholder="" inputAlign="center"
							style="height: 25px;" suffixIcon="arrow-right"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input></view>
				</view>
				<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
					v-if="$store.state.bgColor=='while'"></view>
				<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
					v-if="$store.state.bgColor=='black'"></view>

				<view style="width: 100%; font-size: 12px; margin-top: 10px; font-weight: bold;">

					<view style="display: flex; height: 40px; line-height: 40px;">
						<view style="flex: 1;">{{i18n.profit_rate}}</view>
						<view>{{profit_ratio}}%</view>
					</view>
					<view style="display: flex; height: 40px; line-height: 40px;">
						<view style="flex: 1;">{{i18n.estimated_commission}}</view>
						<view>{{mhyprices}}</view>
					</view>
					<view style="display: flex; height: 35px; line-height: 35px;">
						<view style="flex: 1;">{{i18n.balance}}</view>
						<view>
							<!-- {{users.lever_wallet.balance[0].lever_balance}} -->
							{{users.usdt}}
						</view>
					</view>
				</view>
				<view style="width: 100%; ">
					<view style="display: flex; width: 100%; padding-top: 10px;">
						<u-button :loading="mhydloading" :disabled="mhymrdis" loadingText="" @click="mhyOrder(1)" style=" border-radius: 100rpx;background: #0166fc;
											    border: 0;  width:49%; font-size: 12px; height: 45px; color: #FFFFFF;
							line-height: 45px;">{{i18n.buy_up}}</u-button>
						<u-button :loading="mhymloading" :disabled="mhymcdis" loadingText="" @click="mhyOrder(2)" style=" border-radius: 100rpx;background: #F23C48;
											    border: 0;  width: 49%; height: 45px;
							line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{i18n.buy_down}}</u-button>
					</view>
				</view>
			</view>

		</view>
		<u-picker :show="shows" v-if="$store.state.bgColor=='black'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="cancel" @confirm="confirm" @change="changeHandler"
			style="border-radius: 18px; background: #222;" class="bnsss">

		</u-picker>
		<u-picker :show="shows" v-if="$store.state.bgColor=='while'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="cancel" @confirm="confirm" @change="changeHandler"
			style="border-radius: 18px; background: #fff;" class="wnsss">

		</u-picker>

		<u-picker :show="gshows" v-if="$store.state.bgColor=='black'" ref="guPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="gcancel" @confirm="gconfirm" @change="changeHandler"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker :show="gshows" v-if="$store.state.bgColor=='while'" ref="guPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="gcancel" @confirm="gconfirm" @change="changeHandler"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-picker :show="kcshows" ref="uPickers" v-if="$store.state.bgColor=='black'" :itemHeight="100"
			:defaultIndex="kcdfindex" :columns="kccolumns" keyName="seconds" @cancel="kccancel" @confirm="kcconfirm"
			@change="changeHandler" style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>
		<u-picker :show="kcshows" ref="uPickers" v-if="$store.state.bgColor=='while'" :itemHeight="100"
			:defaultIndex="kcdfindex" :columns="kccolumns" keyName="seconds" @cancel="kccancel" @confirm="kcconfirm"
			@change="changeHandler" style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-popup :show="ordershow" :round="30" :duration="300" :closeable="true" @close="close" @open="open">
			<view
				:style="$store.state.bgColor=='black'?'height:330px;  padding: 6px;background: #22252F; color: #fff;':''">
				<view class="u-content" style="text-align: center;">
					<view class="u-content__circle"
						:style="this.$store.state.bgColor=='black'?'background: #22252F;':''">
						<u--image
							:src="$store.state.bgColor=='black'?'../../static/<EMAIL>':'../../static/<EMAIL>'"
							width="140" style="margin: 0 auto;"></u--image>
					</view>
					<view
						style="margin-top: 40px; padding-left: 10px; font-size: 18px; font-weight: bold;margin: 0 auto;">
						{{i18n.order_confirmed}}
					</view>
					<view
						style="margin-top: 30px;display: flex;flex-direction: column;align-items: center;justify-content: center;">
						<button @click="can()" style="background: #0166fc;border-radius: 100px;
									border: 0;  width: 80%; height: 40px;
						line-height: 40px;  font-size: 12px;flex: 1;color: #FFFFFF;">{{i18n.confirm}}</button>
						<button @click='orders()' style="background: #FFFFFF; border-radius: 100px;
								border: 0;  flex: 1; width:80%; font-size: 12px; height: 40px; color: #222222;margin-top: 20px;
						line-height: 40px;">{{i18n.view_order}}</button>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="zbshow" mode="bottom" @close="zbclose" @open="zbopen">
			<!-- <view style="display: flex;flex-direction: column; background-color:#282A37;padding-bottom: 100rpx;"> -->
			<view
				:style="$store.state.bgColor=='black'?'display: flex;flex-direction: column; background-color:#22252F;color:#fff;padding-bottom: 100rpx;':'display: flex;flex-direction: column; background-color:#fff;padding-bottom: 100rpx;'">
				<view style="color: #222;font-weight: bold; margin-left: 32rpx;margin-top: 60rpx;margin-bottom: 30rpx;">
					<!-- 主图 -->
				</view>
				<view style="display: flex;flex-direction: row; flex-wrap: wrap;margin-left: 32rpx; color:#fff">
					<view :class="item.select?'itemview_':'itemview'" v-for="(item,index) in mainIndicaList"
						:style="$store.state.bgColor=='black'?'color:#fff':''" @click="ChangeIndex(0,item.name,item)">
						{{item.name}}
					</view>
					<!-- <view :class="ckone=='BOLL'?'itemview_':'itemview'" @click="ChangeIndex(0,'BOLL')">BOLL</view>
						  <view :class="ckone=='SAR'?'itemview_':'itemview'" @click="ChangeIndex(0,'SAR')">SAR</view>
						  <view :class="ckone=='EMA'?'itemview_':'itemview'" @click="ChangeIndex(0,'EMA')">EMA</view>
						   <view :class="ckone=='BBI'?'itemview_':'itemview'" @click="ChangeIndex(0,'BBI')">BBI</view> -->
				</view>


				<view style="color: #222;font-weight: bold;margin-left: 32rpx;margin-top: 60rpx;margin-bottom: 30rpx;">
					<!-- 指标 -->
				</view>
				<view style="display: flex;flex-direction: row; flex-wrap: wrap;margin-left: 32rpx;">
					<!-- <view :class="item.select?'itemview_':'itemview'">BOLL -->

					<view :class="item.select?'itemview_':'itemview'" v-for="(item,index) in indicaIndexList"
						:style="$store.state.bgColor=='black'?'color:#fff':''" @click="ChangeIndex(1,item.name,item)">
						{{item.name}}
					</view>
					<!-- 	<view :class="cktwo=='MACD'?'itemview_':'itemview'" style="display: none;" @click="ChangeIndex(1,'MACD')">MACD</view>
						<view :class="cktwo=='KDJ'?'itemview_':'itemview'" @click="ChangeIndex(1,'KDJ')">KDJ</view>
						<view :class="cktwo=='BIAS'?'itemview_':'itemview'" @click="ChangeIndex(1,'BIAS')">BIAS</view>
						<view :class="cktwo=='RSI'?'itemview_':'itemview'" @click="ChangeIndex(1,'RSI')">RSI</view>
						<view :class="cktwo=='OBV'?'itemview_':'itemview'" @click="ChangeIndex(1,'OBV')">OBV</view>
				<view :class="cktwo=='BRAR'?'itemview_':'itemview'" @click="ChangeIndex(1,'BRAR')">BRAR</view>
				<view :class="cktwo=='ROC'?'itemview_':'itemview'" @click="ChangeIndex(1,'ROC')">ROC</view>
				<view :class="cktwo=='DMA'?'itemview_':'itemview'" @click="ChangeIndex(1,'DMA')">DMA</view>
				<view :class="cktwo=='TRIX'?'itemview_':'itemview'" @click="ChangeIndex(1,'TRIX')">TRIX</view>
				<view :class="cktwo=='DMI'?'itemview_':'itemview'" @click="ChangeIndex(1,'DMI')">DMI</view>
				<view :class="cktwo=='CCI'?'itemview_':'itemview'" @click="ChangeIndex(1,'CCI')">CCI</view>
				<view :class="cktwo=='CR'?'itemview_':'itemview'" @click="ChangeIndex(1,'CR')">CR</view>
				<view :class="cktwo=='PSY'?'itemview_':'itemview'" @click="ChangeIndex(1,'PSY')">PSY</view>
				<view :class="cktwo=='VR'?'itemview_':'itemview'" @click="ChangeIndex(1,'VR')">VR</view>
				<view :class="cktwo=='WR'?'itemview_':'itemview'" @click="ChangeIndex(1,'WR')">WR</view>
				<view :class="cktwo=='MTM'?'itemview_':'itemview'" @click="ChangeIndex(1,'MTM')">MTM</view>
				<view :class="cktwo=='EMV'?'itemview_':'itemview'" @click="ChangeIndex(1,'EMV')">EMV</view> -->

				</view>

			</view>
		</u-popup>
		<u-popup :show="bzsshow" mode="left" @close="bzsclose" @open="bzsopen" @touchmove.stop.prevent
			customStyle="border-radius: 0px !important; overflow-y: scroll;overscroll-behavior: none">
			<view style=" " @click="showBZ(item,true)" :class="currency_id==item.id?'showItems':'noShowItems'"
				v-for="(item,index) in indicaList">
				<view>{{item.name}}</view>
			</view>
		</u-popup>
		<tab-bar></tab-bar>
	</view>
</template>

<script>
	// #ifdef H5
	import HQChart from '@/uni_modules/jones-hqchart2/js_sdk/umychart.uniapp.h5.js'
	// #endif

	// #ifndef H5
	import {
		JSCommon
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js'
	import {
		JSCommonHQStyle
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.style.wechat.js'
	import {
		JSConsole
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.console.wechat.js'

	//禁用日志
	JSConsole.Complier.Log = () => {};
	JSConsole.Chart.Log = () => {};
	// #endif

	import {
		mapState
	} from 'vuex'



	var pako = require('pako');

	function DefaultData() {}

	DefaultData.GetKLineOption = function() {
		let data = {
			Type: '历史K线图',
			//Type: '历史K线图横屏',

			Windows: //窗口指标
				[{
						Index: "EMPTY",
						Modify: false,
						Change: false,
						Close: false,
						TitleHeight: 0
					},
					// {
					// 	Index: "VOL",
					// 	Modify: false,
					// 	Change: false,
					// 	Close: false
					// }
					// {Index:"MACD2",Modify: false, Change: false,Close:false}
				],
			OverlayIndexFrameWidth: 50,
			IsAutoUpdate: false, //是自动更新数据(不自动更新由外部更新)
			IsApiPeriod: true, //使用Api计算周期
			IsCorssOnlyDrawKLine: true,
			CorssCursorTouchEnd: false,
			IsClickShowCorssCursor: true,
			EnableScrollUpDown: true,
			CorssCursorInfo: {
				// Left: 0,
				// Right: 0,
				// Bottom: 0,
				IsShowCorss: true
			},
			Language: "EN",
			Border: //边框
			{
				Left: 1,
				Right: 62, //右边间距
				Top: 30,
				Bottom: 25,
			},

			KLine: {
				Right: 0, //复权 0 不复权 1 前复权 2 后复权
				Period: 0, //周期: 0 日线 1 周线 2 月线 3 年线
				PageSize: 46,
				IsShowTooltip: false,
				DrawType: 0,
			},
			KLineTitle: {
				IsShowName: false,
				IsShowSettingInfo: false,
				IsShowDateTime: false,
				IsShowTime: false,
				IsClose: false,
				IsYFClose: false
			},
			Frame: //子框架设置
				[{
						SplitCount: 8,
						IsShowLeftText: false,
						SplitType: 1,
						Custom: [{
							Type: 0,
							Position: 'right'
						}]
					},
					{
						SplitCount: 4,
						IsShowLeftText: false,
						SplitType: 1
					},
					{
						SplitCount: 4,
						IsShowLeftText: false
					}
				],

			ExtendChart: [{
					// Name: 'KLineTooltip'
				}, //开启手机端tooltip
			],

		};

		return data;
	}

	DefaultData.GetMinuteOption = function() {
		let data = {
			Type: '历史K线图',
			//Type: '历史K线图横屏',

			Windows: //窗口指标
				[{
						Index: "EMPTY",
						Modify: false,
						Change: false,
						Close: false,
						TitleHeight: 0
					},
					{
						Index: "VOL",
						Modify: false,
						Change: false,
						Close: false
					},
					{
						Index: "BOLL",
						Modify: false,
						Change: false,
						Close: false
					},

				],
			OverlayIndexFrameWidth: 50,
			IsAutoUpdate: false, //是自动更新数据(不自动更新由外部更新)
			IsApiPeriod: true, //使用Api计算周期
			IsCorssOnlyDrawKLine: true,
			CorssCursorTouchEnd: true,
			IsClickShowCorssCursor: false,
			EnableScrollUpDown: true,
			CorssCursorInfo: {
				Left: 0,
				Right: 0,
				Bottom: 0,
				IsShowCorss: false
			},
			Border: //边框
			{
				Left: 1,
				Right: 1, //右边间距
				Top: 1,
				Bottom: 25,
			},
			IsShowCorssCursorInfo: false,
			KLine: {
				Right: 0, //复权 0 不复权 1 前复权 2 后复权
				Period: 4, //周期: 0 日线 1 周线 2 月线 3 年线
				PageSize: 160,
				IsShowTooltip: false,
				DrawType: 4,
			},

			KLineTitle: {
				IsShowName: false,
				IsShowSettingInfo: false,
			},

			Frame: //子框架设置
				[{
						SplitCount: 8,
						IsShowLeftText: false,
						SplitType: 1,
						Custom: [{
							Type: 0,
							Position: 'right'
						}]
					},

					{
						SplitCount: 4,
						IsShowLeftText: false,
						SplitType: 1
					},
					{
						SplitCount: 4,
						IsShowLeftText: false
					}
				],

			ExtendChart: [
				// {Name:'KLineTooltip' },	//开启手机端tooltip
			],

		};

		return data;
	}

	DefaultData.GetDepthOption = function() {
		var option = {
			Type: '深度图', //创建图形类型
			EnableZoomUpDown: {
				//Wheel:false,
				//Keyboard:false,
				//Touch:false,
			},
			IsAutoUpdate: false, //是自动更新数据
			AutoUpdateFrequency: 10000, //数据更新频率
			//CorssCursorTouchEnd:true,
			EnableScrollUpDown: true,

			MaxVolRate: 1.2,

			CorssCursorInfo: {
				HPenType: 0,
				VPenType: 1,
				IsShowTooltip: true
			},

			Listener: {
				//KeyDown:false,
				//Wheel:false
			},

			SplashTitle: "下载数据 ......",
			Language: "EN",

			Border: //边框
			{
				Left: 1, //左边间距
				Right: 1, //右边间距
				Bottom: 25, //底部间距
				Top: 1 //顶部间距
			},

			//框架设置
			Frame: {
				SplitCount: 2,
				IsShowLeftText: false,
				XLineType: 3,
				XSplitCount: 2
			},

		};

		return option;
	}

	var KLINE_CHART_TYPE = {
		KLINE_ID: 1, //K线图
		MINUTE_KLINE_ID: 2 //K线面积图
	};

	var g_KLine = {
		JSChart: null
	}

	export default {
		data() {
			let data = {
				scrollH: 600,
				// 顶部选项卡
				scrollInto: "",
				tabIndex: 0,
				isHide: false,
				scrollHight1: 500,
				scrollHight2: 500,
				scrollHight3: 500,
				navs: [],
				currentNav: 0,
				currentNavcc: 0,
				indicatorList: ['VOL'],
				miansList: [],
				navsList: [],
				newsList: [],
				bgStatusOne: '',
				bgStatusTwo: '',
				indexList: [],
				show: false,
				timer: null,
				values: '',
				each_piece: 1,
				mainIndicaList: [{
						name: 'MA',
						select: false
					},
					{
						name: 'BOLL',
						select: false
					},
					{
						name: 'SAR',
						select: false
					},
					{
						name: 'EMA',
						select: false
					},
					{
						name: 'BBI',
						select: false
					}
				],
				indicaIndexList: [{
						name: 'VOL',
						select: true
					},
					{
						name: 'MACD',
						select: false
					},
					{
						name: 'KDJ',
						select: false
					},
					{
						name: 'BIAS',
						select: false
					},
					{
						name: 'RSI',
						select: false
					},
					{
						name: 'OBV',
						select: false
					},
					{
						name: 'BRAR',
						select: false
					},
					{
						name: 'ROC',
						select: false
					},
					{
						name: 'DMA',
						select: false
					},
					{
						name: 'TRIX',
						select: false
					},
					{
						name: 'DMI',
						select: false
					},
					{
						name: 'CCI',
						select: false
					},
					{
						name: 'CR',
						select: false
					},
					{
						name: 'PSY',
						select: false
					}, {
						name: 'VR',
						select: false
					}, {
						name: 'WR',
						select: false
					},
					{
						name: 'MTM',
						select: false
					},
					{
						name: 'EMV',
						select: false
					}
				],
				kcvalues: '',
				kcnumber: 1,
				showSex: false,
				value11: false,
				ordershow: false,
				dfindex: [0],
				kcdfindex: [0],
				isShow: 0,
				hyzs: false,
				hyzy: false,
				hyzsd: true,
				hyzyd: true,
				hyzyc: "#BDC1D0",
				hyzsc: "#BDC1D0",
				ghyzs: false,
				ghyzy: false,
				ghyzsd: true,
				ghyzyd: true,
				xdloading: false,
				xdmloading: false,
				mrdis: false,
				mcdis: false,
				gdloading: false,
				gdmloading: false,
				gmrdis: false,
				gmcdis: false,

				mhydloading: false,
				mhymloading: false,
				mhymrdis: false,
				mhymcdis: false,
				dhbts: 'dhbts',
				sdhbts: 'sdhbts',
				showItemId: 0,


				pids: 4,
				ghyzyc: "#BDC1D0",
				ghyzsc: "#BDC1D0",
				target_profit_price: 0,
				stop_loss_price: 0,
				gtarget_profit_price: 0,
				gstop_loss_price: 0,
				profit_ratio: 0,
				mhyprices: 0,
				user_lever: 0, // 个人的资金
				margin: 0, //保证金
				handlingFee: 0, //手续费,
				micro_trade_fee: 0, //手续费,
				gmargin: 0, //保证金
				ghandlingFee: 0, //手续费,
				symbolQuotation: {},
				target_price: '',
				currency_name: "",
				alias_currency_name: "",
				originalQuotation: [],
				zbshow: false,
				quotation: [],
				ckone: "MA",
				cktwo: "VOL",
				legal_name: "",
				currency_id: "",
				buy_direction: "",
				currentIndex: "",
				bzsshow: false,
				indicaList: [],
				price: '',
				multiple0: 0, //倍数
				multiple1: 0, //倍数
				number: 0.1, //交易手数,
				gnumber: 0.1, //交易手数,
				model1: {
					userInfo: {
						name: 'uView UI',
						sex: '',
					},
				},
				shows: false,
				gshows: false,
				kcshows: false,
				columns: [
					// ['100', '200', '300']
				],
				kccolumns: [
					// ['60s', '300s', '900s']
				],
				Symbol: 'XAUUSD.BIT',
				symboles: '',
				OriginalSymbol: 'XAUUSD',
				ChartWidth: 300,
				ChartHeight: 300,
				opening: 1,
				dataes: [],
				msg: null,
				mhytimeList: [],
				mhytimeArray: [],
				isLoding: "",
				mhyfee: 0,
				mhyusers: {},
				KLine: {
					Option: DefaultData.GetKLineOption(),
				},

				// WSUrl:'wss://www.huobi.com/-/s/pro/ws',	//火币api地址, 需要根据火币网经常调整. 会经常变(https://www.huobi.br.com/en-us/exchange/btc_usdt/)
				// WSUrl:'ws://*************:24419',
				// WSUrl:'wss://www.https://zonhoutai.forexglobal.vip/socket.io/?EIO=3&transport=websocket',

				SocketOpen: false,
				LastSubString: null, //最后一个订阅的数据
				intervals: {
					"1min": "1",
					"5min": "5",
					"15min": "15",
					"30min": "30",
					"60min": "60",
					"1day": "360",
					"1week": "1800",
					"1mon": "10800",
					"1year": "129600"
				},
				periods: {
					"4": "1min",
					"5": "5min",
					"6": "15min",
					"7": "30min",
					"8": "60min",
					"0": "1day",
					"1": "1week",
					"2": "1mon",
					"3": "1year"
				},

				Update: {
					EnableLimit: false, //更新是否限速
					LastUpdateTime: null,
					Frequency: 5000, //更新频率
					Cache: null,
				},
				topPrice: 0, // 顶部价格仅由K线驱动
				priceUpdateTimer: null, // 价格更新定时器
				lastPriceUpdateTime: 0, // 上次价格更新时间
			};

			return data;

		},

		name: 'KLineChart',
		watch: {
			currency_name: function(val) {

			},
			'$store.state.bgColor'(val) {
				this.bgStatusOne = val
			},
			'$store.state.currency'(val) {
				this.currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
				this.alias_currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
				this.legal_name = this.$store.state.currency.legal_name || 'USDT'
				this.currency_id = this.$store.state.currency.currency_id || 114
				this.currentIndex = this.$store.state.currency.currentIndex || 0
				// let buy_direction = buy_direction || 0
				this.buyDirection = 0
				// 	 this.timer = setTimeout(() => {
				// 	       this.getQuotation()
				// 	 this.getDatas()
				// 	 	}, 5400)
				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id,
					"alias": this.alias_currency_name
				}
				// this.ChangePeriod(4)
				this.showBZ(item)
			},
			currentNav: function(val) {
				if (val == 1) {
					// if(this.mhytimeList.length>0){
					// 	return
					// }
					this.mhytimeList = []
					var that = this
					const res = this.$H.get('/api/v1/seconds', false).then(res => {
						if (res.type == "ok") {
							res.data.data.map(item => {
								this.mhytimeList.push({
									"id": item.id,
									"seconds": item.seconds,
									"profit_ratio": item.profit_ratio,
									"mhyprices": res.data.fee / 100,
									"fee": res.data.fee / 100
								})
								// const pram=item.seconds
								//      this.mhytimeArray.push({
								//   pram:item.profit_ratio
								//   })
							})
							this.kccolumns = []
							this.kccolumns.push(this.mhytimeList)
							this.kcvalues = this.kccolumns[0][0].seconds
							this.profit_ratio = this.kccolumns[0][0].profit_ratio
							this.mhyprices = this.kccolumns[0][0].mhyprices
							this.mhyfee = this.kccolumns[0][0].fee
						}
					})
				}
			},
			kcnumber: function(val) {
				this.mhyprices = this.mhyfee * val
			},
			kcvalues: function(val) {
				if (this.mhytimeList.length > 0) {
					this.mhytimeList.map(item => {
						if (val == item.seconds) {
							this.profit_ratio = item.profit_ratio
							this.mhyprices = item.mhyprices
							this.mhyfee = item.fee
						}
					})
				}
			},
			symbolQuotation: function(val) {
				this.calc()
				this.gcalc()
			},
			number: function(val) {
				this.calc()
			},
			multiple0: function(val) {

				this.calc()
			},
			gnumber: function(val) {

				if (val == "") {
					this.gnumber == 0
				}
				this.gcalc()
			},
			multiple1: function(val) {

				this.gcalc()
			},
			hyzs: function(val) {

				if (!val) {
					this.hyzsd = true
					this.hyzsc = "#BDC1D0"


					// hyzyc:"#BDC1D0",
				} else {
					this.hyzsd = false
					this.hyzsc = "#222222"
					if (this.stop_loss_price == 0) this.stop_loss_price = this.symbolQuotation.now_price
				}



			},
			hyzy: function(val) {
				if (!val) {
					this.hyzyd = true
					this.hyzyc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.hyzyd = false
					this.hyzyc = "#222222"
					if (this.target_profit_price == 0) this.target_profit_price = this.symbolQuotation.now_price

				}
			},
			ghyzs: function(val) {
				if (!val) {
					this.ghyzsd = true
					this.ghyzsc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.ghyzsd = false
					this.ghyzsc = "#222222"
					if (this.gstop_loss_price == 0) this.gstop_loss_price = this.symbolQuotation.now_price

				}



			},
			ghyzy: function(val) {
				if (!val) {
					this.ghyzyd = true
					this.ghyzyc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.ghyzyd = false
					this.ghyzyc = "#222222"
					if (this.gtarget_profit_price == 0) this.gtarget_profit_price = this.symbolQuotation.now_price

				}
			}
		},
		onLoad(options) {
			uni.setNavigationBarTitle({
				title: 'priectw'

			})
			const {
				i18n
			} = this
			this.navs.push(i18n.contracts)
			this.navs.push(i18n.second_contract)
			this.navsList.push(i18n.trade)
			this.navsList.push(i18n.pending_order)
			this.isHide = false
			uni.$on('page-popup', (data) => {
				this.msg = data.msg;
				this.subNames()
			});
			let {
				currency_name,
				legal_name,
				currency_id,
				buy_direction,
				currentIndex
			} = options


			let getCurrency = uni.getStorageSync('currency')
			if (this.isLoding == "") {
				this.currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
				this.alias_currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
				this.legal_name = this.$store.state.currency.legal_name || 'USDT'
				this.currency_id = this.$store.state.currency.currency_id || 114
				this.currentIndex = this.$store.state.currency.currentIndex || 0

				if (getCurrency) {
					this.alias_currency_name = getCurrency.alias
					this.legal_name = getCurrency.legal_name
					this.currency_name = getCurrency.currency_name
					this.currency_id = getCurrency.currency_id
				}
				// let buy_direction = buy_direction || 0
				this.buyDirection = 0
				// 	 this.timer = setTimeout(() => {
				// 	       this.getQuotation()
				// 	 this.getDatas()
				// 	 	}, 5400)
				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id,
					"alias": this.alias_currency_name
				}
				// this.ChangePeriod(4)
				uni.getSystemInfo({
					success: (res) => {
						this.$nextTick(() => {
							this.isLoding = "loading"
						})
					}
				});
				this.showBZ(item)
			} else {
				// if(this.currency_name==this.$store.state.currency.currency_name || !this.$store.state.currency.currency_name || !bol) return
				if (this.currency_name == "") {
					this.currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
					this.alias_currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
					this.legal_name = this.$store.state.currency.legal_name || 'USDT'
					this.currency_id = this.$store.state.currency.currency_id || 114
					this.currentIndex = this.$store.state.currency.currentIndex || 0
				} else {
					if (this.$store.state.currency.currency_name) {
						if (this.currency_name != this.$store.state.currency.currency_name) {
							this.currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
							this.alias_currency_name = this.$store.state.currency.currency_name || 'XAUUSD'
							this.legal_name = this.$store.state.currency.legal_name || 'USDT'
							this.currency_id = this.$store.state.currency.currency_id || 114
							this.currentIndex = this.$store.state.currency.currentIndex || 0
						}
					}
				}
				if (getCurrency) {
					this.alias_currency_name = getCurrency.alias
					this.legal_name = getCurrency.legal_name
					this.currency_name = getCurrency.currency_name
					this.currency_id = getCurrency.currency_id
				}
				this.buyDirection = 0
				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id,
					"alias": this.alias_currency_name
				}
				this.showBZ(item)
			}

			// const {i18n} = this
			// this.navs=[]
			// this.navsList=[]
			// this.navs.push(i18n.contracts)
			// this.navs.push(i18n.second_contract)


			// this.navsList.push(i18n.trade)
			// this.navsList.push(i18n.pending_order)

			// this.showBZ(item)

			// this.multiple0 = this.columns[0][0]
			// this.kcvalues = this.kccolumns[0][0]
			// if(!currency_name)
			// {
			// 	 this.ClearChart();

			// }

			// this.getDatas()
			// 	if(this.symbolQuotation.length==0)
			// 	{



			// 	}

		},

		computed: {
			i18n() {
				return this.$t("trade")
			},
			...mapState({
				socket: state => state.socket,
				token: state => state.token,
				users: state => state.users,
				currency: state => state.currency
			})
		},
		onReady() {
			// console.log("[KLineChart::onReady]");
			this.$nextTick(() => {
				// #ifndef H5
				this.OnSize();
				this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 8);
				// #endif
			});
		},
		onShow() {
			this.$store.commit('changeTabbarIndex', 2)
			this.auths(() => {})
			const {
				i18n
			} = this
			this.navs = []
			this.navsList = []
			this.navs.push(i18n.contracts)
			this.navs.push(i18n.second_contract)
			this.navsList.push(i18n.trade)
			this.navsList.push(i18n.pending_order)

			if (this.$store.state.currency.opening || this.$store.state.currency.opening == 0) {

				if (this.currency_name == this.$store.state.currency.currency_name) {
					this.opening = this.$store.state.currency.opening
				}
			}
			if (!this.opening) {
				this.isShowes(true)
			} else {
				this.isShowes(false)
			}
			let bol = true
			this.getDatas()
			if (!this.bgStatusOne) {
				this.bgStatusOne = this.$store.state.bgColor
			}
			if (this.bgStatusTwo != this.bgStatusOne) {
				bol = false
				if (g_KLine.JSChart) this.ClearChart()
			}
			this.bgStatusTwo = this.$store.state.bgColor
			uni.getSystemInfo({
				success: (res) => {
					var width = res.windowWidth;
					var height = res.windowHeight;
					this.ChartWidth = width - 15;
					// this.ChartHeight=height-130;
					this.ChartHeight = 450;
					this.$nextTick(() => {
						this.getQuotation()
						// if (g_KLine.JSChart) return;
						this.InitalHQChart();
						this.OnSize();
						this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 4);
					})
				}
			})

			// this.ChangeSymbol(this.currency_name)
			// // this.startSocket()
			// uni.getSystemInfo({
			//     success:  (res) =>
			// 	{
			// 		var width=res.windowWidth;
			// 		var height=res.windowHeight;
			//         this.ChartWidth=width-20;
			// 		this.ChartHeight=559;
			// 		this.$nextTick(()=>
			// 		{
			// 			this.InitalHQChart();
			// 			this.OnSize();
			// 			this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 4);
			// 		})
			//     }
			// });
		},
		onHide() {
			this.isHide = true
			// this.isShowes()
			// this.$store.commit('setCurrency',{})
			// this.socket.removeListener('daymarket')
			// this.socket.removeListener('kline')
			// this.ClearChart()
		},

		onUnload() {

			uni.$off('page-popup')
			this.isHide = false
			// 统一使用off方法清理WebSocket监听器
			this.socket.off('daymarket')
			this.socket.off('kline')
			// 清理定时器（如果存在）
			if (this.priceUpdateTimer) {
				clearInterval(this.priceUpdateTimer)
				this.priceUpdateTimer = null
			}
			this.ClearChart()
		},

		methods: {
			// 获取当前价格的备用方法
			getCurrentPrice() {
				if (this.originalQuotation && this.originalQuotation.length > 0) {
					const currentCurrency = this.originalQuotation.find(item => item.currency_id == this.currency_id);
					if (currentCurrency && currentCurrency.now_price) {
						return this.$options.filters.setPrecision(currentCurrency.now_price, currentCurrency.precision_length);
					}
				}
				return '--';
			},
			SetMoney(type, item) {
				if (type == 1) {
					if (item == 1 && !this.hyzsd) {
						this.stop_loss_price = Number(this.stop_loss_price) + 1
					} else if (item == 2 && !this.hyzsd) {
						if (this.stop_loss_price > 1) {
							this.stop_loss_price = Number(this.stop_loss_price) - 1
						}
					}
				} else if (type == 2) {
					if (item == 1 && !this.hyzyd) {
						this.target_profit_price = Number(this.target_profit_price) + 1
					} else if (item == 2 && !this.hyzyd) {
						if (this.target_profit_price > 1) {
							this.target_profit_price = Number(this.target_profit_price) - 1
						}
					}
				} else if (type == 3) {
					if (item == 1) {
						this.number = (Number(this.number) + 0.1).toFixed(2)
					} else if (item == 2) {
						if (this.number > 0.1) {
							this.number = (Number(this.number) - 0.1).toFixed(2)
						}
					}
				} else if (type == 4) {
					if (item == 1 && !this.ghyzsd) {
						this.gstop_loss_price = Number(this.gstop_loss_price) + 1
					} else if (item == 2 && !this.ghyzsd) {
						if (this.gstop_loss_price > 1) {
							this.gstop_loss_price = Number(this.gstop_loss_price) - 1
						}
					}
				} else if (type == 5) {
					if (item == 1 && !this.ghyzyd) {
						this.gtarget_profit_price = Number(this.gtarget_profit_price) + 1
					} else if (item == 2 && !this.ghyzyd) {
						if (this.gtarget_profit_price > 1) {
							this.gtarget_profit_price = Number(this.gtarget_profit_price) - 1
						}
					}
				} else if (type == 6) {
					if (item == 1) {
						this.gnumber = (Number(this.gnumber) + 0.1).toFixed(2)
					} else if (item == 2) {
						if (this.gnumber > 0.1) {
							this.gnumber = (Number(this.gnumber) - 0.1).toFixed(2)
						}
					}
				}
			},
			isShowes(bols = true) {
				if (bols) {
					this.mrdis = true,
						this.mcdis = true,

						this.gmrdis = true,
						this.gmcdis = true,


						this.mhymrdis = true,
						this.mhymcdis = true
				} else {
					this.mrdis = false,
						this.mcdis = false,

						this.gmrdis = false,
						this.gmcdis = false,


						this.mhymrdis = false,
						this.mhymcdis = false
				}

			},
			isScs() {
				const _this = this
				const restwo = this.$H.get('/api/v1/optional/list', false).then(restwo => {
					const has = restwo.data.findIndex(item => item.currency_id == this.currency_id)
					if (has > -1) {
						_this.isShow = 1
					} else {
						_this.isShow = 0
					}
				})
			},
			collects() {

				if (this.isShow) {
					this.isShow = 0
					const res = this.$H.post('/api/v1/optional/del', false, {
						currency_id: this.currency_id
					}).then(res => {
						if (res.type == "ok") {
							this.$nextTick(() => {
								this.isShow = 0

							})
						}

					})
				} else {
					this.isShow = 1
					const res = this.$H.post('/api/v1/optional/add', false, {
						currency_id: this.currency_id
					}).then(res => {

						if (res.type == "ok") {
							this.$nextTick(() => {
								this.isShow = 1

							})
						}

					})
				}

			},
			//切换股票
			ChangeSymbol(symbol) {
				if (this.OriginalSymbol == symbol) {
					return
				};
				this.OriginalSymbol = symbol;
				this.Symbol = symbol + '.BIT';
				if (g_KLine.JSChart) {
					g_KLine.JSChart.ChangeSymbol(this.Symbol);
				}
			},
			onChange(e) {

				if (this.gnumber == "") {

					this.gnumber = 0;
				}
			},
			newLoads() {
				this.InitalHQChart();
				this.OnSize();
				this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 4);
			},
			showBZ(item, isshow = false) {
				uni.setStorageSync('currency', {
					currency_id: item.currency_id,
					currency_name: item.currency_name,
					legal_name: item.legal_name,
					alias: item.alias
				})
				this.currency_id = item.currency_id
				this.currency_name = item.currency_name
				this.alias_currency_name = item.alias
				this.legal_name = item.legal_name
				this.symboles = this.currency_name + '/' + this.legal_name
				this.getQuotation()
				if (!isshow) {
					this.getDatas()
				} else {
					this.currentIndex = item.currentIndex
					this.opening = item.opening
					if (this.opening) {
						this.isShowes(false)
					} else {
						this.isShowes(true)
					}
				}
				this.isScs()
				// this.timer = setTimeout(() => {
				// 	// this.originalQuotation=[]
				// 	// this.symbolQuotation={}
				// 	// this.quotation=[]
				// 	this.getQuotation()
				// }, 2600)
				// this.ClearChart();
				// this.socket.removeListener('kline')
				// this.socket.removeListener('daymarket')
				this.bzsshow = false
				this.currentNav = 0
				this.hyzs = false
				this.hyzy = false
				this.hyzsd = true
				this.hyzyd = true
				this.hyzyc = "#BDC1D0"
				this.hyzsc = "#BDC1D0"
				this.ghyzs = false
				this.ghyzy = false
				this.ghyzsd = true
				this.ghyzyd = true
				this.target_profit_price = 0
				this.stop_loss_price = 0
				this.gtarget_profit_price = 0
				this.gstop_loss_price = 0
				this.profit_ratio = 0
				this.mhyprices = 0
				this.kcnumber = 1
				this.ChangeSymbol(this.currency_name)
			},
			getDatas() {

				const res = this.$H.get('/api/v1/quotation_new', false).then(res => {
					this.indicaList = []

					for (var i = 0; i < res.data.length; i++) {

						res.data[i].quotation.map(item => {
							item.currentIndex = i
							if (this.currency_id == 0) {

								this.currency_id = item.currency_id
							}
							const items = {
								"id": item.currency_id,
								"currency_id": item.currency_id,
								"name": item.show_name,
								"currency_name": item.currency_name,
								"legal_name": item.legal_name,
								"currentIndex": item.currentIndex,
								"opening": item.currency.opening,
								"alias": item.currency.alias
							}
							this.indicaList.push(items)


						})
					}

				})
			},
			bzsClick() {
				this.bzsshow = true
			},
			zbclick() {
				this.zbshow = true
			},
			zbopen() {

			},
			zbclose() {
				this.zbshow = false

			},
			bzsopen() {

			},
			bzsclose() {
				this.bzsshow = false

			},
			mhyOrder(types) {


				if (types == 1) {
					this.mhydloading = true
					this.mhymcdis = true;
				} else {
					this.mhymloading = true
					this.mhymrdis = true;
				}


				const {
					symbolQuotation,
					currency_id,
					kcnumber,
					kcvalues
				} = this
				const match_ids = Number(symbolQuotation.id)


				// return false
				this.$H.post('/api/v1/MicroOrder/submit', false, {
					type: types,
					match_id: match_ids,
					currency_id: 1,
					seconds: kcvalues,
					number: kcnumber
				}).then(result => {


					if (types == 1) {
						this.mhydloading = false
						this.mhymcdis = false;
					} else {
						this.mhymloading = false
						this.mhymrdis = false;
					}


					if (result.type == "ok") {

						this.ordershow = true


						this.$store.commit('setOrNum', 2)
						getApp().setUser()
					}

				})





			},
			orders() {
				this.ordershow = false
				uni.navigateTo({
					url: "/pages/order/order"
				})
			},
			can() {
				this.ordershow = false
			},
			subNames() {

				if (this.msg == "ok") {

					const subNVue = uni.getSubNVueById('modal')
					// 关闭弹窗
					subNVue.hide('none', 300)
				}
				if (this.msg == "can") {

					const subNVue = uni.getSubNVueById('modal')
					// 关闭弹窗
					subNVue.hide('none', 300)
				}

			},
			orderAppClicks() {
				// this.ordershow = true
				const subNVue = uni.getSubNVueById('bottoms');
				subNVue.show('slide-in-bottom', 300);
				// uni.hideTabBar();
			},
			//
			orderClicks(e, g) {

				if (e == 1) {
					if (g) {
						this.xdloading = true
						this.mcdis = true;
					} else {
						this.gdloading = true
						this.gmcdis = true;
					}

				} else {


					if (g) {
						this.xdmloading = true
						this.mrdis = true
					} else {
						this.gdmloading = true
						this.gmrdis = true
					}


				}

				let {
					number,
					multiple0,
					currency_id,
					stop_loss_price,
					target_profit_price,
					gnumber,
					multiple1
				} = this



				let newnumber = number
				let multiple = multiple0


				let stop_loss_prices = stop_loss_price

				if (!this.hyzs) {

					stop_loss_prices = 0
				}
				let target_profit_prices = target_profit_price
				if (!this.hyzy) {

					target_profit_prices = 0
				}
				let target_prices = 0

				if (!g) {
					newnumber = gnumber
					multiple = multiple1
					target_prices = this.target_price
					stop_loss_prices = this.gstop_loss_price

					if (!this.ghyzs) {
						stop_loss_prices = 0
					}
					target_profit_prices = this.gtarget_profit_price
					if (!this.ghyzy) {
						target_profit_prices = 0
					}
				}


				this.$H.post('/api/v1/lever/submit', false, {
					share: newnumber,
					multiple: multiple,
					legal_id: 1,
					currency_id: currency_id,
					type: e,
					status: g,
					target_profit_price: target_profit_prices,
					stop_loss_price: stop_loss_prices,
					target_price: target_prices
				}).then(result => {
					if (e == 1) {
						if (g) {
							this.xdloading = false
							this.mcdis = false;
						} else {
							this.gdloading = false
							this.gmcdis = false;
						}
					} else {
						if (g) {
							this.xdmloading = false
							this.mrdis = false;
						} else {
							this.gdmloading = false
							this.gmrdis = false;
						}

					}
					if (result.type == "ok") {

						this.ordershow = true
						if (g == 0) {
							this.$store.commit('setOrNum', 1)
						}

						this.$H.get('/api/v1/user', false).then(result => {

							this.$store.commit('setUser', result.data)
							// uni.navigateTo({
							// 	url: "/pages/order/order"
							// })


						})
					}

				})






			},

			sectionChange(index) {
				this.currentNav = index;
			},
			changeHandler(e) {},
			// 递归查找父级对象
			findParentByCurrencyId(data, targetCurrencyId) {
				function recurse(current, parent = null) {
					if (Array.isArray(current)) {
						for (let item of current) {
							const result = recurse(item, current); // 数组元素的父级就是当前数组
							if (result) return result;
						}
					} else if (typeof current === 'object' && current !== null) {
						if (current.currency_id === targetCurrencyId) {
							return parent; // 找到了，返回父级
						}

						for (let key in current) {
							if (current.hasOwnProperty(key)) {
								const result = recurse(current[key], current); // 当前对象是下一层的父级
								if (result) return result;
							}
						}
					}
					return null;
				}

				return recurse(data);
			},

			getQuotation() {
				const res = this.$H.get('/api/v1/quotation_new', false).then(res => {
					const parent = this.findParentByCurrencyId(res.data, this.currency_id);
					if (parent) {
						// 根据找到的父级对象处理 quotation 数据
						this.originalQuotation = parent.map(item => {
							item.precision_length = this.$utils.getPrecisionLength(item.now_price);
							if (item.micro_trade_fee != '0.00') {
								this.micro_trade_fee = item.micro_trade_fee;
							}
							return item;
						});
					} else {
						console.error("未找到对应的父级对象");
					}
					this.quotation = this.originalQuotation
					let has = this.originalQuotation.find(item => item.currency_id == this.currency_id)
					if (has) {
						this.symbolQuotation = has
						
						// 调试日志：记录初始价格设置
						console.log('📊 初始价格设置:', {
							currency_id: has.currency_id,
							currency_name: has.currency_name,
							now_price: has.now_price,
							source: 'quotation_new_api'
						});
						
						// 延迟同步K线价格到顶部（确保K线图已加载）
						this.$nextTick(() => {
							setTimeout(() => {
								this.syncKlinePriceToTop();
							}, 2000); // 等待K线图加载完成
						});
						
						var newArray = []
						has.multiple.map(item => {
							newArray.push(item.value)
						})
						this.columns = []
						this.columns.push(newArray)
						if (has.lever_min_share == 0) {
							this.number = 0.1
						} else {
							this.number = has.lever_min_share
						}
						if (this.columns.length > 0 && this.columns[0].length > 0) {
							this.multiple0 = this.columns[0][0]
							this.multiple1 = this.columns[0][0]
						}
						this.price = has.now_price
					}
					this.startSocket()
					this.calc()
					this.gcalc()
				})
			},
			startSocket() {
				const _this = this
				const {
					currency_id,
				} = this
				// 移除旧的daymarket监听器，避免重复绑定
				this.socket.off('daymarket');

				this.socket.on('daymarket', res => {
					let data = res
					let period = g_KLine.JSChart.JSChartContainer.Period;

					console.log('📈 接收到daymarket数据，但优先使用K线价格');

					if (this.currency_id == res.currency_id && this.periods[period] == res
						.period) {

						// 【简化】只处理K线实时数据更新，价格由K线直接同步
						const obj = {
							Symbol: this.currency_name,
							OriginalSymbol: this.currency_name,
							Period: period
						};
						this.RecvRealtimeData(res, obj);

						// K线数据更新后，立即同步价格
						setTimeout(() => {
							this.updatePriceFromKline();
						}, 100);
					}
				});

				// 添加备用定时器，检测价格更新是否中断
				this.startPriceUpdateMonitor();
			},

			// 【简化】直接从K线同步价格，不复杂判断
			updatePriceFromKline() {
				if (!g_KLine.JSChart || !g_KLine.JSChart.JSChartContainer) {
					return;
				}

				try {
					const chartContainer = g_KLine.JSChart.JSChartContainer;
					const mainData = chartContainer.ChartPaint[0];

					if (mainData && mainData.Data && mainData.Data.Data && mainData.Data.Data.length > 0) {
						const latestData = mainData.Data.Data[mainData.Data.Data.length - 1];

						if (latestData && latestData.Close && latestData.Close > 0) {
							const klinePrice = latestData.Close;
							const currentTopPrice = this.topPrice || 0;
							if (!currentTopPrice || Math.abs(klinePrice - currentTopPrice) >= 0.01) {
								this.topPrice = klinePrice;
								console.log('📈 顶部价格已由K线更新', { price: klinePrice, time: new Date().toLocaleTimeString() });
							}
						}
					}
				} catch (error) {
					console.error('K线价格同步失败:', error);
				}
			},

			// 【简化】启动K线价格直接同步
			startPriceUpdateMonitor() {
				// 清除之前的定时器
				if (this.priceUpdateTimer) {
					clearInterval(this.priceUpdateTimer);
				}

				// 立即获取一次价格
				setTimeout(() => {
					this.updatePriceFromKline();
				}, 2000);

				// 每5秒直接从K线同步价格
				this.priceUpdateTimer = setInterval(() => {
					this.updatePriceFromKline();
				}, 5000);
			},

			// 【新增】页面加载时从K线获取初始价格
			loadInitialPriceFromKline() {
				// 等待K线图加载完成
				const checkKlineReady = () => {
					if (g_KLine.JSChart && g_KLine.JSChart.JSChartContainer) {
						try {
							const chartContainer = g_KLine.JSChart.JSChartContainer;
							const mainData = chartContainer.ChartPaint[0];

							if (mainData && mainData.Data && mainData.Data.Data && mainData.Data.Data.length > 0) {
								const latestData = mainData.Data.Data[mainData.Data.Data.length - 1];

								if (latestData && latestData.Close && latestData.Close > 0) {
									const klinePrice = latestData.Close;

									// 【关键】只有在顶部价格为空或为0时才初始化
									if (!this.symbolQuotation.now_price || this.symbolQuotation.now_price == 0) {
										console.log('🚀 页面加载时从K线获取初始价格:', klinePrice);

										const priceData = {
											now_price: klinePrice,
											close: klinePrice,
											api_form: 'kline_initial',
											updated_at: new Date().toISOString()
										};

										this.updatePriceFromSource(priceData, 'kline_initial');
									}
								}
							}
						} catch (error) {
							console.error('获取K线初始价格失败:', error);
						}
					} else {
						// K线还没加载完成，1秒后重试
						setTimeout(checkKlineReady, 1000);
					}
				};

				// 延迟2秒开始检查，确保K线图有时间加载
				setTimeout(checkKlineReady, 2000);
			},

			// 【优化】检查并从K线更新价格（休市时使用）
			checkAndUpdatePriceFromKline() {
				// 检查是否为日股
				if (!this.isJPStock()) {
					return;
				}

				// 使用现有的 opening 状态判断（与首页逻辑一致）
				if (this.isMarketOpen()) {
					return; // 交易时间内，依赖实时推送
				}

				// 休市时间，从K线获取最新价格
				this.updatePriceFromKlineData();
			},

			// 【优化】判断是否为日股（简化逻辑）
			isJPStock() {
				// 通过币种类型判断，更准确
				return this.symbolQuotation && this.symbolQuotation.currency_type === 2; // JPStock类型ID
			},

			// 【新增】判断市场是否开放（使用现有逻辑）
			isMarketOpen() {
				// 使用与首页一致的 opening 状态
				// opening: 1=开市, 0=休市
				return this.symbolQuotation && this.symbolQuotation.opening === 1;
			},

			// 【新增】从K线数据更新价格
			updatePriceFromKlineData() {
				if (!g_KLine.JSChart || !g_KLine.JSChart.JSChartContainer) {
					return;
				}

				try {
					const chartContainer = g_KLine.JSChart.JSChartContainer;
					const mainData = chartContainer.ChartPaint[0];

					if (mainData && mainData.Data && mainData.Data.Data && mainData.Data.Data.length > 0) {
						const latestData = mainData.Data.Data[mainData.Data.Data.length - 1];

						if (latestData && latestData.Close && latestData.Close > 0) {
							const newPrice = latestData.Close;
							this.topPrice = newPrice;

							console.log('休市期间从K线更新价格:', {
								currency: this.currency_name,
								price: newPrice,
								source: 'kline_data'
							});
						}
					}
				} catch (error) {
					console.error('从K线更新价格失败:', error);
				}
			},
			
			// 刷新价格数据
			refreshPriceData() {
				// 重新获取股票行情数据
				this.getQuotation();
				// 更新最后更新时间
				this.lastPriceUpdateTime = Date.now();
			},
			
			// 【新增】主动同步K线价格到顶部
			syncKlinePriceToTop() {
				if (!g_KLine.JSChart || !g_KLine.JSChart.JSChartContainer) {
					console.log('⚠️  K线图未加载，延迟重试同步...');
					setTimeout(() => {
						this.syncKlinePriceToTop();
					}, 1000);
					return;
				}

				const chartContainer = g_KLine.JSChart.JSChartContainer;
				console.log('🔄 开始同步K线价格到顶部...');
				
				try {
					// 从ChartPaint获取最新数据
					if (chartContainer.ChartPaint && chartContainer.ChartPaint[0] && chartContainer.ChartPaint[0].Data) {
						const mainData = chartContainer.ChartPaint[0].Data;
						if (mainData && mainData.Data && mainData.Data.length > 0) {
							const latestData = mainData.Data[mainData.Data.length - 1];
							
							if (latestData && latestData.Close && latestData.Close > 0) {
								const klinePrice = latestData.Close;
								const currentTopPrice = this.topPrice;
								
								console.log('📊 价格对比:', {
									顶部当前价格: currentTopPrice,
									K线最新价格: klinePrice,
									差异: Math.abs(klinePrice - currentTopPrice),
									差异百分比: currentTopPrice > 0 ? ((Math.abs(klinePrice - currentTopPrice) / currentTopPrice) * 100).toFixed(2) + '%' : 'N/A'
								});
								
								// 如果价格差异超过1%或顶部价格为空，强制同步
								if (!currentTopPrice || currentTopPrice == 0 || Math.abs(klinePrice - currentTopPrice) / currentTopPrice > 0.01) {
									console.log('🚨 检测到价格差异或顶部价格为空，强制同步...');
									this.topPrice = klinePrice;
									console.log('✅ 强制同步完成，新价格:', klinePrice);
									return true;
								}
							}
						}
					}
					
					console.log('⚠️  无法获取有效的K线价格数据');
					return false;
					
				} catch (error) {
					console.error('❌ 同步K线价格时出错:', error);
					return false;
				}
			},
			
			confirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e

				this.multiple0 = value[0];
				this.shows = false
			},
			cancel(e) {
				this.shows = false
			},

			gconfirm(e) {

				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.guPicker
				} = e

				this.multiple1 = value[0];
				this.gshows = false
			},
			gcancel(e) {
				this.gshows = false
			},



			kcconfirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPickers
				} = e

				this.kcvalues = value[0].seconds;
				this.kcshows = false
			},
			kccancel(e) {
				this.kcshows = false
			},
			onChangeTab(e) {
				this.changesTabs(e.detail.current)
			},
			changesTabs(id) {
				if (this.tabIndex === id) {
					return;
				}
				this.tabIndex = id;
				this.scrollInto = 'tab' + id;
			},
			open() {

			},
			close() {
				this.ordershow = false
				// 	this.timer = setTimeout(() => {
				// 		uni.showTabBar()
				// 	}, 400)
			},
			calc() {
				const {
					symbolQuotation,
					buyDirection,
					multiple0,
					indicaList,
					number
				} = this
				const spread = Number(symbolQuotation.spread)
				const lever_share_num = Number(symbolQuotation.lever_share_num)
				const lever_trade_fee = Number(symbolQuotation.lever_trade_fee)
				const prices = Number(symbolQuotation.now_price)

				let spreadPrices = parseFloat((prices * spread) / 100);

				let pricesTotal = 0;
				if (buyDirection == 0) {
					// 如果方向是买
					pricesTotal = parseFloat(prices + spreadPrices);
				} else {
					pricesTotal = parseFloat(prices - spreadPrices);
				}
				let totalPrice = parseFloat(pricesTotal * lever_share_num * number);
				this.each_piece = symbolQuotation.each_piece
				// this.margin = ((totalPrice / multiple0) * this.each_piece).toFixed(5);
				this.margin = this.autoTruncate(this.each_piece * number)
				this.handlingFee = this.micro_trade_fee * Math.ceil(number);
			},
			gcalc() {
				const {
					symbolQuotation,
					buyDirection,
					multiple1,
					gnumber
				} = this
				const spread = Number(symbolQuotation.spread)
				const lever_share_num = Number(symbolQuotation.lever_share_num)
				const lever_trade_fee = Number(symbolQuotation.lever_trade_fee)
				const prices = Number(symbolQuotation.now_price)
				let spreadPrices = parseFloat((prices * spread) / 100);
				let pricesTotal = 0;
				if (buyDirection == 0) {
					// 如果方向是买
					pricesTotal = parseFloat(prices + spreadPrices);
				} else {
					pricesTotal = parseFloat(prices - spreadPrices);
				}
				let totalPrice = parseFloat(pricesTotal * lever_share_num * gnumber);
				this.each_piece = symbolQuotation.each_piece
				// this.gmargin = ((totalPrice / multiple1) * this.each_piece).toFixed(5);
				this.gmargin = this.autoTruncate(this.each_piece * gnumber)
				this.ghandlingFee = this.micro_trade_fee * Math.ceil(gnumber)
			},
			loadmore() {
				// for (let i = 0; i < 60; i++) {
				// 	this.indexList.push({
				// 		url: this.urls[uni.$u.random(0, this.urls.length - 1)]
				// 	})
				// }

				// const res =  this.$H.get('/api/v1/quotation_new',false).then(res => {

				// 	res.data[i].quotation.map(item=>{
				// 									item.precision_length = this.$utils.getPrecisionLength(item.now_price)
				// 									item.currentIndex = i
				// 									const has=this.mLists.findIndex(items => items.currency_id == item.currency_id)
				// 									if(has > -1){
				// 										item.isCollect = 1
				// 									}else{
				// 										item.isCollect = 0
				// 									}

				// 									 this.quotationOriginal.push(item)
				// 									  this.resData =  res.data[i].quotation
				// 									  this.startSocket();
				// 	})

				// 	// console.log('uuu')
				// 	// 	console.log(res.data[0].quotation[0].id)






				// 	 // this.myLists()

				// 	})

				this.quotation = this.resData


			},
			//对外接口
			//周期切换
			ChangePeriod(period) {
				this.pids = period
				if (period == -1) {
					if (g_KLine.JSChart.KLineType != KLINE_CHART_TYPE.MINUTE_KLINE_ID) {
						this.ClearChart();
						this.CreateKLineChart(KLINE_CHART_TYPE.MINUTE_KLINE_ID, 4);
					}
				} else {
					if (g_KLine.JSChart.KLineType == KLINE_CHART_TYPE.KLINE_ID) {
						g_KLine.JSChart.ChangePeriod(period);
					} else {
						this.ClearChart();
						this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, period);
					}
				}
			},
			ChangeIndex(index, name, itemes) {
				// if ("MA" == name) return;
				// console.log(index, name, itemes)
				// indicatorList:[],
				// miansList:[],
				if (!index) {
					const has = this.miansList.findIndex((item) => item == name)
					if (has > -1) {
						// g_KLine.JSChart.RemoveIndexWindow(has)
						g_KLine.JSChart.DeleteOverlayWindowsIndex(name)
						this.miansList.splice(has, 1)
					} else {
						this.miansList.push(name)
						var obj = {
							WindowIndex: index,
							IndexName: name,
							Identify: name,
							ShowRightText: false,
							OverlayIndexFrameWidth: 30,
							ShowToolbar: false,
							IsShowIndexTitle: false
						}
						g_KLine.JSChart.AddOverlayIndex(obj);
					}
					itemes.select = !itemes.select
				} else {
					const has = this.indicatorList.findIndex((item) => item == name)
					if (has > -1) {
						g_KLine.JSChart.RemoveIndexWindow(has + 1)
						this.indicatorList.splice(has, 1)
					} else {
						if (this.indicatorList.length >= 3) {
							return;
						}
						this.indicatorList.push(name)
						g_KLine.JSChart.AddIndexWindow(name);
					}
					itemes.select = !itemes.select
				}
				// g_KLine.JSChart.ChangeIndex(index, name);
				// var obj={WindowIndex:0, IndexName:name, Identify:name,ShowRightText:false,OverlayIndexFrameWidth:30,ShowToolbar:false,IsShowIndexTitle:false}
				// g_KLine.JSChart.AddOverlayIndex(obj);
			},
			OnSize() {
				// #ifdef H5
				this.OnSize_h5();
				// #endif
			},
			OnSize_h5() {
				var chartHeight = this.ChartHeight - 150;
				var chartWidth = this.ChartWidth;
				var kline = this.$refs.kline;
				kline.style.width = chartWidth + 'px';
				kline.style.height = chartHeight + 'px';
				if (g_KLine.JSChart) g_KLine.JSChart.OnSize();
			},
			InitalHQChart() {
				// k线颜色
				// #ifdef H5
				HQChart.MARKET_SUFFIX_NAME.GetBITDecimal = this.GetBITDecimal;
				var blackStyle = HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
				blackStyle.FrameTitleBGColor = "rgb(255,255,255)";
				let fontColor = "#fff"
				if (this.$store.state.bgColor == "black") {
					blackStyle.FrameTitleBGColor = "rgb(14, 24, 46)";
					blackStyle.UnchagneTextColor = fontColor;
					blackStyle.UpTextColor = fontColor;
					blackStyle.DownTextColor = fontColor;
				}
				if (this.$store.state.bgColor == "while") {
					fontColor = "#000"
					blackStyle.FrameSplitPen = "rgba(0, 0, 0, 0.1)";
					blackStyle.TooltipBGColor = "#ddd";
					blackStyle.UnchagneTextColor = fontColor;
					blackStyle.UpTextColor = fontColor;
					blackStyle.DownTextColor = fontColor;
				}
				blackStyle.FrameYLineDash = [4, 4];
				blackStyle.FrameXLineDash = [6, 6];
				blackStyle.UpBarColor = '#0166fc';
				blackStyle.DownBarColor = "rgb(238,21,21)";
				blackStyle.FrameLatestPrice.DownBarColor = 'rgb(238,21,21)';

				HQChart.JSChart.SetStyle(blackStyle);
				this.$refs.kline.style.backgroundColor = blackStyle.FrameTitleBGColor;
				// #endif

				// #ifndef H5
				JSCommon.MARKET_SUFFIX_NAME.GetBITDecimal = this.GetBITDecimal;
				var blackStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
				this.SetHQChartStyle(blackStyle);
				JSCommon.JSChart.SetStyle(blackStyle);

				// #endif
			},

			//设置图形颜色
			SetHQChartStyle(blackStyle) {
				blackStyle.BGColor = 'rgb(155,255,255)'; //背景
				blackStyle.FrameTitleBGColor = 'rgb(0,0,0)'; //指标标题背景
				blackStyle.FrameSplitTextColor = 'rgb(101,117,138)'; //刻度颜色

				//K线颜色
				blackStyle.UpBarColor = '#0166fc';
				blackStyle.UpTextColor = blackStyle.UpBarColor;
				blackStyle.DownBarColor = 'rgb(210,73,99)';
				blackStyle.DownTextColor = blackStyle.DownBarColor;
				//平盘
				blackStyle.UnchagneBarColor = blackStyle.UpBarColor;
				blackStyle.UnchagneTextColor = blackStyle.UpBarColor;

				//指标线段颜色
				blackStyle.Index.LineColor[0] = 'rgb(88,106,126)';
				blackStyle.Index.LineColor[1] = 'rgb(222,217,167)';
				blackStyle.Index.LineColor[2] = 'rgb(113,161,164)';

				//最新价格刻度颜色
				blackStyle.FrameLatestPrice.UpBarColor = '#0166fc';
				blackStyle.FrameLatestPrice.DownBarColor = 'rgb(210,73,99)';
				blackStyle.FrameLatestPrice.UnchagneBarColor = blackStyle.FrameLatestPrice.UpBarColor;

				blackStyle.Frame.XBottomOffset = 2;

				//blackStyle.Minute.PriceColor='rgb(255,255,255)';
			},

			GetBITDecimal(symbol) {
				// var lowerSymbol=symbol.toLowerCase();
				// if (lowerSymbol=="ethusdt.bit" || lowerSymbol=="btcusdt.bit") return 5;
				return 2;
			},

			ClearChart() {
				if (g_KLine.JSChart) {
					g_KLine.JSChart.ChartDestory();
					g_KLine.JSChart = null;
				}

				// #ifdef H5
				var divKLine = document.getElementById('kline');
				// 检查是否有子节点

				// 检查是否成功获取到了元素
				if (divKLine) {
					if (divKLine.hasChildNodes()) {
						while (divKLine.hasChildNodes()) {
							divKLine.removeChild(divKLine.lastChild);
						}
					}
				}
				// #endif
			},

			//创建K线图
			CreateKLineChart_h5(klineType, period) {
				if (g_KLine.JSChart) return;
				var blackStyle = HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
				// blackStyle.FrameTitleBGColor="rgb(255,255,255)"
				// blackStyle.FrameSplitPen="rgb(237,250,255)"
				// HQChart.JSChart.SetStyle(blackStyle);
				// this.$refs.kline.style.backgroundColor=blackStyle.BGColor;	//div背景色设置黑色
				// this.$refs.kline.style.backgroundColor=blackStyle.BGColor;	//div背景色设置黑色
				this.KLine.Option.Symbol = this.Symbol;
				let chart = HQChart.JSChart.Init(this.$refs.kline);
				if (klineType == KLINE_CHART_TYPE.MINUTE_KLINE_ID) {
					this.KLine.Option = DefaultData.GetMinuteOption();
				} else {
					klineType = KLINE_CHART_TYPE.KLINE_ID;
					this.KLine.Option = DefaultData.GetKLineOption();
				}
				this.KLine.Option.NetworkFilter = this.NetworkFilter;
				this.KLine.Option.Symbol = this.Symbol;
				this.KLine.Option.KLine.Period = period;
				if (this.$store.state.bgColor == "while") {
					HQChart.JSChart.GetResource().KLine.MaxMin.Color = "rgb(238,21,21)"
				}
				HQChart.JSChart.GetResource().FrameLogo.Text = null;
				chart.SetOption(this.KLine.Option);
				g_KLine.JSChart = chart;
				g_KLine.JSChart.KLineType = klineType;
			},

			CreateKLineChart_app(klineType, period) {
				if (g_KLine.JSChart) return;

				let element = new JSCommon.JSCanvasElement();
				// #ifdef APP-PLUS
				element.IsUniApp = true; //canvas需要指定下 是uniapp的app
				// #endif
				element.ID = 'kline2';
				element.Height = this.ChartHeight; //高度宽度需要手动绑定!!
				element.Width = this.ChartWidth;

				//var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
				//JSCommon.JSChart.SetStyle(blackStyle);

				g_KLine.JSChart = JSCommon.JSChart.Init(element);
				if (klineType == KLINE_CHART_TYPE.MINUTE_KLINE_ID) {
					this.KLine.Option = DefaultData.GetMinuteOption();
				} else {
					klineType = KLINE_CHART_TYPE.KLINE_ID;
					this.KLine.Option = DefaultData.GetKLineOption();
				}
				this.KLine.Option.NetworkFilter = (data, callback) => {
					this.NetworkFilter(data, callback);
				};
				this.KLine.Option.Symbol = this.Symbol;
				this.KLine.Option.IsClickShowCorssCursor = true;
				this.KLine.Option.IsFullDraw = true; //每次手势移动全屏重绘
				this.KLine.Option.KLine.Period = period;
				JSCommon.JSChart.GetResource().FrameLogo.Text = null;
				g_KLine.JSChart.SetOption(this.KLine.Option);
				g_KLine.JSChart.KLineType = klineType;
			},

			CreateKLineChart(klineType, period) {
				this.Update.Cache = null;
				// #ifdef H5
				this.CreateKLineChart_h5(klineType, period);
				// #endif
				// #ifndef H5
				this.CreateKLineChart_app(klineType, period);
				// #endif
			},
			CreateDepthChart() {
				this.Update.Cache = null;
				// #ifdef H5
				this.CreateDepthChart_h5();
				// #endif
				// #ifndef H5
				this.CreateDepthChart_app();
				// #endif
			},

			CreateDepthChart_h5() {
				if (g_KLine.JSChart) return;
				var option = DefaultData.GetDepthOption();
				option.Symbol = this.Symbol;
				option.NetworkFilter = this.NetworkFilter;
				chart.SetOption(option);
				g_KLine.JSChart = chart;
			},
			CreateDepthChart_app() {
				if (g_KLine.JSChart) return;
				var element = new JSCommon.JSCanvasElement();
				// #ifdef APP-PLUS
				element.IsUniApp = true; //canvas需要指定下 是uniapp的app
				// #endif
				element.ID = 'kline2';
				element.Height = this.ChartHeight; //高度宽度需要手动绑定!!
				element.Width = this.ChartWidth;
				//var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
				//JSCommon.JSChart.SetStyle(blackStyle);
				var chart = JSCommon.JSChart.Init(element);
				var option = DefaultData.GetDepthOption();
				option.Symbol = this.Symbol;
				option.IsFullDraw = true; //每次手势移动全屏重绘
				option.NetworkFilter = this.NetworkFilter;
				chart.SetOption(option);
				g_KLine.JSChart = chart;
			},

			ChangeDepthChart() {
				if (g_KLine.JSChart && g_KLine.JSChart.JSChartContainer.ClassName == "DepthChartContainer") return;
				this.ClearChart();
				this.CreateDepthChart();
			},

			NetworkFilter(data, callback) {
				switch (data.Name) {
					case 'KLineChartContainer::ReqeustHistoryMinuteData': //分钟全量数据下载
						this.RequestHistoryMinuteData(data, callback);
						break;
					case 'KLineChartContainer::RequestFlowCapitalData': //数字货币不会调用
						this.RequestFlowCapitalData(data, callback);
						break;
					case 'KLineChartContainer::RequestHistoryData': //日线全量数据下载
						this.RequestHistoryData(data, callback);
						break;
					case "DepthChartContainer::RequestDepthData":
						this.RequestDepthData(data, callback);
						break;
				}
			},

			//////////////////////////////////////////////////////
			//WS
			//心跳包
			SendWSHeartMessage() {
				if (this.SocketOpen) {
					var pong = {
						'pong': new Date().getTime()
					};
					var message = JSON.stringify(pong);
					uni.sendSocketMessage({
						data: message
					});
				}
			},

			//取消订阅上一次的信息
			SendUnSubscribeMessage() {
				if (!this.LastSubString || !this.SocketOpen) return;
				var message = JSON.stringify({
					unsub: this.LastSubString
				}); //取消上次订阅的信息
				uni.sendSocketMessage({
					data: message
				});
				this.LastSubString = null; //清空最后一个订阅信息
			},

			RequestWSData(data, recvCallback) {
				// if (!this.SocketOpen)
				// {
				// 	uni.connectSocket( {url:this.WSUrl} );//创建连接

				// 	uni.onSocketOpen((event)=>
				// 	{
				// 		this.SocketOpen=true;
				// 		console.log(event);
				// 		var message=JSON.stringify(data.SendData);
				// 		uni.sendSocketMessage({data:message});
				// 		if (data.SendData.sub) this.LastSubString=data.SendData.sub;
				// 	});
				// }
				// else
				// {
				// 	this.SendUnSubscribeMessage();
				// 	var message=JSON.stringify(data.SendData);
				// 	uni.sendSocketMessage({data:message});
				// 	if (data.SendData.sub) this.LastSubString=data.SendData.sub;    //保存最后一个订阅信息
				// }
				uni.onSocketMessage((event) => {
					// let ploydata = new Uint8Array(event.data);
					// let msg = pako.inflate(ploydata, {to: 'string'});
					// console.log("[KLineChart::RequestWSData] recv ", msg);
					// var recvData=JSON.parse(msg);
					if (event.data == "40") {
						return
					}
					var wz = event.data.indexOf('[')
					if (wz > 2) {
						wz = event.data.indexOf('{')
					}
					event.data = event.data.substring(wz, event.length)
					var recvData = JSON.parse(event.data);
					if (recvData.ping) {
						this.SendWSHeartMessage(); //回复服务器心跳包
					} else if (recvData.unsubbed) //取消订阅成功
					{

					} else if (recvData.subbed) //订阅成功
					{

					} else {
						recvCallback(recvData, data);
					}
				});
				uni.onSocketError((event) => {
					console.log(event);
				});
			},

			//生成请求数据
			GeneratePostData(symbol, period) {
				//1min, 5min, 15min, 30min, 60min,4hour,1day,1week, 1mon
				var MAP_PERIOD = new Map([
					[4, '1min'],
					[5, '5min'],
					[6, "15min"],
					[7, '30min'],
					[8, "60min"],
					[0, '1day'],
					[1, '1week'],
					[2, '1mon'],
					[3, '1year']
				]);
				var strPeriod = MAP_PERIOD.get(period);
				var reqData = {
					req: `market.${symbol}.kline.${strPeriod}`,
					symbol: symbol,
					period: strPeriod
				};
				var subData = {
					sub: `market.${symbol}.kline.${strPeriod}`,
					symbol: symbol,
					period: strPeriod
				};
				return {
					Req: reqData,
					Sub: subData
				};
			},

			//请求分钟历史数据
			RequestHistoryMinuteData(data, callback) {
				data.PreventDefault = true;
				this.Update.Cache = null;
				var symbol = data.Request.Data.symbol;
				var period = data.Self.Period; //周期
				var postData = this.GeneratePostData(this.OriginalSymbol, period);
				var obj = {
					SendData: postData.Req,
					Symbol: symbol,
					OriginalSymbol: this.OriginalSymbol,
					Period: period,
					Callback: callback
				};
				this.RecvHistoryMinuteData({}, obj);
				// this.RequestWSData(obj, (recvData,data)=>{ this.RecvHistoryMinuteData(recvData,data); });
			},

			//接收历史分钟数据
			async RecvHistoryMinuteData(recvData, data) {
				// if (recvData.rep!=data.SendData.req) return;
				var hqChartData = {
					code: 0,
					data: []
				};
				hqChartData.symbol = data.Symbol;
				hqChartData.name = data.OriginalSymbol;
				let froms = Date.parse(new Date()) / 1000 - this.intervals[data.SendData.period] * 24 * 60 * 60
				let tos = Date.parse(new Date()) / 1000
				const res = await this.$H.post('/api/v1/klineMarket', false, {
					// symbol:"BTC/USDT",
					symbol: this.symboles,
					period: data.SendData.period,
					from: froms,
					to: tos
				})
				this.topPrice = res.data[res.data.length - 1].close;
				//          var dataes=[
				// 	{"id":1692648120,"open":26127.76,"close":26149.12,"low":26127.76,"high":26149.12,"amount":3.1703699796539158,"vol":82872.18459971,"count":624}
				// 	,{"id":1692648180,"open":26149.11,"close":26124.33,"low":26124.33,"high":26149.12,"amount":3.561071,"vol":93068.68270964,"count":270}

				// if (recvData.data)
				// 			{

				// 先按时间戳排序数据，确保时间顺序正确
				res.data.sort(function(a, b) {
					return a.time - b.time;
				});

				var yClose = null; //前收盘
				for (var i in res.data) {
					var item = res.data[i];
					//时间戳转换
					var dateTime = new Date();
					dateTime.setTime(item.time);
					var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate();
					var time = dateTime.getHours() * 100 + dateTime.getMinutes();
					var newItem = [date, yClose, parseFloat(item.open), parseFloat(item.high), parseFloat(item.low),
						parseFloat(item.close), parseFloat(item.volume), null, time
					];
					yClose = item.close;
					hqChartData.data.push(newItem);
				}
				// }
				// #ifdef H5
				data.Callback(hqChartData);
				// #endif
				// #ifndef H5
				data.Callback({
					data: hqChartData
				});
				// #endif
				this.SubscribeMinuteRealtimeData(data);
			},

			//订阅最新分钟K线数据
			SubscribeMinuteRealtimeData(data) {
				//订阅最新数据
				var postData = this.GeneratePostData(data.OriginalSymbol, data.Period);
				var obj = {
					SendData: postData.Sub,
					Symbol: data.Symbol,
					OriginalSymbol: data.OriginalSymbol,
					Period: data.Period
				};
				// this.socket.on('kline', res => {
				// this.RecvMinuteRealtimeData(res,obj);
				//   })
				this.socket.on('kline', res => {
					this.RecvMinuteRealtimeData(res, obj);
				})
				// this.RequestWSData(obj, (recvData,data)=>{ this.RecvMinuteRealtimeData(recvData,data); });
			},

			RecvMinuteRealtimeData(item, data) {
				// console.log(recvData);
				// if (recvData.ch!=data.SendData.sub) return;
				if (!g_KLine.JSChart) return;
				var internalChart = g_KLine.JSChart.JSChartContainer;
				var period = internalChart.Period;
				var symbol = internalChart.Symbol;
				if (symbol != data.Symbol || period != data.Period) return;
				// 【简化】K线数据更新后，直接同步价格
				if (this.currency_id == item.currency_id && this.periods[period] == item.period) {
					// K线数据更新后，立即从K线获取最新价格
					setTimeout(() => {
						this.updatePriceFromKline();
					}, 50);
				}
				var hqChartData = {
					code: 0,
					data: [],
					ver: 2.0
				}; //更新数据使用2.0版本格式
				hqChartData.symbol = data.Symbol;
				hqChartData.name = data.OriginalSymbol;
				//TODO:把recvData => hqchart内部格式 格式看教程
				//HQChart使用教程30-K线图如何对接第3方数据15-轮询增量更新1分钟K线数据
				// var item=recvData.kline;
				var piod = null;
				var dateTime = null;
				if (item.currency_name != this.currency_name) {
					return
				}
				if (this.periods[period] !== item.period) return;
				dateTime = new Date();
				dateTime.setTime(item.time);
				var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate();
				var time = dateTime.getHours() * 100 + dateTime.getMinutes();
				var newItem = [date, null, parseFloat(item.open), parseFloat(item.high), parseFloat(item.low), parseFloat(
					item.close), parseFloat(item.volume), null, time];
				//是否启动缓存定时更新，降低刷新频率
				if (this.Update.EnableLimit) {
					if (!this.Update.Cache) {
						this.Update.Cache = {
							Data: [newItem]
						};
					} else {
						var cache = this.Update.Cache;
						var item = cache.Data[cache.Data.length - 1];
						if (item[0] == newItem[0] && item[8] == newItem[8])
							cache.Data[cache.Data.length - 1] = newItem;
						else
							cache.Data.push(newItem)
					}
					var bUpdate = true;
					if (this.Update.LastUpdateTime) {
						var now = Date.now();
						if (now - this.Update.LastUpdateTime < this.Update.Frequency) //15s更新一次界面
							bUpdate = false;
					}
					if (!bUpdate) return; //不更新
					hqChartData.data = this.Update.Cache.Data;
					this.Update.Cache = null;
					this.Update.LastUpdateTime = Date.now();
				} else {
					hqChartData.data.push(newItem);
				}
				// #ifdef H5
				internalChart.RecvMinuteRealtimeData(hqChartData);
				// #endif
				// #ifndef H5
				internalChart.RecvMinuteRealtimeData({
					data: hqChartData
				});
				// #endif
			},


			//日K数据下载
			RequestHistoryData(data, callback) {
				data.PreventDefault = true;
				this.Update.Cache = null;
				var symbol = data.Request.Data.symbol;
				var period = data.Self.Period; //周期
				var postData = this.GeneratePostData(this.OriginalSymbol, period);
				var obj = {
					SendData: postData.Req,
					Symbol: symbol,
					OriginalSymbol: this.OriginalSymbol,
					Period: period,
					Callback: callback
				};
				this.RecvHistoryData({}, obj);
				// this.RequestWSData(obj, (recvData,data)=>{ this.RecvHistoryData(recvData,data); });
			},

			//接收到日线数据 转化成hqchart格式数据
			async RecvHistoryData(recvData, data) {
				// if (recvData.rep!=data.SendData.req) return;
				// var hqChartData={code:0, data:[]};
				// hqChartData.symbol=data.Symbol;
				// hqChartData.name=data.OriginalSymbol;
				var hqChartData = {
					code: 0,
					data: []
				};
				hqChartData.symbol = data.Symbol;
				hqChartData.name = data.OriginalSymbol;
				const froms = Date.parse(new Date()) / 1000 - this.intervals[data.SendData.period] * 24 * 60 * 60
				const tos = Date.parse(new Date()) / 1000
				const res = await this.$H.post('/api/v1/klineMarket', false, {
					// symbol:"BTC/USDT",
					symbol: this.symboles,
					period: data.SendData.period,
					from: froms,
					to: tos
				})

				// if (recvData.data)
				// {
				//console.log("[RecvHistoryData] recvData.data",recvData.data);

				// 先按时间戳排序数据，确保时间顺序正确
				res.data.sort(function(a, b) {
					return a.time - b.time;
				});

				var yClose = null; //前收盘
				for (var i in res.data) {
					var item = res.data[i];
					//时间戳转换
					var dateTime = new Date();
					dateTime.setTime(item.time);
					var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate();
					var time = dateTime.getHours() * 100 + dateTime.getMinutes();
					var newItem = [date, parseFloat(yClose), parseFloat(item.open), parseFloat(item.high), parseFloat(
						item
						.low), parseFloat(item.close), parseFloat(item.amount), null];

					yClose = item.close;
					hqChartData.data.push(newItem);
				}
				// }

				// #ifdef H5
				data.Callback(hqChartData);
				// #endif
				// #ifndef H5
				data.Callback({
					data: hqChartData
				});
				// #endif
				this.SubscribRealtimeData(data);
			},

			//订阅最新日K线数据
			SubscribRealtimeData(data) {
				//订阅最新数据
				var postData = this.GeneratePostData(data.OriginalSymbol, data.Period);
				var obj = {
					SendData: postData.Sub,
					Symbol: data.Symbol,
					OriginalSymbol: data.OriginalSymbol,
					Period: data.Period
				};
				// 移除重复的daymarket监听器 - 已在startSocket()中统一处理
				// this.socket.on('daymarket', res => {
				// 	this.RecvRealtimeData(res, obj);
				// })
				// this.RequestWSData(obj, (recvData,data)=>{ this.RecvRealtimeData(recvData,data); });
			},
			autoTruncate(num) {
				// 将数字转换为字符串
				const numStr = num.toString();
				// 分割整数部分和小数部分
				const [integerPart, decimalPart] = numStr.split('.');
				// 如果没有小数部分，直接返回原值
				if (!decimalPart) {
					return num;
				}
				// 找到第一个非零数字的位置（从小数点后开始）
				let significantDigits = 0;
				for (let i = 0; i < decimalPart.length; i++) {
					if (decimalPart[i] !== '0') {
						significantDigits = i + 1; // 保留到第一个非零数字后的下一位
						break;
					}
				}
				// 如果全是 0，则只保留整数部分
				if (significantDigits === 0) {
					return parseFloat(integerPart);
				}
				// 计算需要保留的小数位数
				const decimalPlaces = Math.min(significantDigits, decimalPart.length, 4);
				// 使用四舍五入截取到指定的小数位数
				const factor = Math.pow(10, decimalPlaces);
				return Math.round(num * factor) / factor;
			},
			RecvRealtimeData(item, data) {
				if (!g_KLine.JSChart) return;
				var internalChart = g_KLine.JSChart.JSChartContainer;
				var period = internalChart.Period;
				var symbol = internalChart.Symbol;
				if (symbol != data.Symbol || period != data.Period) return;
				var hqChartData = {
					code: 0,
					data: [],
					ver: 2.0
				}; //更新数据使用2.0版本格式
				hqChartData.symbol = data.Symbol;
				hqChartData.name = data.OriginalSymbol;
				//TODO:把recvData => hqchart内部格式 格式看教程
				//HQChart使用教程30-K线图如何对接第3方数据15-轮询增量更新1分钟K线数据
				// var item=recvData.kline;
				var piod = null;
				var dateTime = null;
				if (item.currency_name != this.currency_name) {
					return
				}
				if (this.periods[period] !== item.period) return;
				dateTime = new Date();
				dateTime.setTime(item.time);
				var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate();
				var time = dateTime.getHours() * 100 + dateTime.getMinutes();
				var newItem = [date, null, parseFloat(item.open), parseFloat(item.high), parseFloat(item.low), parseFloat(
					item.close), parseFloat(item.volume), null, time];
				//是否启动缓存定时更新，降低刷新频率
				if (this.Update.EnableLimit) {
					if (!this.Update.Cache) {
						this.Update.Cache = {
							Data: [newItem]
						};
					} else {
						var cache = this.Update.Cache;
						var item = cache.Data[cache.Data.length - 1];
						if (item[0] == newItem[0] && item[8] == newItem[8])
							cache.Data[cache.Data.length - 1] = newItem;
						else
							cache.Data.push(newItem)
					}
					var bUpdate = true;
					if (this.Update.LastUpdateTime) {
						var now = Date.now();
						if (now - this.Update.LastUpdateTime < this.Update.Frequency) //15s更新一次界面
							bUpdate = false;
					}
					if (!bUpdate) return; //不更新
					hqChartData.data = this.Update.Cache.Data;
					this.Update.Cache = null;
					this.Update.LastUpdateTime = Date.now();
				} else {
					hqChartData.data.push(newItem);
				}
				// #ifdef H5
				internalChart.RecvMinuteRealtimeData(hqChartData);
				// #endif
				// #ifndef H5
				internalChart.RecvMinuteRealtimeData({
					data: hqChartData
				});
				// #endif
				return
				// if (recvData.ch!=data.SendData.sub) return;
				if (!g_KLine.JSChart) return;
				var internalChart = g_KLine.JSChart.JSChartContainer;
				var period = internalChart.Period;
				var symbol = internalChart.Symbol;
				if (symbol != data.Symbol || period != data.Period) return;
				if (item.currency_name != "BTC") {
					return
				}
				if (this.periods[period] !== item.period) return;
				var hqChartData = {
					code: 0,
					stock: []
				};
				//TODO:把recvData => hqchart内部格式 格式看教程
				//HQChart使用教程30-K线图如何对接第3方数据14-轮询增量更新日K数据
				if (this.Update.EnableLimit) //是否启动缓存定时更新，降低刷新频率
				{
					var now = Date.now();
					if (this.Update.LastUpdateTime) {
						if (now - this.Update.LastUpdateTime < this.Update.Frequency) return;
					}
					this.Update.LastUpdateTime = now;
				}
				var stock = {
					symbol: data.Symbol,
					name: data.OriginalSymbol
				};
				// var item=recvData.tick;
				var dateTime = new Date();
				dateTime.setTime(item.time);
				var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate();
				var time = dateTime.getHours() * 100 + dateTime.getMinutes();
				stock.date = date;
				stock.yclose = null;
				stock.open = item.open;
				stock.high = item.high;
				stock.low = item.low;
				stock.price = item.close;
				stock.vol = item.volume;
				stock.amount = null;
				hqChartData.stock.push(stock);
				// #ifdef H5
				internalChart.RecvRealtimeData(hqChartData);
				// #endif
				// #ifndef H5
				internalChart.RecvRealtimeData({
					data: hqChartData
				});
				// #endif
			},
			GeneratePostDepthData(symbol, step) {
				var reqData = {};
				var subData = {
					sub: `market.${symbol}.depth.${step}`,
					symbol: symbol
				};
				return {
					Req: reqData,
					Sub: subData
				};
			},
			RequestDepthData(data, callback) {
				data.PreventDefault = true;
				this.Update.Cache = null;
				var symbol = data.Request.Data.symbol;
				var period = data.Self.Period; //周期
				var postData = this.GeneratePostDepthData(this.OriginalSymbol, 'step1');
				var obj = {
					SendData: postData.Sub,
					Symbol: symbol,
					OriginalSymbol: this.OriginalSymbol,
					Callback: callback
				};
				this.RequestWSData(obj, (recvData, data) => {
					this.RecvDepthData(recvData, data);
				});
			},

			RecvDepthData(recvData, data) {
				if (recvData.ch != data.SendData.sub) return;
				if (!g_KLine.JSChart) return;
				var internalChart = g_KLine.JSChart.JSChartContainer;
				var symbol = internalChart.Symbol;
				if (symbol != data.Symbol || internalChart.ClassName != 'DepthChartContainer') return;
				var tick = recvData.tick;
				var asks = [],
					bids = [];
				if (tick.asks) {
					for (var i = 0; i < tick.asks.length; ++i) {
						var item = tick.asks[i];
						asks.push([item[0], item[1]]);
					}
				}
				if (tick.bids) {
					for (var i = 0; i < tick.bids.length; ++i) {
						var item = tick.bids[i];
						bids.push([item[0], item[1]]);
					}
				}
				var hqChartData = {
					code: 0,
					asks: asks, //卖盘
					bids: bids, //买盘
					datatype: "snapshot" //全量数据
				};
				internalChart.ChartSplashPaint.EnableSplash(false);
				internalChart.RecvDepthData(hqChartData);
			},


			///////////////////////////////////////////////
			//手势事件 app/小程序才有
			//KLine事件
			KLineTouchStart: function(event) {
				if (g_KLine.JSChart) g_KLine.JSChart.OnTouchStart(event);
			},

			KLineTouchMove: function(event) {
				if (g_KLine.JSChart) g_KLine.JSChart.OnTouchMove(event);
			},

			KLineTouchEnd: function(event) {
				if (g_KLine.JSChart) g_KLine.JSChart.OnTouchEnd(event);
			},
		}

	}
</script>
<style scoped>
	.bnsss /deep/.uni-picker-view-mask {
		background: inherit;
	}

	/deep/.u-image {
		margin: 0 auto;
	}

	.kline {
		background: #ffffff;
		position: relative;
	}

	page {

		position: inherit;
		overflow: auto !important;
	}

	.showItems {
		width: 130px;
		text-align: center;
		padding: 20px;
		--tw-bg-opacity: 1 !important;
		border-bottom: 2px solid #e5e7eb;
		background-color: #e5e7eb;
	}

	.noShowItems {
		width: 130px;
		text-align: center;
		padding: 20px;
		--tw-bg-opacity: 1 !important;
		border-bottom: 2px solid #e5e7eb;
	}

	.itemview {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 15rpx 25rpx;

		justify-content: space-between;
		color: #222;
		border-radius: 8rpx;
		margin-top: 16rpx;

		margin-right: 20rpx;

		text {}

		image {
			width: 30rpx;
			height: 30rpx;
			margin-left: 60rpx;
		}
	}

	.itemview_ {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 15rpx 25rpx;

		justify-content: space-between;
		color: #222;
		border-radius: 8rpx;
		margin-top: 16rpx;
		border: 2rpx solid #333;
		margin-right: 20rpx;

		text {}

		image {
			width: 30rpx;
			height: 30rpx;
			margin-left: 60rpx;
		}
	}

	.u-content {
		padding: 0 0 40px 0;
		@include flex(column);
		align-items: center;
		justify-content: center;

		&__circle {
			background-color: $u-success;
			@include flex;
			border-radius: 100px;
			width: 60px;
			height: 60px;
			align-items: center;
			justify-content: center;
		}

		&__normal {
			font-size: 15px;
			color: $u-success;
			margin-top: 15px;
		}
	}

	.composings {
		margin-top: 18px;
		font-weight: bold;
	}

	.u-line {
		border-bottom: 0px solid rgb(214, 215, 217);
	}

	uni-swiper-item {
		overflow-x: hidden;
		overflow-y: auto;
	}

	.wnss /deep/.u-input__plus {

		background: #F5F7FA !important;
	}

	.bnss /deep/.u-input__plus {

		background: #12151B !important;
	}

	.bnss-flex {
		display: flex;
		align-items: center;
		text-align: center;
	}

	.bnss-flex-li {
		width: 60rpx;
		height: 60rpx;
		background: #f2f3f5;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx;
		border-radius: 6rpx;
	}

	.bnss-flex-input {
		height: 42rpx;
		border: none !important;
		background: #f2f3f5;
	}

	.bnss /deep/.u-input__minus {


		background: #12151B !important;
	}

	.bnss /deep/.u-icon__icon {
		color: #fff !important;
	}

	.wnss /deep/.u-input__minus {


		background: #F5F7FA !important;
	}

	.wnss /deep/.u-input {

		border: 1px solid #D2D6E8;
	}

	.bnss /deep/.u-input {

		border: 1px solid #2F3142;
	}

	.bnss /deep/.u-input__input--disabled {
		color: #424656 !important;
	}

	/deep/.u-subsection__bar {
		border-bottom: 2px solid #0166fc;
	}

	.bnss /deep/.u-subsection--button {
		height: 65px;
		background: #1A1C24;
	}

	uni-page-body {
		overflow-x: hidden;
	}

	.jys {
		background: #ffffff;
	}

	.jys .scroll-view {
		width: 95% !important;
		height: 75rpx;
		line-height: 75rpx;
		white-space: nowrap;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 6px;
		margin-top: 15px;

	}


	.jys .scroll-viewws {
		width: 93% !important;
		/* height: 105rpx; */
		line-height: 105rpx;
		white-space: nowrap;
		background: #ededed;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		/* border-radius: 0px 100px 100px 0px; */
		border-radius: 100px;
		padding-left: 5px;
		padding-right: 5px;
		/* margin-top: 15px; */

	}

	.jys .scroll-viewws .scroll-items {
		display: inline-block;
		width: 50%;
		height: 75rpx;
		line-height: 75rpx;
		text-align: center;
		background: #0166fc;
		/* border-radius: 100px 0px 0px 100px; */
		border-radius: 100px;
		color: #ffffff;
	}

	.jys .scroll-viewws .scroll-items-r {
		display: inline-block;
		width: 50%;
		height: 75rpx;
		line-height: 75rpx;
		text-align: center;
		background: #0166fc;
		border-radius: 0px 100px 100px 0px;
		color: #ffffff;
	}

	.scroll-viewws .scroll-item {
		display: inline-block;
		width: 50%;
		line-height: 75rpx;
		height: 75rpx;
		text-align: center;
		border-radius: 100px;
		color: #626779;

	}

	.mini-btn {
		border-radius: 50px;
		--tw-text-opacity: 1 !important;
		color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(1 102 252 / var(--tw-bg-opacity)) !important;
	}

	.bmini-btn {
		border-radius: 50px;
		--tw-text-opacity: 1 !important;
		color: rgb(19 19 19 / var(--tw-text-opacity)) !important;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(238 238 238 / var(--tw-bg-opacity)) !important;
	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
	}

	.dhbts {
		display: inline-block;
		background: #fff;
		margin: 5px;
		width: 15%;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;
		background-color: #eeeeee;
		font-weight: 500 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
		/* border-radius: 8vw !important; */
		text-align: center;
	}

	.sdhbts {
		display: inline-block;
		background: #fff;
		margin: 5px;
		width: 15%;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;

		font-weight: 500 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
		/*  border-radius: 8vw !important; */
		text-align: center;
		background-color: rgb(1 102 252 / var(--tw-bg-opacity)) !important;
	}

	/deep/.u-popup__content {

		border-radius: 10px;
	}

	.bnsss /deep/.u-picker__view__column__item {
		color: #fff;
	}

	.bnsss /deep/.u-popup__content {

		border-radius: 10px;
		background: #222;
	}

	.wnsss /deep/.u-popup__content {

		border-radius: 10px;
	}

	.bzsshow {
		overflow: hidden;
		position: fixed;
	}

	/deep/.u-subsection__item__text {
		font-size: 15px !important;

	}








	/deep/.u-subsection--button__bar[data-v-244377f2] {

		background-color: inherit !important;

	}


	.jybs /deep/.u-subsection--button {
		background-color: inherit !important;
		height: 50px;
		margin-left: -5px;

	}

	/deep/.u-subsection__item__text {

		line-height: 30px !important;
		display: flex;
		flex-direction: row;
		align-items: center;
		transition-property: color;
		transition-duration: 0.3s;
		font-size: 15px !important;
		width: 90px;
		text-align: center;
		justify-content: center;
	}

	/deep/ .u-subsection--button__bar {
		background-color: inherit !important;
		border-bottom: 2px solid #0166fc;
		/* width: 85px !important; */
		height: 45px !important;
		border-radius: 0px !important;
	}

	.scroll-view {
		width: 95% !important;
		height: 100rpx;
		line-height: 100rpx;
		white-space: nowrap;
		overflow: hidden;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 6px;
		margin: 0 auto;
	}



	.scroll-view .scroll-items {
		display: inline-block;
		width: 49.5%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #FFFFFF;
		border-radius: 6px;
	}

	.scroll-view .scroll-itembs {
		display: inline-block;
		width: 49.5%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #1A1B24;
		border-radius: 6px;
		color: #ffffff !important;
	}

	.scroll-view .scroll-itemb {
		display: inline-block;
		width: 49.5%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;

	}

	.scroll-view .scroll-item {
		display: inline-block;
		width: 49.5%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;
		color: #828397;
	}



	.jys /deep/.u-subsection--button {
		height: 50px;
		background-color: #F7F8FA !important;

	}

	.jybs /deep/.u-subsection--button {
		height: 50px;


	}

	/deep/.u-subsection__item__text {
		font-size: 15px !important;
	}

	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
</style>