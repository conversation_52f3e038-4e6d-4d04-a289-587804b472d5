<template>
	<view :class="$store.state.bgColor=='black' ? 'jybs' :'jys'">
		<!-- 头部切换币 -->
		<view
			style="display: flex; justify-content: space-between; align-items: right;  margin: 20px 15px 0;padding: 5px 10px;border-radius: 10px; box-shadow: rgba(204, 204, 204, 0.34) 0px 0px 26px 0px;">
			<!-- <view v-if="$store.state.bgColor=='black'"
				style="flex: 1;height: 20px; line-height: 20px; font-size: 16px; font-weight: bold; color: #fff; "
				@click="bzsClick()">
				<view>{{currency_name}}<uni-icons type="bottom" size="14"
						style="font-weight: bold; color:#fff; "></uni-icons></view>
			</view> -->
			<view v-if="$store.state.bgColor=='while'"
				style="flex: 1;height: 20px; line-height: 20px; font-size: 14px; font-weight: bold;  "
				@click="bzsClick()">
				<view>{{currency_name }}<uni-icons type="bottom" size="14" style="font-weight: bold;  "></uni-icons>
				</view>
			</view>
			<view
				style=" text-align: right; color: #0164f7; display: flex; justify-content: center;align-items: center; ">

				<!-- <u-image v-if="isShow==0" src="../../static/<EMAIL>" @click="collects()" width="70"
					height="70"></u-image>
				<u-image v-if="isShow==1" src="../../static/<EMAIL>" @click="collects()" width="70"
					height="70"></u-image> -->
				<view style="font-size: 16px; font-weight: bold; " v-if="symbolQuotation.now_price">
					{{symbolQuotation.now_price | setPrecision(symbolQuotation.precision_length)}}
				</view>
				<view
					style="margin-left: 10px; font-size: 14px; display :flex; justify-content: center; align-items: center;">
					<view>{{symbolQuotation.change}}</view>
					<view v-if="symbolQuotation.now_price" style="height: 15px; line-height: 15px;">
						<img src="../../static/Up_S <EMAIL>" v-if="$utils.getColor(symbolQuotation.now_price)==12"
							style="width: 15px;" alt="">
						<img src="../../static/Down_S <EMAIL>"
							v-else-if="$utils.getColor(symbolQuotation.now_price)==13" style="width: 15px;" alt="">
						<img src="../../static/Down_S <EMAIL>"
							v-else-if="$utils.getColor(symbolQuotation.now_price)==14" style="width: 15px;" alt="">
					</view>
				</view>
			</view>
		</view>
		<view style="width: 95%;  height: 395px;  padding: 0px 10px; 5px">
			<!-- <view class="button-sp-area">
		<!-- <button class="mini-btn" type="default" size="mini" @click="ChangePeriod(-1)">分时</button> -->
			<!-- <button class="mini-btn" type="default" size="mini" @click="ChangePeriod(4)">1M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(5)">5M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(6)">15M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(7)">30M</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(8)">1H</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(0)">1D</button>
		<button class="bmini-btn" type="default" size="mini" @click="ChangePeriod(1)">1W</button> -->
			<!-- 	<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(2)">月</button>
		<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(3)">年</button> -->

			<!-- <button class="mini-btn" type="default" size="mini" @click="ChangeDepthChart()">深度图</button> -->
			<!-- </view> -->
			<view style="display: flex; justify-content: center; text-align: center; align-items: center;">
				<!-- <view v-if="$store.state.bgColor=='black'"
					style="padding-right: 10px; font-size:15px; font-weight: bold; width:100px; color: #fff;"
					@click="zbclick()">
					<view>indicator<uni-icons type="bottom" size="14"
							style="font-weight: bold; color: #fff;"></uni-icons></view>
				</view>
				<view v-if="$store.state.bgColor=='while'"
					style="padding-right: 10px; font-size:15px; font-weight: bold; width:100px; " @click="zbclick()">
					<view>indicator<uni-icons type="bottom" size="14" style="font-weight: bold;"></uni-icons></view>
				</view> -->
				<view style="width: 100%;overflow: hidden; overflow-x: scroll;white-space: nowrap; ">
					<!-- 	<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0, 'EMPTY')">EMPTY</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0,'BOLL')">ABOLL</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'BOLL')">BOLL</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
				<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'BOLL')">BOLL</button> -->

					<view @click="ChangePeriod(4)" :class="pids==4 ? sdhbts : dhbts" style="border-radius: 100px;">1M
					</view>
					<!-- <view @click="ChangePeriod(5)":class="pids==5 ? sdhbts : dhbts">5M</view> -->
					<view @click="ChangePeriod(6)" :class="pids==6 ? sdhbts : dhbts" style="border-radius: 100px;">15M
					</view>
					<view @click="ChangePeriod(7)" :class="pids==7 ? sdhbts : dhbts" style="border-radius: 100px;">30M
					</view>
					<view @click="ChangePeriod(8)" :class="pids==8 ? sdhbts : dhbts" style="border-radius: 100px;">1H
					</view>
					<view @click="ChangePeriod(0)" :class="pids==0 ? sdhbts : dhbts" style="border-radius: 100px;">1D
					</view>
					<!-- <view @click="ChangePeriod(1)" :class="pids==1 ? sdhbts : dhbts">1W</view> -->

				</view>
			</view>
			<view class='divchart' style='background-color:#0e182e;'>

				<!-- k线 -->
				<div id="container" class="kline" style="width: 100%;height: 330px;">
					<div id="k-line-chart" style="width: 100%; height: 100%;">
						<div id="candle_pane"></div>
					</div>

				</div>
			</view>


		</view>
		<!-- <view style="height:5px"></view> -->



		<view style="">


			<!-- 
			<view v-if="$store.state.bgColor=='black'"
				style="display: flex; background: #1A1B24; justify-content: center; align-items: center; width: 100%;line-height: 68px;font-size:15px;border-bottom: 2px solid #22252F;">
				<u-subsection :list="navs" inactiveColor="#828397" activeColor="#ffffff" :current="currentNav"
					@change="sectionChange"></u-subsection>
			</view> -->
			<!-- m秒合约和合约 -->
			<!-- 
			<view style=" width: 100%; font-size: 15px;" v-if="$store.state.bgColor=='while'">
				<u-subsection :list="navs" :current="currentNav" @change="sectionChange"></u-subsection>
			</view> -->

			<view v-show="currentNav == 0"
				style="width:100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
				<!-- 顶部选项卡 -->
				<!-- 		<view style="height: 8px;"></view>
				<view class="scroll-view" style="background-color: #2F3142;" v-if="$store.state.bgColor=='black'">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
						:class="tabIndex===index?'scroll-itembs':'scroll-itemb'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view>
 -->

				<view class="scroll-viewws" style="background-color: #F5F6FAFF;" v-if="$store.state.bgColor=='while'">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-viewws"
						:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view>

				<!-- <view class="scroll-view">
					<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
						:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
						:id="'tab'+index">{{item}}</view>
				</view> -->
				<!-- 交易 -->
				<swiper :duration="100" :current="tabIndex" @change="onChangeTab"
					style="overflow: hidden;width:95%;padding-left:15px; height: 500px; ">
					<swiper-item>
						<scroll-view scroll-y="true" enable-flex="true" class="section-area">
							<u--form labelPosition="left" :model="model1" ref="uForm">
								<u-form-item ref="item1">
									<view
										:style="$store.state.bgColor=='black'?'width: 98%; margin-top: 12px; font-size: 12px; color: #ffffff;':'width: 98%; margin-top: 12px; font-size: 12px;'">



										<view
											style="display: flex; justify-content: center; align-items: center; width: 100%;"
											@click="shows = true">
											<view style="flex: 1;font-size: 12px; font-weight: bold;">
												{{i18n.multiplier}}
											</view>
											<!-- <view style="width: 100%;" >
												<u--input
													v-model="multiple0" :disabled="true" disabledColor="#222"
													placeholder="" inputAlign="center" style="" class="bsone"
													color="#ffffff" suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold; "></u--input>

											</view> -->

											<view style="width: 50%;"><u--input v-model="multiple0" :disabled="true"
													disabledColor="#fff" placeholder="" inputAlign="center" style=""
													suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold; "></u--input>

											</view>


										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center; color: ;">
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;"
												class="wnss">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="hyzs" activeColor="#0166fc" size="40"
														inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;"
												class="bnss">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="hyzs" activeColor="#0166fc" size="40"
														inactiveColor="#424656">
													</u-switch></view>
											</view>
											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="stop_loss_price" :min='0'
														:disabled="hyzsd" button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#FFFFFF"
														:color="hyzsc" v-model="stop_loss_price" :min='0'
														:disabled="hyzsd" button-size="70"></u-number-box></view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="hyzy" activeColor="#0166fc" size="40"
														:min='0' inactiveColor="#424656">
													</u-switch></view>
											</view>
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="hyzy" activeColor="#0166fc" size="40"
														:min='0' inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="target_profit_price" :min='0'
														:disabled="hyzyd" button-size="70"></u-number-box></view>
											</view>



											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%" :color="hyzyc" :min='0'
														v-model="target_profit_price" :disabled="hyzyd" iconStyle=""
														bgColor="#FFFFFF" button-size="70"></u-number-box></view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.buy_quantity}}</view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="number" integer
														:min="symbolQuotation.lever_min_share"
														:max="symbolQuotation.lever_max_share"
														button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">

												<view><u-number-box inputWidth="100%" v-model="number" iconStyle=""
														bgColor="#FFFFFF" :min="symbolQuotation.lever_min_share"
														:max="symbolQuotation.lever_max_share" button-size="70"
														:step="0.1"></u-number-box></view>
											</view>
										</view>

										<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
											v-if="$store.state.bgColor=='while'"></view>
										<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
											v-if="$store.state.bgColor=='black'"></view>

										<view style="width: 100%; font-size: 12px; font-weight: bold;">
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.each}}</view>
												<view>
													{{i18n.each}}={{this.each_piece}}{{this.currency_name}}/{{this.legal_name}}
												</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_commission}}</view>
												<view>{{ handlingFee }}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_margin}}</view>
												<view>{{margin}}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.balance}}</view>
												<view>{{users.usdt}}</view>
											</view>
										</view>
										<view style="width: 100%; ">
											<view style="display: flex; width: 100%; padding-top: 10px;">
												<!--#ifndef APP-PLUS-->
												<u-button :loading="xdloading" :disabled="mrdis" loadingText=""
													@click="orderClicks(1,1)" style=" border-radius: 100rpx; background: #0166fc; 
											    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_up}}</u-button>
												<!--#endif -->
												<!--#ifdef APP-PLUS-->
												<button @click="orderAppClicks()" style=" border-radius: 100rpx; background: #0166fc; 
											    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_down}}</button>
												<!--#endif -->
												<u-button :loading="xdmloading" :disabled="mcdis" loadingText=""
													@click="orderClicks(2,1)" style=" border-radius: 100rpx; background: #F23C48;
											    border: 0;  width: 49%; height: 35px;
												line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{$t('trade.buy_down')}}</u-button>
											</view>
										</view>
									</view>
								</u-form-item>
							</u--form>
						</scroll-view>
					</swiper-item>

					<swiper-item>
						<scroll-view scroll-y="true" enable-flex="true" class="section-area">
							<u--form labelPosition="left" :model="model1" ref="uForm">
								<u-form-item ref="item1">
									<view
										:style="$store.state.bgColor=='black'?'width: 98%; margin-top: 12px; font-size: 12px; color: #ffffff;':'width: 98%; margin-top: 12px; font-size: 12px;'">


										<view
											style="margin-top: 0px; margin-bottom: 20px; width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view style="font-weight: bold;">{{i18n.price}}</view>
											</view>


											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="target_price" :min="1"
														button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%" v-model="target_price"
														iconStyle="" bgColor="#FFFFFF" :min="1"
														button-size="70"></u-number-box></view>
											</view>
										</view>
										<view
											style="display: flex; justify-content: center; align-items: center; width: 100%;"
											@click="gshows = true">
											<view style="flex: 1;font-size: 12px; font-weight: bold;">
												{{i18n.multiplier}}
											</view>


											<view style="width: 50%;" v-if="$store.state.bgColor=='black'"><u--input
													v-model="multiple1" :disabled="true" disabledColor="#222"
													placeholder="" inputAlign="center" style="height: 25px;"
													class="bsone" color="#ffffff" suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold; "></u--input>

											</view>

											<view style="width: 50%;" v-if="$store.state.bgColor=='while'">
												<u--input v-model="multiple1" :disabled="true" disabledColor="#ffffff"
													placeholder="" inputAlign="center" style="height: 25px;"
													suffixIcon="arrow-right"
													suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
											</view>

										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="ghyzs" activeColor="#0166fc"
														size="40" :min='0' inactiveColor="#424656">
													</u-switch></view>
											</view>
											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.stop_loss}}</view>
												<view><u-switch space="2" v-model="ghyzs" activeColor="#0166fc"
														size="40" :min='0' inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="gstop_loss_price" :min='0'
														:disabled="ghyzsd" button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%"
														v-if="$store.state.bgColor=='while'" iconStyle=""
														bgColor="#FFFFFF" :color="ghyzsc" :min='0'
														v-model="gstop_loss_price" :disabled="ghyzsd"
														button-size="70"></u-number-box></view>
											</view>
										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view v-if="$store.state.bgColor=='black'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="ghyzy" activeColor="#0166fc"
														size="40" inactiveColor="#424656">
													</u-switch></view>
											</view>

											<view v-if="$store.state.bgColor=='while'"
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.take_prodit}}</view>
												<view><u-switch space="2" v-model="ghyzy" activeColor="#0166fc"
														size="40" inactiveColor="rgb(230, 230, 230)">
													</u-switch></view>
											</view>
											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" iconStyle="" bgColor="#12151B"
														color="#fff" v-model="gtarget_profit_price" :min='0'
														:disabled="ghyzsd" button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%" :color="ghyzyc" :min='0'
														v-model="gtarget_profit_price" :disabled="ghyzyd" iconStyle=""
														bgColor="#FFFFFF" button-size="70"></u-number-box></view>
											</view>

										</view>
										<view class="composings"
											style="width: 100%; display: flex; justify-content: center; align-items: center;">
											<view
												style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
												<view>{{i18n.buy_quantity}}</view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='black'"
												class="bnss">
												<view><u-number-box inputWidth="100%" @blur="onChange" v-model="gnumber"
														iconStyle="" bgColor="#12151B"
														:min="symbolQuotation.lever_min_share"
														:max="symbolQuotation.lever_max_share" integer color="#fff"
														button-size="70"></u-number-box></view>
											</view>

											<view style=" width: 50%;" v-if="$store.state.bgColor=='while'"
												class="wnss">
												<view><u-number-box inputWidth="100%" @blur="onChange" v-model="gnumber"
														integer iconStyle="" bgColor="#FFFFFF"
														:min="symbolQuotation.lever_min_share"
														:max="symbolQuotation.lever_max_share" button-size="70"
														:step="0.1"></u-number-box></view>
											</view>
										</view>

										<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
											v-if="$store.state.bgColor=='while'"></view>
										<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
											v-if="$store.state.bgColor=='black'"></view>

										<view style="width: 100%; font-size: 12px; font-weight: bold;">
											<!-- <view style="display: flex; height: 35px; line-height: 35px;"><view
													style="flex: 1;">{{i18n.each}}</view><view>{{i18n.each}} =
													{{this.each_piece}}
													{{this.currency_name}}/{{this.legal_name}}</view></view> -->
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.each}}</view>
												<view>
													{{i18n.each}}={{this.each_piece}}{{this.currency_name}}/{{this.legal_name}}
												</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_commission}}</view>
												<view>{{ ghandlingFee }}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.estimated_margin}}</view>
												<view>{{gmargin}}</view>
											</view>
											<view style="display: flex; height: 35px; line-height: 35px;">
												<view style="flex: 1;">{{i18n.balance}}</view>
												<view>
													<!-- {{users.lever_wallet.balance[0].lever_balance}} -->{{users.usdt}}
												</view>
											</view>
										</view>
										<view style="width: 100%; margin-bottom: 30px;">
											<view style="display: flex; width: 100%; padding-top: 10px;">
												<!--#ifndef APP-PLUS-->
												<u-button :loading="gdloading" :disabled="gmrdis" loadingText=""
													@click="orderClicks(1,0)" style="background: #0166fc;  border-radius: 100rpx;
															    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_up}}</u-button>
												<!--#endif -->
												<!--#ifdef APP-PLUS-->
												<button @click="orderAppClicks()" style="background: #0166fc;  border-radius: 100rpx;
															    border: 0;  width:49%; font-size: 12px; height: 35px; color: #FFFFFF;
													line-height: 45px;">{{i18n.buy_down}}</button>
												<!--#endif -->
												<u-button :loading="gdmloading" :disabled="gmcdis" loadingText=""
													@click="orderClicks(2,0)"
													style="background: #F23C48; border-radius: 100rpx;
															    border: 0;  width: 49%; height: 35px;
													line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{$t('trade.buy_down')}}</u-button>
											</view>
										</view>
									</view>
								</u-form-item>
							</u--form>
						</scroll-view>
					</swiper-item>
				</swiper>

			</view>
			<view v-show="currentNav == 1"
				:style="$store.state.bgColor=='black'?'padding: 10px; color:#fff':'padding: 10px;'">
				<view
					style="margin-top: 15px; margin-bottom: 20px; width: 98%; display: flex; justify-content: center; align-items: center;">
					<view style="display: flex; flex: 1; width: 50%; justify-content: start;align-items: center;">
						<view style="font-weight: bold; text-align:center; font-size: 12px;">{{i18n.open_number}}</view>
					</view>
					<view style=" width: 50%;" v-if="$store.state.bgColor=='black'" class="bnss">
						<view><u-number-box inputWidth="100%" iconStyle="" v-model="kcnumber" color="#fff"
								bgColor="#12151B" :min="1" button-size="70"></u-number-box></view>
					</view>
					<view style=" width: 50%;" v-if="$store.state.bgColor=='while'" class="wnss">
						<view><u-number-box inputWidth="100%" iconStyle="" v-model="kcnumber" bgColor="#FFFFFF" :min="1"
								button-size="70"></u-number-box></view>
					</view>
				</view>
				<view style="display: flex; justify-content: center; align-items: center; width: 98%;"
					@click="kcshows = true">
					<view style="flex: 1;font-size: 12px; font-weight: bold; ">{{i18n.opening_time}}</u-icon></view>
					<view style="width: 50%;" v-if="$store.state.bgColor=='black'"><u--input v-model="kcvalues"
							:disabled="true" disabledColor="#222" placeholder="" inputAlign="center" color="#fff"
							class="bsone" style="height: 25px;" suffixIcon="arrow-right"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input></view>
					<view style="width: 50%;" v-if="$store.state.bgColor=='while'"><u--input v-model="kcvalues"
							:disabled="true" disabledColor="#ffffff" placeholder="" inputAlign="center"
							style="height: 25px;" suffixIcon="arrow-right"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input></view>
				</view>
				<view style="height: 3rpx;background-color: #F5F5F4; margin-top: 20px;"
					v-if="$store.state.bgColor=='while'"></view>
				<view style="height: 3rpx;background-color: #424656; margin-top: 20px;"
					v-if="$store.state.bgColor=='black'"></view>

				<view style="width: 100%; font-size: 12px; margin-top: 10px; font-weight: bold;">

					<view style="display: flex; height: 40px; line-height: 40px;">
						<view style="flex: 1;">{{i18n.profit_rate}}</view>
						<view>{{profit_ratio}}%</view>
					</view>
					<view style="display: flex; height: 40px; line-height: 40px;">
						<view style="flex: 1;">{{i18n.estimated_commission}}</view>
						<view>{{parseFloat(mhyprices).toFixed(2)}}</view>
					</view>
					<view style="display: flex; height: 35px; line-height: 35px;">
						<view style="flex: 1;">{{i18n.balance}}</view>
						<view><!-- {{users.lever_wallet.balance[0].lever_balance}} -->{{users.usdt}}</view>
					</view>
				</view>
				<view style="width: 100%; ">
					<view style="display: flex; width: 100%; padding-top: 10px;">
						<u-button :loading="mhydloading" :disabled="mhymrdis" loadingText="" @click="mhyOrder(1)" style=" border-radius: 100rpx;background: #0166fc; 
											    border: 0;  width:49%; font-size: 12px; height: 45px; color: #FFFFFF;
							line-height: 45px;">{{i18n.buy_up}}</u-button>
						<u-button :loading="mhymloading" :disabled="mhymcdis" loadingText="" @click="mhyOrder(2)" style=" border-radius: 100rpx;background: #F23C48;
											    border: 0;  width: 49%; height: 45px;
							line-height: 45px;  font-size: 12px; margin-left: 13px;color: #FFFFFF;">{{i18n.buy_down}}</u-button>
					</view>
				</view>
			</view>

		</view>
		<u-picker :show="shows" v-if="$store.state.bgColor=='black'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="cancel" @confirm="confirm" @change="changeHandler"
			style="border-radius: 18px; background: #222;" class="bnsss">

		</u-picker>
		<u-picker :show="shows" v-if="$store.state.bgColor=='while'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="cancel" @confirm="confirm" @change="changeHandler"
			style="border-radius: 18px; background: #fff;" class="wnsss">

		</u-picker>

		<u-picker :show="gshows" v-if="$store.state.bgColor=='black'" ref="guPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="gcancel" @confirm="gconfirm" @change="changeHandler"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker :show="gshows" v-if="$store.state.bgColor=='while'" ref="guPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" @cancel="gcancel" @confirm="gconfirm" @change="changeHandler"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-picker :show="kcshows" ref="uPickers" v-if="$store.state.bgColor=='black'" :itemHeight="100"
			:defaultIndex="kcdfindex" :columns="kccolumns" keyName="seconds" @cancel="kccancel" @confirm="kcconfirm"
			@change="changeHandler" style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>
		<u-picker :show="kcshows" ref="uPickers" v-if="$store.state.bgColor=='while'" :itemHeight="100"
			:defaultIndex="kcdfindex" :columns="kccolumns" keyName="seconds" @cancel="kccancel" @confirm="kcconfirm"
			@change="changeHandler" style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-popup :show="ordershow" :round="30" :duration="300" :closeable="true" @close="close" @open="open">
			<view
				:style="$store.state.bgColor=='black'?'height:330px;  padding: 6px;background: #22252F; color: #fff;':''">
				<view class="u-content" style="text-align: center;">
					<view class="u-content__circle"
						:style="this.$store.state.bgColor=='black'?'background: #22252F;':''">
						<u--image
							:src="$store.state.bgColor=='black'?'../../static/<EMAIL>':'../../static/<EMAIL>'"
							width="140" style="margin: 0 auto;"></u--image>
					</view>
					<view
						style="margin-top: 40px; padding-left: 10px; font-size: 18px; font-weight: bold;margin: 0 auto;">
						{{i18n.order_confirmed}}
					</view>
				</view>
				<!-- <view
					style="display: flex; width: 80%; padding-top: 10px; justify-content: center; align-items: center;text-align: center; padding: 50px 50px 25px 50px;">
					<button @click='orders()' style="background: #FFFFFF; border-radius: 100px;
							border: 0;  flex: 1; width:49%; font-size: 12px; height: 33px; color: #222222;
					line-height: 33px;">{{i18n.view_order}}</button>
					<button @click="can()" style="background: #0166fc;border-radius: 100px;
							border: 0;  width: 49%; height: 33px;
					line-height: 33px;  font-size: 12px;flex: 1; margin-left: 13px;color: #FFFFFF;">{{i18n.confirm}}</button>
				</view> -->
			</view>
		</u-popup>

		<u-popup :show="zbshow" mode="bottom" @close="zbclose" @open="zbopen">
			<!-- <view style="display: flex;flex-direction: column; background-color:#282A37;padding-bottom: 100rpx;"> -->
			<view
				:style="$store.state.bgColor=='black'?'display: flex;flex-direction: column; background-color:#22252F;color:#fff;padding-bottom: 100rpx;':'display: flex;flex-direction: column; background-color:#fff;padding-bottom: 100rpx;'">
				<view style="color: #222;font-weight: bold; margin-left: 32rpx;margin-top: 60rpx;margin-bottom: 30rpx;">
					<!-- 主图 -->
				</view>
				<view style="display: flex;flex-direction: row; flex-wrap: wrap;margin-left: 32rpx; color:#fff">
					<view :class="item.select?'itemview_':'itemview'" v-for="(item,index) in mainIndicaList"
						:style="$store.state.bgColor=='black'?'color:#fff':''" @click="ChangeIndex(0,item.name,item)">
						{{item.name}}
					</view>
					<!-- <view :class="ckone=='BOLL'?'itemview_':'itemview'" @click="ChangeIndex(0,'BOLL')">BOLL</view>
						  <view :class="ckone=='SAR'?'itemview_':'itemview'" @click="ChangeIndex(0,'SAR')">SAR</view>
						  <view :class="ckone=='EMA'?'itemview_':'itemview'" @click="ChangeIndex(0,'EMA')">EMA</view>
						   <view :class="ckone=='BBI'?'itemview_':'itemview'" @click="ChangeIndex(0,'BBI')">BBI</view> -->
				</view>


				<view style="color: #222;font-weight: bold;margin-left: 32rpx;margin-top: 60rpx;margin-bottom: 30rpx;">
					<!-- 指标 -->
				</view>
				<view style="display: flex;flex-direction: row; flex-wrap: wrap;margin-left: 32rpx;">
					<!-- <view :class="item.select?'itemview_':'itemview'">BOLL -->

					<view :class="item.select?'itemview_':'itemview'" v-for="(item,index) in indicaIndexList"
						:style="$store.state.bgColor=='black'?'color:#fff':''" @click="ChangeIndex(1,item.name,item)">
						{{item.name}}
					</view>
					<!-- 	<view :class="cktwo=='MACD'?'itemview_':'itemview'" style="display: none;" @click="ChangeIndex(1,'MACD')">MACD</view>
						<view :class="cktwo=='KDJ'?'itemview_':'itemview'" @click="ChangeIndex(1,'KDJ')">KDJ</view>
						<view :class="cktwo=='BIAS'?'itemview_':'itemview'" @click="ChangeIndex(1,'BIAS')">BIAS</view>
						<view :class="cktwo=='RSI'?'itemview_':'itemview'" @click="ChangeIndex(1,'RSI')">RSI</view>
						<view :class="cktwo=='OBV'?'itemview_':'itemview'" @click="ChangeIndex(1,'OBV')">OBV</view>
				<view :class="cktwo=='BRAR'?'itemview_':'itemview'" @click="ChangeIndex(1,'BRAR')">BRAR</view>
				<view :class="cktwo=='ROC'?'itemview_':'itemview'" @click="ChangeIndex(1,'ROC')">ROC</view>
				<view :class="cktwo=='DMA'?'itemview_':'itemview'" @click="ChangeIndex(1,'DMA')">DMA</view>
				<view :class="cktwo=='TRIX'?'itemview_':'itemview'" @click="ChangeIndex(1,'TRIX')">TRIX</view>
				<view :class="cktwo=='DMI'?'itemview_':'itemview'" @click="ChangeIndex(1,'DMI')">DMI</view>
				<view :class="cktwo=='CCI'?'itemview_':'itemview'" @click="ChangeIndex(1,'CCI')">CCI</view>
				<view :class="cktwo=='CR'?'itemview_':'itemview'" @click="ChangeIndex(1,'CR')">CR</view>
				<view :class="cktwo=='PSY'?'itemview_':'itemview'" @click="ChangeIndex(1,'PSY')">PSY</view>
				<view :class="cktwo=='VR'?'itemview_':'itemview'" @click="ChangeIndex(1,'VR')">VR</view>
				<view :class="cktwo=='WR'?'itemview_':'itemview'" @click="ChangeIndex(1,'WR')">WR</view>
				<view :class="cktwo=='MTM'?'itemview_':'itemview'" @click="ChangeIndex(1,'MTM')">MTM</view>
				<view :class="cktwo=='EMV'?'itemview_':'itemview'" @click="ChangeIndex(1,'EMV')">EMV</view> -->

				</view>

			</view>
		</u-popup>
		<u-popup :show="bzsshow" mode="left" @close="bzsclose" @open="bzsopen" @touchmove.stop.prevent
			customStyle="border-radius: 0px !important; overflow-y: scroll;overscroll-behavior: none">
			<view style=" " @click="showBZ(item,true)" :class="currency_id==item.id?'showItems':'noShowItems'"
				v-for="(item,index) in indicaList">
				<view>{{item.name}}</view>
			</view>

			<!-- <view style="width: 130px; text-align: center;padding: 20px; --tw-bg-opacity: 1 !important; border-bottom: 2px solid #e5e7eb;
					">
					   <view>BTCUSD</view>
					</view> -->

		</u-popup>



		<tab-bar></tab-bar>

	</view>

</template>

<script>
	import HQChart from '@/uni_modules/jones-hqchart2/js_sdk/umychart.uniapp.h5.js'

	import {
		JSCommon
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js'
	import {
		JSCommonHQStyle
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.style.wechat.js'
	import {
		JSConsole
	} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.console.wechat.js'

	import {
		KLineChartPro,
		DefaultDatafeed,
		loadLocales,

	} from '@klinecharts/pro';
	import {
		init,
		registerOverlay,
		registerIndicator,
		registerLocale,
		registerStyles
	} from 'klinecharts';
	// import * as klinecharts from 'klinecharts';
	import '@klinecharts/pro/dist/klinecharts-pro.css';
	import {
		mapState
	} from 'vuex'

	function genData(timestamp = new Date().getTime(), length = 500) {
		// 数据生成函数保持不变
		// ...
		let basePrice = 5000
		timestamp = Math.floor(timestamp / 1000 / 60) * 60 * 1000 - length * 60 * 1000
		const dataList = []
		for (let i = 0; i < length; i++) {
			const prices = []
			for (let j = 0; j < 4; j++) {
				prices.push(basePrice + Math.random() * 60 - 30)
			}
			prices.sort()
			const open = +(prices[Math.round(Math.random() * 3)].toFixed(2))
			const high = +(prices[3].toFixed(2))
			const low = +(prices[0].toFixed(2))
			const close = +(prices[Math.round(Math.random() * 3)].toFixed(2))
			const volume = Math.round(Math.random() * 100) + 10
			const turnover = (open + high + low + close) / 4 * volume
			dataList.push({
				timestamp,
				open,
				high,
				low,
				close,
				volume,
				turnover
			})

			basePrice = close
			timestamp += 60 * 1000
		}
		return dataList
	}



	registerLocale('zh', {
		time: '時間：',
		open: '開：',
		high: '高：',
		low: '低：',
		close: '收：',
		volume: '成交量：'
	});
	registerLocale('jp', {
		time: '時間：',
		open: '開始：',
		high: '高：',
		low: '安：',
		close: '終値：',
		volume: '取引高：'
	});
	registerLocale('en', {
		time: 'Time:',
		open: 'Open:',
		high: 'High:',
		low: 'Low:',
		close: 'Close:',
		volume: 'Volume:'
	});
	registerLocale('kor', {
		time: '시간:',
		open: '시가:',
		high: '고가:',
		low: '저가:',
		close: '종가:',
		volume: '거래량:'
	});
	registerLocale('th', {
		time: 'เวลา:',
		open: 'เปิด:',
		high: 'สูงสุด:',
		low: 'ต่ำสุด:',
		close: 'ปิด:',
		volume: 'ปริมาณการซื้อขาย:'
	});
	registerLocale('vie', {
		time: 'Thời gian:',
		open: 'Mở:',
		high: 'Cao nhất:',
		low: 'Thấp nhất:',
		close: 'Đóng:',
		volume: 'Khối lượng giao dịch:'
	});
	registerLocale('ger', {
		time: 'Zeit:',
		open: 'Eröffnung:',
		high: 'Hoch:',
		low: 'Tief:',
		close: 'Schluss:',
		volume: 'Handelsvolumen:'
	});
	registerLocale('rus', {
		time: 'Время:',
		open: 'Открытие:',
		high: 'Максимум:',
		low: 'Минимум:',
		close: 'Закрытие:',
		volume: 'Объем торгов:'
	});
	registerLocale('spa', {
		time: 'Hora:',
		open: 'Apertura:',
		high: 'Máximo:',
		low: 'Mínimo:',
		close: 'Cierre:',
		volume: 'Volumen de operaciones:'
	});
	registerLocale('por', {
		time: 'Hora:',
		open: 'Abertura:',
		high: 'Máxima:',
		low: 'Mínima:',
		close: 'Fechamento:',
		volume: 'Volume de negócios:'
	});
	registerLocale('Ita', {
		time: 'Ora:',
		open: 'Apertura:',
		high: 'Massimo:',
		low: 'Minimo:',
		close: 'Chiusura:',
		volume: 'Volume di negoziazione:'
	});
	registerLocale('ara', {
		time: 'الوقت:',
		open: 'الفتح:',
		high: 'الأعلى:',
		low: 'الأدنى:',
		close: 'الإغلاق:',
		volume: 'حجم التداول:'
	});
	registerLocale('tur', {
		time: 'Zaman:',
		open: 'Açılış:',
		high: 'En Yüksek:',
		low: 'En Düşük:',
		close: 'Kapanış:',
		volume: 'İşlem Hacmi:'
	});
	const red = '#f23c48'
	const green = '#0166fc'

	const alphaRed = 'rgba(242, 60, 72, 1.0)'
	const alphaGreen = 'rgba(1, 102, 252, 1.0)'

	registerStyles('green_rise_red_fall', {
		candle: {
			bar: {
				upColor: green,
				downColor: red,
				upBorderColor: green,
				downBorderColor: red,
				upWickColor: green,
				downWickColor: red
			},
			priceMark: {
				last: {
					upColor: green,
					downColor: red
				}
			}
		},
		indicator: {
			ohlc: {
				upColor: alphaGreen,
				downColor: alphaRed
			},
			bars: [{
				style: 'fill',
				borderStyle: 'solid',
				borderSize: 1,
				borderDashedValue: [2, 2],
				upColor: alphaGreen,
				downColor: alphaRed,
				noChangeColor: '#888888'
			}],
			circles: [{
				style: 'fill',
				borderStyle: 'solid',
				borderSize: 1,
				borderDashedValue: [2, 2],
				upColor: alphaGreen,
				downColor: alphaRed,
				noChangeColor: '#888888'
			}]
		}
	});
	registerOverlay({
		name: 'circle',
		needDefaultPointFigure: true,
		needDefaultXAxisFigure: true,
		needDefaultYAxisFigure: true,
		totalStep: 3,
		createPointFigures: ({
			coordinates
		}) => {
			if (coordinates.length === 2) {
				const xDis = coordinates[1].x - coordinates[0].x;
				const yDis = coordinates[1].y - coordinates[0].y;
				const radius = Math.sqrt(xDis * xDis + yDis * yDis)
				return {
					key: 'circle',
					type: 'circle',
					attrs: {
						...coordinates[0],
						r: radius
					},
					styles: {
						style: 'stroke_fill'
					}
				}
			}
			return []
		}
	}, );

	var g_KLine = {
		JSChart: {
			KLineType: ''
		}
	}
	export default {
		data() {
			let data = {
				scrollH: 600,
				// 顶部选项卡
				scrollInto: "",
				tabIndex: 0,
				isHide: false,
				scrollHight1: 500,
				scrollHight2: 500,
				scrollHight3: 500,
				navs: [],
				currentNav: 0,
				currentNavcc: 0,
				indicatorList: ['VOL'],
				miansList: [],
				navsList: [],
				newsList: [],
				bgStatusOne: '',
				bgStatusTwo: '',
				indexList: [],
				show: false,
				timer: null,
				values: '',
				each_piece: 1,
				mainIndicaList: [{
						name: 'MA',
						select: false
					},
					{
						name: 'BOLL',
						select: false
					},
					{
						name: 'SAR',
						select: false
					},
					{
						name: 'EMA',
						select: false
					},
					{
						name: 'BBI',
						select: false
					}
				],
				indicaIndexList: [{
						name: 'VOL',
						select: true
					},
					{
						name: 'MACD',
						select: false
					},
					{
						name: 'KDJ',
						select: false
					},
					{
						name: 'BIAS',
						select: false
					},
					{
						name: 'RSI',
						select: false
					},
					{
						name: 'OBV',
						select: false
					},
					{
						name: 'BRAR',
						select: false
					},
					{
						name: 'ROC',
						select: false
					},
					{
						name: 'DMA',
						select: false
					},
					{
						name: 'TRIX',
						select: false
					},
					{
						name: 'DMI',
						select: false
					},
					{
						name: 'CCI',
						select: false
					},
					{
						name: 'CR',
						select: false
					},
					{
						name: 'PSY',
						select: false
					}, {
						name: 'VR',
						select: false
					}, {
						name: 'WR',
						select: false
					},
					{
						name: 'MTM',
						select: false
					},
					{
						name: 'EMV',
						select: false
					}
				],
				kcvalues: '',
				kcnumber: 1,
				showSex: false,
				value11: false,
				ordershow: false,
				dfindex: [0],
				kcdfindex: [0],
				isShow: 0,
				hyzs: false,
				hyzy: false,
				hyzsd: true,
				hyzyd: true,
				hyzyc: "#BDC1D0",
				hyzsc: "#BDC1D0",
				ghyzs: false,
				ghyzy: false,
				ghyzsd: true,
				ghyzyd: true,
				xdloading: false,
				xdmloading: false,
				mrdis: false,
				mcdis: false,
				gdloading: false,
				gdmloading: false,
				gmrdis: false,
				gmcdis: false,

				mhydloading: false,
				mhymloading: false,
				mhymrdis: false,
				mhymcdis: false,
				dhbts: 'dhbts',
				sdhbts: 'sdhbts',
				showItemId: 0,


				pids: 8,
				ghyzyc: "#BDC1D0",
				ghyzsc: "#BDC1D0",
				target_profit_price: 0,
				stop_loss_price: 0,
				gtarget_profit_price: 0,
				gstop_loss_price: 0,
				profit_ratio: 0,
				mhyprices: 0,
				user_lever: 0, // 个人的资金
				margin: 0, //保证金
				handlingFee: 0, //手续费,
				micro_trade_fee: 0, //手续费,
				gmargin: 0, //保证金
				ghandlingFee: 0, //手续费,
				symbolQuotation: {},
				target_price: 1,
				currency_name: "",
				originalQuotation: [],
				zbshow: false,
				quotation: [],
				ckone: "MA",
				cktwo: "VOL",
				legal_name: "",
				currency_id: "",
				buy_direction: "",
				currentIndex: "",
				bzsshow: false,
				indicaList: [],
				price: '',
				multiple0: 0, //倍数
				multiple1: 0, //倍数
				number: 1, //交易手数,
				gnumber: 1, //交易手数,
				model1: {
					userInfo: {
						name: 'uView UI',
						sex: '',
					},
				},
				shows: false,
				gshows: false,
				kcshows: false,
				columns: [
					// ['100', '200', '300']
				],
				kccolumns: [
					// ['60s', '300s', '900s']
				],
				symboles: '',
				OriginalSymbol: 'btcusdt',
				ChartWidth: 300,
				ChartHeight: 300,
				opening: 1,
				dataes: [],
				msg: null,
				mhytimeList: [],
				mhytimeArray: [],
				isLoding: "",
				mhyfee: 0,
				mhyusers: {},


				// WSUrl:'wss://www.huobi.com/-/s/pro/ws',	//火币api地址, 需要根据火币网经常调整. 会经常变(https://www.huobi.br.com/en-us/exchange/btc_usdt/)
				// WSUrl:'ws://*************:24419',
				// WSUrl:'wss://www.https://zonhoutai.forexglobal.vip/socket.io/?EIO=3&transport=websocket',

				SocketOpen: false,
				LastSubString: null, //最后一个订阅的数据
				intervals: {
					"1min": "1",
					"5min": "5",
					"15min": "15",
					"30min": "30",
					"60min": "60",
					"1day": "360",
					"1week": "1800",
					"1mon": "10800",
					"1year": "129600"
				},
				periods: {
					"4": "1min",
					"5": "5min",
					"6": "15min",
					"7": "30min",
					"8": "60min",
					"0": "1day",
					"1": "1week",
					"2": "1mon",
					"3": "1year"
				},
				// k线
				chart: '',
				// k线信息
				KLineInformation: {
					Symbol: '',
				},
				KDataList: [],
				Symbol: 'btcusdt.BIT',
				Update: {
					EnableLimit: false, //更新是否限速
					LastUpdateTime: null,
					Frequency: 5000, //更新频率
					Cache: null,
				}
			};

			return data;

		},

		name: 'KLineChart',
		watch: {
			currency_name: function(val) {

			},
			'$store.state.bgColor'(val) {
				this.bgStatusOne = val
			},
			'$store.state.currency'(val) {

				this.currency_name = this.$store.state.currency.currency_name || 'BTC'
				this.legal_name = this.$store.state.currency.legal_name || 'USDT'
				this.currency_id = this.$store.state.currency.currency_id || 0
				this.currentIndex = this.$store.state.currency.currentIndex || 0
				// let buy_direction = buy_direction || 0
				this.buyDirection = 0

				// 	 this.timer = setTimeout(() => {
				// 	       this.getQuotation()
				// 	 this.getDatas()
				// 	 	}, 5400)

				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id
				}
				// this.ChangePeriod(4)

				this.showBZ(item)

			},
			currentNav: function(val) {
				if (val == 1) {
					// if(this.mhytimeList.length>0){
					// 	return
					// }
					this.mhytimeList = []
					var that = this
					const res = this.$H.get('/api/v1/seconds', false).then(res => {

						if (res.type == "ok") {

							res.data.data.map(item => {
								this.mhytimeList.push({
									"id": item.id,
									"seconds": item.seconds,
									"profit_ratio": item.profit_ratio,
									"mhyprices": res.data.fee / 100,
									"fee": res.data.fee / 100

								})
								// const pram=item.seconds
								//      this.mhytimeArray.push({
								//   pram:item.profit_ratio
								//   })
							})

							this.kccolumns = []
							this.kccolumns.push(this.mhytimeList)
							this.kcvalues = this.kccolumns[0][0].seconds
							this.profit_ratio = this.kccolumns[0][0].profit_ratio
							this.mhyprices = this.kccolumns[0][0].mhyprices
							this.mhyfee = this.kccolumns[0][0].fee

						}
					})

				}
			},
			kcnumber: function(val) {
				this.mhyprices = this.mhyfee * val
			},
			kcvalues: function(val) {
				if (this.mhytimeList.length > 0) {
					this.mhytimeList.map(item => {
						if (val == item.seconds) {
							this.profit_ratio = item.profit_ratio
							this.mhyprices = item.mhyprices
							this.mhyfee = item.fee
						}
					})
				}
			},
			symbolQuotation: function(val) {
				this.calc()
				this.gcalc()
			},
			number: function(val) {
				this.calc()
			},
			multiple0: function(val) {

				this.calc()
			},
			gnumber: function(val) {

				if (val == "") {
					this.gnumber == 0
				}
				this.gcalc()
			},
			multiple1: function(val) {

				this.gcalc()
			},
			hyzs: function(val) {

				if (!val) {
					this.hyzsd = true
					this.hyzsc = "#BDC1D0"


					// hyzyc:"#BDC1D0",
				} else {
					this.hyzsd = false
					this.hyzsc = "#222222"
					if (this.stop_loss_price == 0) this.stop_loss_price = this.symbolQuotation.now_price
				}



			},
			hyzy: function(val) {
				if (!val) {
					this.hyzyd = true
					this.hyzyc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.hyzyd = false
					this.hyzyc = "#222222"
					if (this.target_profit_price == 0) this.target_profit_price = this.symbolQuotation.now_price

				}
			},
			ghyzs: function(val) {
				if (!val) {
					this.ghyzsd = true
					this.ghyzsc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.ghyzsd = false
					this.ghyzsc = "#222222"
					if (this.gstop_loss_price == 0) this.gstop_loss_price = this.symbolQuotation.now_price

				}



			},
			ghyzy: function(val) {
				if (!val) {
					this.ghyzyd = true
					this.ghyzyc = "#BDC1D0"
					// hyzyc:"#BDC1D0",
				} else {
					this.ghyzyd = false
					this.ghyzyc = "#222222"
					if (this.gtarget_profit_price == 0) this.gtarget_profit_price = this.symbolQuotation.now_price

				}
			}
		},
		onLoad(options) {
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
			const {
				i18n
			} = this
			this.navs.push(i18n.contracts)
			this.navs.push(i18n.second_contract)

			this.navsList.push(i18n.trade)
			this.navsList.push(i18n.pending_order)

			this.isHide = false
			uni.$on('page-popup', (data) => {
				this.msg = data.msg;
				this.subNames()
			});

			let {
				currency_name,
				legal_name,
				currency_id,
				buy_direction,
				currentIndex
			} = options

			if (this.isLoding == "") {
				this.currency_name = this.$store.state.currency.currency_name || 'BTC'
				this.legal_name = this.$store.state.currency.legal_name || 'USDT'
				this.currency_id = this.$store.state.currency.currency_id || 72
				this.currentIndex = this.$store.state.currency.currentIndex || 0
				// let buy_direction = buy_direction || 0
				this.buyDirection = 0
				// 	 this.timer = setTimeout(() => {
				// 	       this.getQuotation()
				// 	 this.getDatas()
				// 	 	}, 5400)

				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id
				}
				// this.ChangePeriod(4)

				uni.getSystemInfo({
					success: (res) => {

						this.$nextTick(() => {
							this.isLoding = "loading"

						})
					}
				});
			}else{
				// if(this.currency_name==this.$store.state.currency.currency_name || !this.$store.state.currency.currency_name || !bol) return 
				if (this.currency_name == "") {
					this.currency_name = this.$store.state.currency.currency_name || 'BTC'
					this.legal_name = this.$store.state.currency.legal_name || 'USDT'
					this.currency_id = this.$store.state.currency.currency_id || 72
					this.currentIndex = this.$store.state.currency.currentIndex || 0
				} else {
					if (this.$store.state.currency.currency_name) {
						if (this.currency_name != this.$store.state.currency.currency_name) {

							this.currency_name = this.$store.state.currency.currency_name || 'BTC'
							this.legal_name = this.$store.state.currency.legal_name || 'USDT'
							this.currency_id = this.$store.state.currency.currency_id || 72
							this.currentIndex = this.$store.state.currency.currentIndex || 0
						}
					}
				}
				this.buyDirection = 0
				this.symboles = this.currency_name + '/' + this.legal_name
				let item = {
					"currency_name": this.currency_name,
					"legal_name": this.legal_name,
					"currency_id": this.currency_id
				}
				this.showBZ(item)
			}
			// const {i18n} = this
			// this.navs=[]
			// this.navsList=[]
			// this.navs.push(i18n.contracts)
			// this.navs.push(i18n.second_contract)


			// this.navsList.push(i18n.trade)
			// this.navsList.push(i18n.pending_order)

			// this.showBZ(item)

			// this.multiple0 = this.columns[0][0]
			// this.kcvalues = this.kccolumns[0][0]
			// if(!currency_name)
			// {
			// 	 this.ClearChart();

			// }

			// this.getDatas()
			// 	if(this.symbolQuotation.length==0)
			// 	{



			// 	}

		},

		computed: {

			i18n() {
				return this.$t("trade")
			},
			...mapState({
				socket: state => state.socket,
				token: state => state.token,
				users: state => state.users,
				currency: state => state.currency

			})
		},
		onReady() {


			// console.log("[KLineChart::onReady]");
			this.$nextTick(() => {
				this.CreateKLineChart('1', 8);
			});
		},
		onShow() {
			this.$store.commit('changeTabbarIndex', 2)
			this.auths(() => {})
			const {
				i18n
			} = this
			this.navs = []
			this.navsList = []
			this.navs.push(i18n.contracts)
			this.navs.push(i18n.second_contract)
			this.navsList.push(i18n.trade)
			this.navsList.push(i18n.pending_order)

			if (this.$store.state.currency.opening || this.$store.state.currency.opening == 0) {

				if (this.currency_name == this.$store.state.currency.currency_name) {
					this.opening = this.$store.state.currency.opening
				}
			}
			if (!this.opening) {
				this.isShowes(true)
			} else {
				this.isShowes(false)
			}
			let bol = true
			this.getDatas()
			if (!this.bgStatusOne) {
				this.bgStatusOne = this.$store.state.bgColor
			}
			if (this.bgStatusTwo != this.bgStatusOne) {
				bol = false
				if (g_KLine.JSChart) this.ClearChart()
			}
			this.bgStatusTwo = this.$store.state.bgColor
			uni.getSystemInfo({
				success: (res) => {
					var width = res.windowWidth;
					var height = res.windowHeight;
					this.ChartWidth = width - 15;
					// this.ChartHeight=height-130;
					this.ChartHeight = 500;
					this.$nextTick(() => {
						this.getQuotation()
						this.CreateKLineChart('1', 8);
					})
				}
			})




			
		},
		onHide() {
			this.isHide = true
			// this.isShowes()
			// this.$store.commit('setCurrency',{})
			// this.socket.removeListener('daymarket')
			// this.socket.removeListener('kline')

			// this.ClearChart()
		},

		onUnload() {

			uni.$off('page-popup')
			this.isHide = false
			this.socket.removeListener('daymarket')
			this.socket.removeListener('kline')
			this.ClearChart()
		},

		methods: {
			isShowes(bols = true) {
				if (bols) {
					this.mrdis = true,
						this.mcdis = true,

						this.gmrdis = true,
						this.gmcdis = true,


						this.mhymrdis = true,
						this.mhymcdis = true
				} else {
					this.mrdis = false,
						this.mcdis = false,

						this.gmrdis = false,
						this.gmcdis = false,


						this.mhymrdis = false,
						this.mhymcdis = false
				}

			},
			isScs() {
				const _this = this
				const restwo = this.$H.get('/api/v1/optional/list', false).then(restwo => {

					const has = restwo.data.findIndex(item => item.currency_id == this.currency_id)


					if (has > -1) {

						_this.isShow = 1


					} else {
						_this.isShow = 0
					}


				})
			},
			collects() {

				if (this.isShow) {
					this.isShow = 0
					const res = this.$H.post('/api/v1/optional/del', false, {
						currency_id: this.currency_id
					}).then(res => {
						if (res.type == "ok") {
							this.$nextTick(() => {
								this.isShow = 0

							})
						}

					})
				} else {
					this.isShow = 1
					const res = this.$H.post('/api/v1/optional/add', false, {
						currency_id: this.currency_id
					}).then(res => {

						if (res.type == "ok") {
							this.$nextTick(() => {
								this.isShow = 1

							})
						}

					})
				}

			},
			onChange(e) {

				if (this.gnumber == "") {

					this.gnumber = 0;
				}
			},
			newLoads() {
				this.CreateKLineChart('1', 4);
			},
			showBZ(item, isshow = false) {
				this.currency_id = item.currency_id
				this.currency_name = item.currency_name

				this.legal_name = item.legal_name
				this.symboles = this.currency_name + '/' + this.legal_name

				if (!isshow) {
					this.getDatas()

				} else {

					this.currentIndex = item.currentIndex
					this.opening = item.opening


					if (this.opening) {
						this.isShowes(false)
					} else {
						this.isShowes(true)
					}

				}
				this.isScs()

				this.timer = setTimeout(() => {
					// this.originalQuotation=[]
					// this.symbolQuotation={}
					// this.quotation=[]

					this.getQuotation()

				}, 2600)
				// this.ClearChart();
				// this.socket.removeListener('kline')
				// this.socket.removeListener('daymarket')


				this.bzsshow = false
				this.currentNav = 0
				this.hyzs = false,
					this.hyzy = false,
					this.hyzsd = true,
					this.hyzyd = true,
					this.hyzyc = "#BDC1D0",
					this.hyzsc = "#BDC1D0",
					this.ghyzs = false,
					this.ghyzy = false,
					this.ghyzsd = true,
					this.ghyzyd = true,
					this.target_profit_price = 0,
					this.stop_loss_price = 0,
					this.gtarget_profit_price = 0,
					this.gstop_loss_price = 0,
					this.profit_ratio = 0,
					this.mhyprices = 0,
					this.kcnumber = 1
				this.ChangePeriod(this.pids)
				// this.ChangeSymbol(this.currency_name)


			},
			getDatas() {

				const res = this.$H.get('/api/v1/quotation_new', false).then(res => {
					this.indicaList = []

					for (var i = 0; i < res.data.length; i++) {

						res.data[i].quotation.map(item => {
							item.currentIndex = i
							if (this.currency_id == 0) {

								this.currency_id = item.currency_id
							}
							const items = {
								"id": item.currency_id,
								"currency_id": item.currency_id,
								"name": item.show_name,
								"currency_name": item.currency_name,
								"legal_name": item.legal_name,
								"currentIndex": item.currentIndex,
								"opening": item.currency.opening
							}
							this.indicaList.push(items)


						})
					}

				})
			},
			bzsClick() {
				this.bzsshow = true
			},
			zbclick() {
				this.zbshow = true
			},
			zbopen() {

			},
			zbclose() {
				this.zbshow = false

			},
			bzsopen() {

			},
			bzsclose() {
				this.bzsshow = false

			},
			mhyOrder(types) {


				if (types == 1) {
					this.mhydloading = true
					this.mhymcdis = true;
				} else {
					this.mhymloading = true
					this.mhymrdis = true;
				}


				const {
					symbolQuotation,
					currency_id,
					kcnumber,
					kcvalues
				} = this
				const match_ids = Number(symbolQuotation.id)


				// return false
				this.$H.post('/api/v1/MicroOrder/submit', false, {
					type: types,
					match_id: match_ids,
					currency_id: 1,
					seconds: kcvalues,
					number: kcnumber
				}).then(result => {


					if (types == 1) {
						this.mhydloading = false
						this.mhymcdis = false;
					} else {
						this.mhymloading = false
						this.mhymrdis = false;
					}


					if (result.type == "ok") {

						this.ordershow = true


						this.$store.commit('setOrNum', 2)
						getApp().setUser()
					}

				})





			},
			orders() {
				this.ordershow = false
				uni.navigateTo({
					url: "/pages/order/order"
				})
			},
			can() {
				this.ordershow = false
			},
			subNames() {

				if (this.msg == "ok") {

					const subNVue = uni.getSubNVueById('modal')
					// 关闭弹窗
					subNVue.hide('none', 300)
				}
				if (this.msg == "can") {

					const subNVue = uni.getSubNVueById('modal')
					// 关闭弹窗
					subNVue.hide('none', 300)
				}

			},
			orderAppClicks() {
				// this.ordershow = true
				const subNVue = uni.getSubNVueById('bottoms');
				subNVue.show('slide-in-bottom', 300);
				// uni.hideTabBar();
			},
			// 
			orderClicks(e, g) {

				if (e == 1) {
					if (g) {
						this.xdloading = true
						this.mcdis = true;
					} else {
						this.gdloading = true
						this.gmcdis = true;
					}

				} else {


					if (g) {
						this.xdmloading = true
						this.mrdis = true
					} else {
						this.gdmloading = true
						this.gmrdis = true
					}


				}

				let {
					number,
					multiple0,
					currency_id,
					stop_loss_price,
					target_profit_price,
					gnumber,
					multiple1
				} = this



				let newnumber = number
				let multiple = multiple0


				let stop_loss_prices = stop_loss_price

				if (!this.hyzs) {

					stop_loss_prices = 0
				}
				let target_profit_prices = target_profit_price
				if (!this.hyzy) {

					target_profit_prices = 0
				}
				let target_prices = 0

				if (!g) {
					newnumber = gnumber
					multiple = multiple1
					target_prices = this.target_price
					stop_loss_prices = this.gstop_loss_price

					if (!this.ghyzs) {
						stop_loss_prices = 0
					}
					target_profit_prices = this.gtarget_profit_price
					if (!this.ghyzy) {
						target_profit_prices = 0
					}
				}


				this.$H.post('/api/v1/lever/submit', false, {
					share: newnumber,
					multiple: multiple,
					legal_id: 1,
					currency_id: currency_id,
					type: e,
					status: g,
					target_profit_price: target_profit_prices,
					stop_loss_price: stop_loss_prices,
					target_price: target_prices
				}).then(result => {
					if (e == 1) {
						if (g) {
							this.xdloading = false
							this.mcdis = false;
						} else {
							this.gdloading = false
							this.gmcdis = false;
						}
					} else {
						if (g) {
							this.xdmloading = false
							this.mrdis = false;
						} else {
							this.gdmloading = false
							this.gmrdis = false;
						}

					}
					if (result.type == "ok") {

						this.ordershow = true
						if (g == 0) {
							this.$store.commit('setOrNum', 1)
						}

						this.$H.get('/api/v1/user', false).then(result => {
							this.$store.commit('setUser', result.data)
							uni.navigateTo({
								url: "/pages/order/order"
							})
						})
					}

				})
			},

			sectionChange(index) {
				this.currentNav = index;
			},
			changeHandler(e) {
			},
			getQuotation() {


				const res = this.$H.get('/api/v1/quotation_new', false).then(res => {
					this.originalQuotation = res.data[this.currentIndex].quotation.map(item => {
						item.precision_length = this.$utils.getPrecisionLength(item.now_price)
						if (item.micro_trade_fee != '0.00') {
							this.micro_trade_fee = item.micro_trade_fee
						}
						return item

					})
					this.quotation = this.originalQuotation
					this.quotation = this.originalQuotation
					const has = this.originalQuotation.find(item => item.currency_id == this.currency_id)

					if (has) {
						this.symbolQuotation = has
						var newArray = []
						this.symbolQuotation.multiple.map(item => {
							newArray.push(item.value)


						})

						this.columns = []
						this.columns.push(newArray)

						if (this.symbolQuotation.lever_min_share == 0) {
							this.number = 0
						} else {
							this.number = this.symbolQuotation.lever_min_share
						}



						this.multiple0 = this.columns[0][0]
						this.multiple1 = this.columns[0][0]

						this.price = has.now_price


					}
					this.startSocket()
					this.calc()
					this.gcalc()
				})
			},
			startSocket() {
				const _this = this
				const {
					currency_id,
				} = this

				this.socket.on('daymarket', res => {
					let data = res

					if (this.currency_name == res.currency_name && this.legal_name == res.legal_name) {
						this.symbolQuotation = {
							...this.symbolQuotation,
							...res
						}
						this.currency_id = res.currency_id
					}
				});

			},
			confirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e

				this.multiple0 = value[0];
				this.shows = false
			},
			cancel(e) {
				this.shows = false
			},

			gconfirm(e) {

				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.guPicker
				} = e

				this.multiple1 = value[0];
				this.gshows = false
			},
			gcancel(e) {
				this.gshows = false
			},



			kcconfirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPickers
				} = e

				this.kcvalues = value[0].seconds;
				this.kcshows = false
			},
			kccancel(e) {
				this.kcshows = false
			},
			onChangeTab(e) {
				this.changesTabs(e.detail.current)
			},
			changesTabs(id) {
				if (this.tabIndex === id) {
					return;
				}
				this.tabIndex = id;
				this.scrollInto = 'tab' + id;
			},
			open() {

			},
			close() {
				this.ordershow = false
				// 	this.timer = setTimeout(() => {

				// 		uni.showTabBar()

				// 	}, 400)





			},
			calc() {

				const {
					symbolQuotation,
					buyDirection,
					multiple0,
					indicaList,
					number
				} = this
				const spread = Number(symbolQuotation.spread)
				const lever_share_num = Number(symbolQuotation.lever_share_num)
				const lever_trade_fee = Number(symbolQuotation.lever_trade_fee)
				const prices = Number(symbolQuotation.now_price)

				let spreadPrices = parseFloat((prices * spread) / 100);

				let pricesTotal = 0;
				if (buyDirection == 0) {
					// 如果方向是买
					pricesTotal = parseFloat(prices + spreadPrices);
				} else {
					pricesTotal = parseFloat(prices - spreadPrices);
				}

				let totalPrice = parseFloat(pricesTotal * lever_share_num * number);


				this.each_piece = symbolQuotation.each_piece
				this.margin = ((totalPrice / multiple0) * this.each_piece).toFixed(5);


				this.handlingFee = this.micro_trade_fee * Math.ceil(number);


			},
			gcalc() {

				const {
					symbolQuotation,
					buyDirection,
					multiple1,
					gnumber
				} = this
				const spread = Number(symbolQuotation.spread)
				const lever_share_num = Number(symbolQuotation.lever_share_num)
				const lever_trade_fee = Number(symbolQuotation.lever_trade_fee)
				const prices = Number(symbolQuotation.now_price)

				let spreadPrices = parseFloat((prices * spread) / 100);

				let pricesTotal = 0;
				if (buyDirection == 0) {
					// 如果方向是买
					pricesTotal = parseFloat(prices + spreadPrices);
				} else {
					pricesTotal = parseFloat(prices - spreadPrices);
				}

				let totalPrice = parseFloat(pricesTotal * lever_share_num * gnumber);


				this.each_piece = symbolQuotation.each_piece
				this.gmargin = ((totalPrice / multiple1) * this.each_piece).toFixed(5);


				this.ghandlingFee = this.micro_trade_fee * Math.ceil(gnumber)

			},
			loadmore() {
				
				this.quotation = this.resData
			},
			//对外接口
			ChangePeriod(period) //周期切换
			{
				// 知道是几分钟
				this.pids = period
				// this.timePeriod = period
				// 判断是否创建
				this.ClearChart();
				this.CreateKLineChart(1, period);
			},



			ChangeIndex(index, name, itemes) {
				if (!index) {
					const has = this.miansList.findIndex((item) => item == name)
					if (has > -1) {

						// g_KLine.JSChart.RemoveIndexWindow(has)
						g_KLine.JSChart.DeleteOverlayWindowsIndex(name)
						this.miansList.splice(has, 1)


					} else {

						this.miansList.push(name)
						var obj = {
							WindowIndex: index,
							IndexName: name,
							Identify: name,
							ShowRightText: false,
							OverlayIndexFrameWidth: 30,
							ShowToolbar: false,
							IsShowIndexTitle: false
						}
						g_KLine.JSChart.AddOverlayIndex(obj);
					}
					itemes.select = !itemes.select
				} else {
					const has = this.indicatorList.findIndex((item) => item == name)
					if (has > -1) {
						g_KLine.JSChart.RemoveIndexWindow(has + 1)
						this.indicatorList.splice(has, 1)
					} else {
						if (this.indicatorList.length >= 3) {
							return;
						}
						this.indicatorList.push(name)
						g_KLine.JSChart.AddIndexWindow(name);
					}
					itemes.select = !itemes.select
				}

				// g_KLine.JSChart.ChangeIndex(index, name);
				// var obj={WindowIndex:0, IndexName:name, Identify:name,ShowRightText:false,OverlayIndexFrameWidth:30,ShowToolbar:false,IsShowIndexTitle:false}
				// g_KLine.JSChart.AddOverlayIndex(obj);


			},


		

			GetBITDecimal(symbol) {
				// var lowerSymbol=symbol.toLowerCase();
				// if (lowerSymbol=="ethusdt.bit" || lowerSymbol=="btcusdt.bit") return 5;

				return 5;
			},

			ClearChart() {
				// 清除 DOM 元素
				const divKLine = document.getElementById('k-line-chart');
				if (divKLine) {
					try {
						while (divKLine.hasChildNodes()) {
							divKLine.removeChild(divKLine.lastChild);
						}
					} finally {}
				}
				this.chart = null;
			},

			CreateKLineChart_pc() {
				const chart = init('k-line-chart');
				chart.setStyles('green_rise_red_fall')
				chart.applyNewData(this.KDataList)
				chart.setStyles({
					candle: {
						tooltip: {
							showRule: 'none',
							showType: 'none'
						}
					}
				});
				chart.setStyles({
					indicator: {
						tooltip: {
							showRule: 'none',
							showType: 'none'
						}
					}
				});
				this.chart = chart;
			},
			CreateKLineChart(klineType, period) {
				this.Update.Cache = null;
				this.KLineInformation.Symbol = this.Symbol
				// this.updateMiddleWidth()
				this.RequestHistoryMinuteData(this.KLineInformation, period);
			},
			updateMiddleWidth() {
				const container = document.querySelector(".containerPC");
				const nowDiv = document.querySelector(".nowPC");
				const leftDiv = document.querySelector(".leftPC");
				const rightDiv = document.querySelector(".rightPC");
				const middleDiv = document.querySelector(".middlePC");

				const leftWidth = leftDiv ? leftDiv.offsetWidth : 0;
				const rightWidth = rightDiv ? rightDiv.offsetWidth : 0;
				const nowWidth = rightDiv ? nowDiv.offsetWidth : 0;
				const containerWidth = container.offsetWidth;
				middleDiv.style.width = `${containerWidth - leftWidth - rightWidth- nowWidth}px`;
			},
			CreateDepthChart() {
				this.Update.Cache = null;

				// #ifdef H5
				this.CreateDepthChart_h5();
				// #endif

				// #ifndef H5
				this.CreateDepthChart_app();
				// #endif
			},

			CreateDepthChart_h5() {
				if (g_KLine.JSChart) return;

				let chart = HQChart.JSChart.Init(this.$refs.kline);

				var option = DefaultData.GetDepthOption();
				option.Symbol = this.Symbol;
				option.NetworkFilter = this.NetworkFilter;
				chart.SetOption(option);
				g_KLine.JSChart = chart;
			},

			ChangeDepthChart() {
				if (g_KLine.JSChart && g_KLine.JSChart.JSChartContainer.ClassName == "DepthChartContainer") return;

				this.ClearChart();
				this.CreateDepthChart();
			},

			//WS
			//心跳包
			SendWSHeartMessage() {
				if (this.SocketOpen) {
					var pong = {
						'pong': new Date().getTime()
					};
					var message = JSON.stringify(pong);
					uni.sendSocketMessage({
						data: message
					});
				}
			},

			//取消订阅上一次的信息
			SendUnSubscribeMessage() {

				if (!this.LastSubString || !this.SocketOpen) return;

				var message = JSON.stringify({
					unsub: this.LastSubString
				}); //取消上次订阅的信息
				uni.sendSocketMessage({
					data: message
				});
				this.LastSubString = null; //清空最后一个订阅信息
			},

			RequestWSData(data, recvCallback) {
				uni.onSocketMessage((event) => {

					if (event.data == "40") {
						return
					}


					var wz = event.data.indexOf('[')
					if (wz > 2) {
						wz = event.data.indexOf('{')
					}

					event.data = event.data.substring(wz, event.length)

					var recvData = JSON.parse(event.data);

					if (recvData.ping) {

						this.SendWSHeartMessage(); //回复服务器心跳包
					} else if (recvData.unsubbed) //取消订阅成功
					{

					} else if (recvData.subbed) //订阅成功 
					{

					} else {
						recvCallback(recvData, data);
					}

				});

				uni.onSocketError((event) => {
					console.log(event);
				});

			},

			//生成请求数据
			GeneratePostData(symbol, period) {
				//1min, 5min, 15min, 30min, 60min,4hour,1day,1week, 1mon
				var MAP_PERIOD = new Map([
					[4, '1min'],
					[5, '5min'],
					[6, "15min"],
					[7, '30min'],
					[8, "60min"],
					[0, '1day'],
					[1, '1week'],
					[2, '1mon'],
					[3, '1year']
				]);

				var strPeriod = MAP_PERIOD.get(period);

				var reqData = {
					req: `market.${symbol}.kline.${strPeriod}`,
					symbol: symbol,
					period: strPeriod
				};

				var subData = {
					sub: `market.${symbol}.kline.${strPeriod}`,
					symbol: symbol,
					period: strPeriod
				};

				return {
					Req: reqData,
					Sub: subData
				};
			},

			//请求分钟历史数据
			RequestHistoryMinuteData(data, period) {

				data.PreventDefault = true;
				// 清空历史数据
				this.KDataList = [];
				var symbol = this.currency_name; //币名
				var postData = this.GeneratePostData(this.OriginalSymbol, period);
				var obj = {
					SendData: postData.Req,
					Symbol: symbol,
					OriginalSymbol: this.OriginalSymbol,
					Period: period,
				};
				this.RecvHistoryMinuteData({}, obj);
			},

			//接收历史分钟数据
			async RecvHistoryMinuteData(recvData, data) {
				var hqChartData = {
					code: 0,
					data: []
				};
				hqChartData.symbol = this.Symbol;
				hqChartData.name = data.OriginalSymbol;
				let froms = Date.parse(new Date()) / 1000 - this.intervals[data.SendData.period || '1min'] * 24 * 60 *
					60
				let tos = Date.parse(new Date()) / 1000
				const res = await this.$H.post('/api/v1/klineMarket', false, {
					// symbol:"BTC/USDT",
					symbol: this.symboles,
					period: data.SendData.period || '15min',
					from: froms,
					to: tos

				})
				let timestamp = new Date().getTime()
				let basePrice = 5000
				for (var i in res.data) {

					const open = res.data[i].open
					const high = res.data[i].high
					const low = res.data[i].low
					const close = res.data[i].close
					const volume = res.data[i].volume
					const turnover = (open + high + low + close) / 4 * volume
					this.KDataList.push({
						timestamp,
						open,
						high,
						low,
						close,
						volume,
						turnover
					})

					basePrice = close
					timestamp += 60 * 1000
				}
				var yClose = null; //前收盘
				this.SubscribeMinuteRealtimeData(data);
			},

			//订阅最新分钟K线数据
			SubscribeMinuteRealtimeData(data) {

				//订阅最新数据
				var postData = this.GeneratePostData(data.OriginalSymbol, data.Period);
				var obj = {
					SendData: postData.Sub,
					Symbol: data.Symbol,
					OriginalSymbol: data.OriginalSymbol,
					Period: data.Period
				};
				this.socket.on('kline', res => {
					this.RecvMinuteRealtimeData(res, obj);
				});
			},

			RecvMinuteRealtimeData(res, data) {
				if (!this.chart) {
					this.CreateKLineChart_pc();
					return
				}
				if (res.currency_name != this.currency_name) return
				if ((data.SendData.period) !== res.period) return;
					const dataList = this.chart.getDataList()
					const lastData = dataList[dataList.length - 1]
					const newData = {
						...lastData
					}
					newData.close = res.close
					newData.high = res.high
					newData.low = res.low
					newData.volume = res.volume
					this.chart.updateData(newData)

			},

		}

	}
</script>
<style>
	.bnsss /deep/.uni-picker-view-mask {
		background: inherit;
	}

	/deep/.u-image {
		margin: 0 auto;
	}

	.kline {
		background: #ffffff;
	}

	page {

		position: inherit;
		overflow: auto !important;
	}

	.showItems {
		width: 130px;
		text-align: center;
		padding: 20px;
		--tw-bg-opacity: 1 !important;
		border-bottom: 2px solid #e5e7eb;
		background-color: #e5e7eb;
	}

	.noShowItems {
		width: 130px;
		text-align: center;
		padding: 20px;
		--tw-bg-opacity: 1 !important;
		border-bottom: 2px solid #e5e7eb;
	}

	.itemview {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 15rpx 25rpx;

		justify-content: space-between;
		color: #222;
		border-radius: 8rpx;
		margin-top: 16rpx;

		margin-right: 20rpx;

		text {}

		image {
			width: 30rpx;
			height: 30rpx;
			margin-left: 60rpx;
		}
	}

	.itemview_ {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 15rpx 25rpx;

		justify-content: space-between;
		color: #222;
		border-radius: 8rpx;
		margin-top: 16rpx;
		border: 2rpx solid #333;
		margin-right: 20rpx;

		text {}

		image {
			width: 30rpx;
			height: 30rpx;
			margin-left: 60rpx;
		}
	}

	.u-content {
		padding: 65px 90px 0;
		@include flex(column);
		align-items: center;
		justify-content: center;

		&__circle {
			background-color: $u-success;
			@include flex;
			border-radius: 100px;
			width: 60px;
			height: 60px;
			align-items: center;
			justify-content: center;
		}

		&__normal {
			font-size: 15px;
			color: $u-success;
			margin-top: 15px;
		}
	}

	.composings {
		margin-top: 18px;
		font-weight: bold;
	}

	.u-line {
		border-bottom: 0px solid rgb(214, 215, 217);
	}

	uni-swiper-item {
		overflow-x: hidden;
		overflow-y: auto;
	}

	.wnss /deep/.u-number-box__plus {

		background: #F5F7FA !important;
	}

	.bnss /deep/.u-number-box__plus {

		background: #12151B !important;
	}

	.bnss /deep/.u-number-box__minus {


		background: #12151B !important;
	}

	.bnss /deep/.u-icon__icon {
		color: #fff !important;
	}

	.wnss /deep/.u-number-box__minus {


		background: #F5F7FA !important;
	}

	.wnss /deep/.u-number-box {

		border: 1px solid #D2D6E8;
	}

	.bnss /deep/.u-number-box {

		border: 1px solid #2F3142;
	}

	.bnss /deep/.u-number-box__input--disabled {
		color: #424656 !important;
	}

	/deep/.u-subsection__bar {
		border-bottom: 2px solid #0166fc;
	}

	.bnss /deep/.u-subsection--button {
		height: 65px;
		background: #1A1C24;
	}

	uni-page-body {
		overflow-x: hidden;
	}

	.jys {
		background: #ffffff;
	}

	.jys .scroll-view {
		width: 95% !important;
		height: 75rpx;
		line-height: 75rpx;
		white-space: nowrap;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 6px;
		margin-top: 15px;

	}


	.jys .scroll-viewws {
		width: 93% !important;
		/* height: 105rpx; */
		line-height: 105rpx;
		white-space: nowrap;
		background: #ededed;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		/* border-radius: 0px 100px 100px 0px; */
		border-radius: 100px;
		padding-left: 5px;
		padding-right: 5px;
		/* margin-top: 15px; */

	}

	.jys .scroll-viewws .scroll-items {
		display: inline-block;
		width: 50%;
		height: 75rpx;
		line-height: 75rpx;
		text-align: center;
		background: #0166fc;
		/* border-radius: 100px 0px 0px 100px; */
		border-radius: 100px;
		color: #ffffff;
	}

	.jys .scroll-viewws .scroll-items-r {
		display: inline-block;
		width: 50%;
		height: 75rpx;
		line-height: 75rpx;
		text-align: center;
		background: #0166fc;
		border-radius: 0px 100px 100px 0px;
		color: #ffffff;
	}

	.scroll-viewws .scroll-item {
		display: inline-block;
		width: 50%;
		line-height: 75rpx;
		height: 75rpx;
		text-align: center;
		border-radius: 100px;
		color: #626779;

	}

	.mini-btn {
		border-radius: 50px;
		--tw-text-opacity: 1 !important;
		color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(1 102 252 / var(--tw-bg-opacity)) !important;
	}

	.bmini-btn {
		border-radius: 50px;
		--tw-text-opacity: 1 !important;
		color: rgb(19 19 19 / var(--tw-text-opacity)) !important;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(238 238 238 / var(--tw-bg-opacity)) !important;
	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
	}

	.dhbts {
		display: inline-block;
		background: #fff;
		margin: 5px;
		width: 15%;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;
		background-color: #eeeeee;
		font-weight: 500 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
		/* border-radius: 8vw !important; */
		text-align: center;
	}

	.sdhbts {
		display: inline-block;
		background: #fff;
		margin: 5px;
		width: 15%;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;

		font-weight: 500 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
		/*  border-radius: 8vw !important; */
		text-align: center;
		background-color: rgb(1 102 252 / var(--tw-bg-opacity)) !important;
	}

	/deep/.u-popup__content {

		border-radius: 10px;
	}

	.bnsss /deep/.u-picker__view__column__item {
		color: #fff;
	}

	.bnsss /deep/.u-popup__content {

		border-radius: 10px;
		background: #222;
	}

	.wnsss /deep/.u-popup__content {

		border-radius: 10px;
	}

	.bzsshow {
		overflow: hidden;
		position: fixed;
	}

	/deep/.u-subsection__item__text {
		font-size: 15px !important;

	}








	/deep/.u-subsection--button__bar[data-v-244377f2] {

		background-color: inherit !important;

	}


	.jybs /deep/.u-subsection--button {
		background-color: inherit !important;
		height: 50px;
		margin-left: -5px;

	}

	/deep/.u-subsection__item__text {

		line-height: 30px !important;
		display: flex;
		flex-direction: row;
		align-items: center;
		transition-property: color;
		transition-duration: 0.3s;
		font-size: 15px !important;
		width: 90px;
		text-align: center;
		justify-content: center;
	}

	/deep/ .u-subsection--button__bar {
		background-color: inherit !important;
		border-bottom: 2px solid #0166fc;
		/* width: 85px !important; */
		height: 45px !important;
		border-radius: 0px !important;
	}

	.scroll-view {
		width: 95% !important;
		height: 100rpx;
		line-height: 100rpx;
		white-space: nowrap;
		overflow: hidden;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 6px;
		margin: 0 auto;
	}



	.scroll-view .scroll-items {
		display: inline-block;
		width: 49.5%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #FFFFFF;
		border-radius: 6px;
	}

	.scroll-view .scroll-itembs {
		display: inline-block;
		width: 49.5%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #1A1B24;
		border-radius: 6px;
		color: #ffffff !important;
	}

	.scroll-view .scroll-itemb {
		display: inline-block;
		width: 49.5%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;

	}

	.scroll-view .scroll-item {
		display: inline-block;
		width: 49.5%;
		line-height: 80rpx;
		height: 80rpx;
		text-align: center;
		border-radius: 6px;
		color: #828397;
	}



	.jys /deep/.u-subsection--button {
		height: 50px;
		background-color: #F7F8FA !important;

	}

	.jybs /deep/.u-subsection--button {
		height: 50px;


	}

	/deep/.u-subsection__item__text {
		font-size: 15px !important;
	}

	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
</style>