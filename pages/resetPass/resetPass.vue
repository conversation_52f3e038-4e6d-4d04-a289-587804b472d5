<template>
	<view>
		<view>
			<view
				style="width:93%;display: flex; padding-top: 10px; padding-left: 10px; justify-content: center;align-items: center;">
				<view style="flex: 1;" @click="backs()">
					<image src="../../static/<EMAIL>" style="width: 50rpx; height: 50rpx;"></image>
				</view>
				<view style="width: 124px;
height: 35px;
background: #F5F5F5;
border-radius: 388px 388px 388px 388px;
opacity: 1; width: 40px; padding: 2px 0px; ; display: flex; justify-content: center; align-items: center;"
					@click="showModal" data-target="viewModal">
					<view>
						<image :src="clangs.img" style="width:25px; height: 20px;"></image>
					</view>
					<!-- <view style="height: 20px; margin: 0 5px; line-height: 20px; font-size: 14px;">
							<text>{{clangs.name}}</text>
						</view> -->
					<!-- <view>
							<image src="../../static/<EMAIL>" style="width:15px; height: 13px;"></image>
						</view> -->
				</view>
			</view>
		</view>
		<view style="width: 225px; margin: 0 auto; padding: 10rpx 0 25rpx 0; text-align: center;">
			<view>
				<image src="../../static/logo.png" style="width:200px; height: 100px;"></image>
			</view>
			<!-- <text style="font-size: 18px; font-weight: bold;">{{i18n.welcome_register}}</text> -->
		</view>
		<uni-forms :modelValue="formData">

			<view style="height: 430px;
		
		opacity: 1; width: 90%; margin: 0 auto; ">
				<view class="wrap">
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">
						<text class="wrap_text" style="color: #0166fc;">{{i18n.enter_email_or_phone}}</text>
						<u--input :placeholder="i18n.enter_email_or_phone_placeholder" class="inps" v-model="usestring"
							type="text" @input="onInputChange"></u--input>
<!--						<view v-if="inputType" style="font-size: 12px; color: #666; margin-top: 5px;">-->
<!--							{{inputType === 'email' ? '检测到邮箱格式' : '检测到手机号格式'}}-->
<!--						</view>-->
						</view>

					</view>
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">
							<text class="wrap_text">{{i18n.enter_password}}</text>
							<uni-easyinput class="uni-mt-5" :type="types" :passwordIcon="false" :suffixIcon="suffixIcon"
								v-model="passwords" :placeholder="i18n.enter_new_password"
								@iconClick="suffix"></uni-easyinput>
						</view>

					</view>
					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

						<view style="width: 100%; margin-top: 10px;">

							<text class="wrap_text">{{i18n.enter_password_again}}</text>
							<uni-easyinput class="uni-mt-5" :type="confirmtypes" :passwordIcon="false"
								:suffixIcon="confirmsuffixIcon" v-model="confirmpasswords"
								:placeholder="i18n.enter_new_password_again" @iconClick="confirmsuffix"></uni-easyinput>

							<!-- <u-input class="inps"  :type="types"
			                            placeholder="请再次输入密码"
										@iconClick="suffix()"
			                            v-model="formData.password"
			                            :suffixIcon="suffixIcon"
			                            :password='passwordShow'
										
			                        ></u-input> -->
						</view>

					</view>

				</view>

				<view class="wrap">

					<view
						style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-bottom: 15px;">




					</view>

					<!-- #ifndef APP-NVUE -->
					<text class="wrap_text">{{i18n.enter_vercode}}</text>
					<u-input v-model="code" class="inps" style=" padding: 10px !important;"
						:placeholder="i18n.enter_vercode">
					<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						<u--input v-model="code" class="inps" style="padding: 10px !important;"
							:placeholder="i18n.enter_vercode">
						<!-- #endif -->
							<template slot="suffix">
								<u-code ref="uCode" @change="codeChange" seconds="60"
									:changeText="'X'+i18n.get_ver_code"></u-code>
								<u-button class="my_u_button" @tap="getCode" :text="tips" size="mini"></u-button>

							</template>
					<!-- #ifndef APP-NVUE -->
					</u-input>
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					</u--input>
					<!-- #endif -->
					<view style=" display: flex; justify-content: center; align-items: center;">
						<view style="  text-align: center; margin-right: 2px;">







						</view>


					</view>
					<view style="width:100%;margin-top: 25px;">
						<u-button :loading="loading" loadingText="......" @click="obutton()" loadingSize="30" style="background: #0166fc; margin-top: 6vh;
															border-radius: 6vh;	    border: 0;   font-size: 14px;  height: 45px; color: #FFFFFF;
						line-height: 55px !important;">{{i18n.reset_password}}</u-button>
						<!-- 	<view style="width:280px; margin: 0 auto; margin-top: 30px;" @click="backs()">
							<text>{{i18n.have_account}}?</text>
							<text style="color: #0166fc;font-size: 15px; font-weight: bold; padding-left: 10px;">
								{{i18n.sign_in}}</text>
						</view> -->

					</view>

				</view>
			</view>


		</uni-forms>
		</scroll-view>
		<view class="DrawerClose" :class="modalName=='viewModal'?'show':''" @tap="hideModal">
			<text class="cuIcon-pullright"></text>
		</view>

	</view>
</template>

<script>
	import {
		langs
	} from "@/common/language.js"
	export default {
		data() {
			return {
				formData: {
					"password": ""
				},
				types: "password",
				confirmtypes: "password",
				loading: false,
				tips: '',
				seconds: "",
				loading: false,
				values: null,
				value: null,
				passwordShow: true,
				confirmpasswords: null,
				passwords: null,
				confirmpasswordShow: true,
				xyname: null,
				checkboxValue1: null,
				usestring: '',
				getcode: null,
				clangs: {},
				language: [],
				modalName: null,
				inputType: '', // 'email' 或 'phone'
				code: ''

			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
			this.language = langs
			this.setLoads()
		},
		onShow() {
			this.setLgs()
		},
		methods: {
			// 【新增】检测输入类型
			onInputChange() {
				this.detectInputType();
			},
			
			// 【修复】检测输入是邮箱还是手机号
			detectInputType() {
				const input = this.usestring.trim();
				if (!input) {
					this.inputType = '';
					return;
				}

				// 【修复】简化邮箱格式检测 - 只要包含@符号就认为是邮箱
				const emailRegex = /^[^\s@]+@[^\s@]+$/;
				// 手机号格式检测（支持多种格式）
				const phoneRegex = /^1[3-9]\d{9}$|^\+?[1-9]\d{7,14}$/;

				if (emailRegex.test(input)) {
					this.inputType = 'email';
					console.log('检测到邮箱格式:', input);
				} else if (phoneRegex.test(input)) {
					this.inputType = 'phone';
					console.log('检测到手机号格式:', input);
				} else {
					this.inputType = '';
					console.log('未识别的格式:', input);
				}
			},
			
			// 【新增】验证输入格式
			validateInput() {
				if (!this.usestring) {
					uni.showToast({
						title: this.i18n.please_enter_email_or_phone,
						icon: 'none'
					});
					return false;
				}

				if (!this.inputType) {
					uni.showToast({
						title: this.i18n.please_enter_correct_format,
						icon: 'none'
					});
					return false;
				}

				return true;
			},
			
			setLoads() {
				let lgs = uni.getStorageSync('lang') || 'en'
				this.language.map(el => {
					el.selected = false
					if (el.value == lgs) el.selected = true
					return el
				})
			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})
				console.log(langs)
				this.language = langs
				this._i18n.locale = item.value
				console.log(item.value)
				uni.setStorageSync('lang', item.value)

				this.$utils.setTabbar(this)

				this.clangs = item
				this.hideModal()
				document.querySelector('.u-button__text').innerText = this.$t("reset.get_ver_code");
				// this.$store.commit('setLang', item.value)

				// setTimeout(() => {
				// 	this.showLanguage = false
				// }, 200)
			},
			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
			},
			hideModal(e) {
				this.modalName = null

			},
			obutton() {
				// 验证输入格式
				if (!this.validateInput()) {
					return;
				}

				let {
					usestring,
					code,
					passwords,
					confirmpasswords
				} = this;

				// 验证密码
				if (!passwords || passwords.length < 6) {
					uni.showToast({
						title: '密码长度不低于6位',
						icon: 'none'
					});
					return false;
				}

				if (passwords != confirmpasswords) {
					uni.showToast({
						title: '两次密码不一致，请重新填写密码',
						icon: 'none'
					});
					return false;
				}

				if (!this.getcode) {
					uni.showToast({
						title: '请先获取验证码',
						icon: 'none'
					});
					return false;
				}

				if (!code) {
					uni.showToast({
						title: '验证码不能为空',
						icon: 'none'
					});
					return false;
				}

				this.loading = true;

				// 根据输入类型构建请求参数
				let requestData = {
					code: code,
					password: passwords,
					repassword: confirmpasswords,
					type: this.inputType // 'email' 或 'phone'
				};

				// 根据类型设置不同的字段名
				if (this.inputType === 'email') {
					requestData.email = usestring;
				} else if (this.inputType === 'phone') {
					requestData.phone = usestring;
				}

				console.log('重置密码请求参数:', requestData);

				this.$H.post('/api/v1/resetPassword', false, requestData).then(res => {
					this.loading = false;
					if (res.type == "ok") {
						uni.showToast({
							title: '重置密码成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.reLaunch({
								url: "/pages/login/login"
							});
						}, 1500);
					} else {
						uni.showToast({
							title: res.message || '重置密码失败',
							icon: 'none'
						});
					}
				}).catch(error => {
					this.loading = false;
					console.error('重置密码错误:', error);
					uni.showToast({
						title: '重置密码失败，请重试',
						icon: 'none'
					});
				});
			},
			isShow() {
				this.types = this.types === "password" ? "text" : "password"
			},
			backs() {
				uni.navigateBack({
					delta: 1
				})
			},
			checkboxChange() {

			},

			codeChange(text) {
				if ("获取验证码" == text || "重新获取" == text) {
					text = this.$t("reset.get_ver_code")
				}
				this.tips = text;
			},
			getCode() {
				// 验证输入格式
				if (!this.validateInput()) {
					return;
				}

				let { usestring } = this;

				if (this.$refs.uCode.canGetCode) {
					uni.showLoading({
						title: '正在获取验证码...'
					});

					// 根据输入类型选择不同的API接口
					let apiPath = '';
					let requestData = {};

					if (this.inputType === 'email') {
						// 邮箱验证码
						apiPath = '/api/v1/sms_mail';
						requestData = { user_string: usestring };
					} else if (this.inputType === 'phone') {
						// 手机验证码
						apiPath = '/api/v1/sms_phone';
						requestData = { phone: usestring };
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '请输入正确的邮箱或手机号',
							icon: 'none'
						});
						return;
					}

					this.$H.post(apiPath, false, requestData).then(res => {
						uni.hideLoading();
						if (res.type == "ok") {
							uni.showToast({
								title: this.inputType === 'email' ? '邮箱验证码已发送' : '短信验证码已发送',
								icon: 'success'
							});
							console.log('验证码发送成功:', res);
							this.getcode = 1;
							this.$refs.uCode.start();
						} else {
							uni.showToast({
								title: res.message || '验证码发送失败',
								icon: 'none'
							});
						}
					}).catch(error => {
						uni.hideLoading();
						console.error('验证码发送错误:', error);
						uni.showToast({
							title: '验证码发送失败，请重试',
							icon: 'none'
						});
					});

				} else {
					uni.showToast({
						title: '请等待倒计时结束后再发送',
						icon: 'none'
					});
				}
				// if (this.$refs.uCode.canGetCode) {
				// 	// 模拟向后端请求验证码
				// 	uni.showLoading({
				// 		title: '正在获取验证码...'
				// 	})












				// 	setTimeout(() => {
				// 		uni.hideLoading();
				// 		// 这里此提示会被this.start()方法中的提示覆盖
				// 		uni.$u.toast('验证码已发送');
				// 		// 通知验证码组件内部开始倒计时
				// 		this.$refs.uCode.start();
				// 	}, 2000);
				// } else {
				// 	uni.$u.toast('倒计时结束后再发送');
				// }
			},

			suffix() {

				this.types = this.types === "password" ? "text" : "password"
				this.passwordShow = !this.passwordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			},
			confirmsuffix() {

				this.confirmtypes = this.confirmtypes === "password" ? "text" : "password"
				this.confirmpasswordShow = !this.confirmpasswordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			}

		},
		computed: {
			i18n() {
				return this.$t("reset")
			},
			suffixIcon() {
			
				if (this.passwordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			},
			confirmsuffixIcon() {

				if (this.confirmpasswordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			}
		}
	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}

	/deep/.u-input {
		background: #fff;
	}

	.u-border {
		border-color: #D2D6E8 !important;
	}

	.u-button--info[data-v-3bf2dba7] {
		border: none;
		color: #0166fc;
		border-color: #ebedf0;
		border-width: 0px;
		font-weight: bold;
	}

	/* 	.inps {
		padding: 10px !important;
	} */

	/deep/.u-icon__img {
		width: 20px !important;
		height: 20px !important;
	}

	/* /deep/.u-icon__icon[data-v-172979f2] {
	
	    font-size: 22px !important;
	} */
	/deep/.content-clear-icon {
		color: initial !important;
	}

	/deep/.uni-easyinput__content[data-v-abe12412] {
		background-color: #f8f8f8 !important;
		border-radius: 6vh;
		border: none;
		padding: 4px;
	}

	/deep/.uni-easyinput__placeholder-class {

		font-size: 15px;
		color: rgb(192, 196, 204);
		padding-left: 3px !important;
	}

	/deep/.uni-easyinput__content-input {

		padding-left: 3px !important;
	}

	page {
		position: inherit;
	}

	/* 	/deep/.u-checkbox__icon-wrap--square{
	    border-radius: 50%;
	} */
	.DrawerPage {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		background-color: #F7F8FA;
		transition: all 0.4s;
	}

	.DrawerPage.show {
		transform: scale(0.9, 0.9);
		left: 65vw;
		box-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);
		transform-origin: 0;
	}

	.DrawerWindow {
		position: absolute;
		width: 65vw;
		height: 100vh;
		left: 0;
		top: 0;
		transform: scale(0.9, 0.9) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
		padding: 100upx 0;
	}

	.DrawerWindow.show {
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	.DrawerClose {
		position: absolute;
		width: 40vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30upx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50upx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 35vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.cuIcon {
		width: 64upx;
		height: 64upx;
		line-height: 64upx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10upx;
		height: 10upx;
		background-color: currentColor;
		position: absolute;
		bottom: 10upx;
		border-radius: 10upx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}

	.DrawerWindow view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	.DrawerClose view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	view,
	image {
		box-sizing: border-box;
	}

	/*#ifdef MP*/
	view,
	image {
		box-sizing: content-box !important;
	}

	/*#endif*/
	.uni-scroll-view-content view,
	image {
		box-sizing: content-box !important;
	}

	.inps {
		background-color: #f8f8f8 !important;
		border: none;
		padding: 10px !important;
		/* height: 35px; */
		border-radius: 55px;
	}

	.my_u_button {
		background-color: #0166fc;
		min-height: 34px;
		max-height: 44px;
		border-radius: 45px;
		color: #FFFFFF !important;
	}

	.wrap_text {
		display: block;
		margin-bottom: 2vh;
		font-size: 25rpx;
		font-weight: 600;

	}
</style>