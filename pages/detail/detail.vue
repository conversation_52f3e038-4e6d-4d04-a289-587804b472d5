<template>
	<div class='divchart' style='background-color:#0e182e;'>
		
		<!--  #ifdef  H5 -->
		<div>
			<div class='kline' id="kline" ref='kline'></div>
		</div>
		<!--  #endif -->
		
		<!--  #ifndef  H5 -->
		<view>
			<canvas id="kline2" canvas-id='kline2' class='kline2' v-bind:style="{width: ChartWidth+'px', height: ChartHeight+'px'}" 
			  @touchstart="KLineTouchStart" @touchmove='KLineTouchMove' @touchend='KLineTouchEnd' ></canvas>
		</view>
		<!--  #endif -->
		
		<div class="button-sp-area">
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(-1)">分时</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(0)">日线</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(1)">周线</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(4)">1分钟</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(6)">15分钟</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeDepthChart()">深度图</button>
		</div>
		
		<div class="button-sp-area">
			<button class="mini-btn" type="default" size="mini" @click="ChangeSymbol('btcusdt')">btcusdt</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeSymbol('ethusdt')">ethusdt</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(0, 'EMPTY')">EMPTY</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangeIndex(1, 'MACD')">MACD</button>
		</div>
		<u-tabbar
			:value="value1"
			@change="change1"
			:fixed="false"
			:placeholder="false"
			:safeAreaInsetBottom="false"
		>
			<u-tabbar-item text="首页" icon="home" @click="click1" ></u-tabbar-item>
			<u-tabbar-item text="放映厅" icon="photo" @click="click1" ></u-tabbar-item>
			<u-tabbar-item text="直播" icon="play-right" @click="click1" ></u-tabbar-item>
			<u-tabbar-item text="我的" icon="account" @click="click1" ></u-tabbar-item>
		</u-tabbar>
	</div>
</template>

<script>
// #ifdef H5	
import HQChart from '@/uni_modules/jones-hqchart2/js_sdk/umychart.uniapp.h5.js'
// #endif

// #ifndef H5
import {JSCommon} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js'
import {JSCommonHQStyle} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.style.wechat.js'
import {JSConsole} from '@/uni_modules/jones-hqchart2/js_sdk/umychart.console.wechat.js'

//禁用日志
JSConsole.Complier.Log=()=>{ };
JSConsole.Chart.Log=()=>{ };
// #endif


import { mapState } from 'vuex'


var pako = require('pako');

function DefaultData() { }

DefaultData.GetKLineOption = function () 
{
    let data = 
    {
        Type: '历史K线图', 
		//Type: '历史K线图横屏',
        
        Windows: //窗口指标
        [
            {Index:"MA",Modify: false, Change: false, Close:false}, 
            {Index:"VOL",Modify: false, Change: false,Close:false},
			{Index:"MACD2",Modify: false, Change: false,Close:false}
        ], 
 
		IsAutoUpdate:false,           //是自动更新数据(不自动更新由外部更新)
        IsApiPeriod:true,             //使用Api计算周期
        IsCorssOnlyDrawKLine:true,
        CorssCursorTouchEnd:true,
 
        Border: //边框
        {
            Left:   1,
            Right:  1, //右边间距
            Top:    25,
            Bottom: 25,
        },
 
        KLine:
        {
            Right:0,                            //复权 0 不复权 1 前复权 2 后复权
            Period:0,                           //周期: 0 日线 1 周线 2 月线 3 年线 
            PageSize:30,
            IsShowTooltip:false,
			DrawType:0,
        },
		
		KLineTitle:
		{
			IsShowName:false,
			IsShowSettingInfo:false,
		},
		
		Frame:  //子框架设置
		[
			{   
				SplitCount:3,IsShowLeftText:false, SplitType:1,
				Custom: [ { Type:0, Position:'right' } ]
			},

			{SplitCount:2,IsShowLeftText:false, SplitType:1 },
			{SplitCount:2,IsShowLeftText:false}
		],
		
		ExtendChart:
		[
			{Name:'KLineTooltip' },	//开启手机端tooltip
		], 
        
    };
 
    return data;
}

DefaultData.GetMinuteOption = function () 
{
    let data = 
    {
        Type: '历史K线图', 
		//Type: '历史K线图横屏',
        
        Windows: //窗口指标
        [
            {Index:"EMPTY",Modify: false, Change: false, Close:false, TitleHeight:0 }, 
            {Index:"VOL",Modify: false, Change: false,Close:false},
        ], 
 
		IsAutoUpdate:false,           //是自动更新数据(不自动更新由外部更新)
        IsApiPeriod:true,             //使用Api计算周期
        IsCorssOnlyDrawKLine:true,
        CorssCursorTouchEnd:true,
 
        Border: //边框
        {
            Left:   1,
            Right:  1, //右边间距
            Top:    25,
            Bottom: 25,
        },
 
        KLine:
        {
            Right:0,                            //复权 0 不复权 1 前复权 2 后复权
            Period:4,                           //周期: 0 日线 1 周线 2 月线 3 年线 
            PageSize:60,
            IsShowTooltip:false,
			DrawType:4,
        },
		
		KLineTitle:
		{
			IsShowName:false,
			IsShowSettingInfo:false,
		},
		
		Frame:  //子框架设置
		[
			{   
				SplitCount:3,IsShowLeftText:false, SplitType:1,
				Custom: [ { Type:0, Position:'right' } ]
			},

			{SplitCount:2,IsShowLeftText:false, SplitType:1 },
			{SplitCount:2,IsShowLeftText:false}
		],
		
		ExtendChart:
		[
			{Name:'KLineTooltip' },	//开启手机端tooltip
		], 
        
    };
 
    return data;
}
 
DefaultData.GetDepthOption=function()
{
	var option=
	{
		Type:'深度图',   //创建图形类型
		EnableZoomUpDown:
		{
		//Wheel:false,
		//Keyboard:false,
		//Touch:false,
		},

		Symbol:'BTCBUSD.bit',
		IsAutoUpdate:false,       //是自动更新数据
		AutoUpdateFrequency:10000,   //数据更新频率
		//CorssCursorTouchEnd:true,
		EnableScrollUpDown:true,

		MaxVolRate:1.2,

		CorssCursorInfo: { HPenType:0, VPenType:1, IsShowTooltip:true },

		Listener:
		{
		//KeyDown:false,
		//Wheel:false
		},
		
		SplashTitle:"下载数据 ......",
		Language:"EN",

		Border: //边框
		{
		Left:1,         //左边间距
		Right:1,       //右边间距
		Bottom:25,      //底部间距
		Top:1           //顶部间距
		},

		//框架设置
		Frame: { SplitCount:2, IsShowLeftText:false , XLineType:3, XSplitCount:2 },

	};

	return option;
}
 
var KLINE_CHART_TYPE =
{
	KLINE_ID:1,   		//K线图
	MINUTE_KLINE_ID:2  //K线面积图
};

var g_KLine={ JSChart:null };

export default 
{
	data() 
	{
		let data=
		{
			Symbol:'btcusdt.BIT', 
			OriginalSymbol:'btcusdt',
			ChartWidth:300,
			ChartHeight:600,
			KLine:
			{
				Option:DefaultData.GetKLineOption(), 
			},
			
			// WSUrl:'wss://www.huobi.com/-/s/pro/ws',	//火币api地址, 需要根据火币网经常调整. 会经常变(https://www.huobi.br.com/en-us/exchange/btc_usdt/)
			WSUrl:'ws://*************:24419',
			SocketOpen:false,
			LastSubString:null,     //最后一个订阅的数据
			periods:{
				"4":"1min",
				"5":"5min",
				"6":"15min",
				"0":"1day",
				"1":"1week",
				"2":"1mon",
			},
			Update:
			{
				EnableLimit:false,		//更新是否限速
				LastUpdateTime:null,
				Frequency:5000,		//更新频率
				Cache:null,
			}
		};
		
		return data;
	},
	
	name:'KLineChart',
	
	onLoad() 
	{
		
	},
	computed: {
		...mapState({
			socket:state=>state.socket,
			token:state=>state.token
		})
	},
	onReady()
	{	
		console.log("[KLineChart::onReady]");
		this.$nextTick(()=>
		{
			// #ifndef H5
			this.OnSize();
			this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 4); 
			// #endif
		});
	},
	
	onShow()
	{
		uni.getSystemInfo({
		    success:  (res) =>
			{
				var width=res.windowWidth;
				var height=res.windowHeight;
		        this.ChartWidth=width;
				this.ChartHeight=height-130;
				this.$nextTick(()=>
				{
					this.InitalHQChart();
					this.OnSize();
					this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID, 4); 
				})
		    }
		});
	},
	
	onHide()
	{
		if(this.SocketOpen)
		{
			uni.closeSocket();
			this.SocketOpen=false;
		}
		
		this.ClearChart();
	},
	
	onUnload()
	{
		if(this.SocketOpen)
		{
			uni.closeSocket();
			this.SocketOpen=false;
		}
		
		this.ClearChart();
	},
	
	methods: 
	{
		//对外接口
		ChangePeriod(period)  //周期切换
		{
			if(period==-1)
			{
				if (g_KLine.JSChart.KLineType!=KLINE_CHART_TYPE.MINUTE_KLINE_ID)
				{
					this.ClearChart();
					this.CreateKLineChart(KLINE_CHART_TYPE.MINUTE_KLINE_ID,4);
				}
			}
			else
			{
				if (g_KLine.JSChart.KLineType==KLINE_CHART_TYPE.KLINE_ID)
				{
					g_KLine.JSChart.ChangePeriod(period);
				}
				else
				{
					this.ClearChart();
					this.CreateKLineChart(KLINE_CHART_TYPE.KLINE_ID,period);
				}
			}
			
		},
		
		ChangeSymbol(symbol)   //切换股票
		{
			if (this.OriginalSymbol==symbol) return;

			this.OriginalSymbol=symbol;
			this.Symbol=symbol+'.BIT';
			g_KLine.JSChart.ChangeSymbol(this.Symbol);
		},
		
		ChangeIndex(index, name)
		{
			g_KLine.JSChart.ChangeIndex(index, name);
		},
				
		OnSize()
		{
			// #ifdef H5
			this.OnSize_h5();
			// #endif
		},
		
		OnSize_h5()
		{
			var chartHeight = this.ChartHeight;
			var chartWidth = this.ChartWidth;
			 
			var kline=this.$refs.kline;
			kline.style.width=chartWidth+'px';
			kline.style.height=chartHeight+'px';
			if (g_KLine.JSChart) g_KLine.JSChart.OnSize();
		},
		
		InitalHQChart()
		{
			// #ifdef H5
			HQChart.MARKET_SUFFIX_NAME.GetBITDecimal=this.GetBITDecimal;
			var blackStyle=HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
			this.SetHQChartStyle(blackStyle);
			HQChart.JSChart.SetStyle(blackStyle);
			
			// #endif
			
			// #ifndef H5
			JSCommon.MARKET_SUFFIX_NAME.GetBITDecimal=this.GetBITDecimal;
			var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
			this.SetHQChartStyle(blackStyle);;
			JSCommon.JSChart.SetStyle(blackStyle);	
			
			// #endif
		},
		
		//设置图形颜色
		SetHQChartStyle(blackStyle)
		{
			blackStyle.BGColor='rgb(14,24,46)';                 //背景
			blackStyle.TooltipPaint.BGColor="rgba(14,24,46,0.8)";			//tooltip背景色
			blackStyle.FrameTitleBGColor='rgb(14,24,46)';       //指标标题背景
			blackStyle.FrameSplitTextColor='rgb(101,117,138)';  //刻度颜色
		
			//K线颜色
			blackStyle.UpBarColor='rgb(37,175,142)';
			blackStyle.UpTextColor= blackStyle.UpBarColor;
			blackStyle.DownBarColor='rgb(210,73,99)';
			blackStyle.DownTextColor=blackStyle.DownBarColor;
			//平盘
			blackStyle.UnchagneBarColor=blackStyle.UpBarColor;
			blackStyle.UnchagneTextColor=blackStyle.UpBarColor;
		
			//指标线段颜色
			blackStyle.Index.LineColor[0]='rgb(88,106,126)';    
			blackStyle.Index.LineColor[1]='rgb(222,217,167)';
			blackStyle.Index.LineColor[2]='rgb(113,161,164)';
		
			//最新价格刻度颜色
			blackStyle.FrameLatestPrice.UpBarColor='rgb(37,175,142)';
			blackStyle.FrameLatestPrice.DownBarColor='rgb(210,73,99)';
			blackStyle.FrameLatestPrice.UnchagneBarColor=blackStyle.FrameLatestPrice.UpBarColor;
			
			blackStyle.Frame.XBottomOffset=2;
			
			//blackStyle.Minute.PriceColor='rgb(255,255,255)';
		},
		
		GetBITDecimal(symbol)
		{
			var lowerSymbol=symbol.toLowerCase();
			if (lowerSymbol=="ethusdt.bit" || lowerSymbol=="btcusdt.bit") return 2;
			
			return 2;
		},
		
		ClearChart()
		{
			if (g_KLine.JSChart)
			{
				g_KLine.JSChart.ChartDestory();
				g_KLine.JSChart=null;
			}
		
			// #ifdef H5
			var divKLine=document.getElementById('kline');
			while (divKLine.hasChildNodes()) 
			{
				divKLine.removeChild(divKLine.lastChild);
			}　
			// #endif
		},
				
		CreateKLineChart_h5(klineType, period)  //创建K线图
		{
		    if (g_KLine.JSChart) return;
			
			//var blackStyle=HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
			//HQChart.JSChart.SetStyle(blackStyle);
			//this.$refs.kline.style.backgroundColor=blackStyle.BGColor;	//div背景色设置黑色
						
		    this.KLine.Option.Symbol=this.Symbol;
		    let chart=HQChart.JSChart.Init(this.$refs.kline);
			
			if (klineType==KLINE_CHART_TYPE.MINUTE_KLINE_ID)
			{
				
				
				 console.log("+++++++++++++++++++");
				this.KLine.Option=DefaultData.GetMinuteOption();
			}
			else
			{
				 console.log("-------------------------");
				klineType=KLINE_CHART_TYPE.KLINE_ID;
				this.KLine.Option=DefaultData.GetKLineOption();
			}
			
			
			this.KLine.Option.NetworkFilter=this.NetworkFilter;
			this.KLine.Option.Symbol=this.Symbol;
			this.KLine.Option.KLine.Period=period;
			
		    chart.SetOption(this.KLine.Option);
		    g_KLine.JSChart=chart;
			g_KLine.JSChart.KLineType=klineType;
		},
		
		CreateKLineChart_app(klineType,period)
		{
			if (g_KLine.JSChart) return;
			
			let element = new JSCommon.JSCanvasElement();
			// #ifdef APP-PLUS
			element.IsUniApp=true;	//canvas需要指定下 是uniapp的app
			// #endif
			element.ID = 'kline2';
			element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
			element.Width = this.ChartWidth;
			
			//var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
			//JSCommon.JSChart.SetStyle(blackStyle);
					
			g_KLine.JSChart = JSCommon.JSChart.Init(element);
			if (klineType==KLINE_CHART_TYPE.MINUTE_KLINE_ID) 
			{
				this.KLine.Option=DefaultData.GetMinuteOption();
			}
			else
			{
				klineType=KLINE_CHART_TYPE.KLINE_ID;
				this.KLine.Option=DefaultData.GetKLineOption();
			}
			this.KLine.Option.NetworkFilter=(data, callback)=>{ this.NetworkFilter(data, callback); };
			this.KLine.Option.Symbol=this.Symbol;
			this.KLine.Option.IsClickShowCorssCursor=true;
			this.KLine.Option.IsFullDraw=true; 	//每次手势移动全屏重绘
			this.KLine.Option.KLine.Period=period;
			g_KLine.JSChart.SetOption(this.KLine.Option);
			g_KLine.JSChart.KLineType=klineType;
		},
		
		CreateKLineChart(klineType, period)
		{
			this.Update.Cache=null;
			
			// #ifdef H5
			this.CreateKLineChart_h5(klineType,period);
			// #endif
			
			// #ifndef H5
			this.CreateKLineChart_app(klineType,period);
			// #endif
		},
		
		CreateDepthChart()
		{
			this.Update.Cache=null;
			
			// #ifdef H5
			this.CreateDepthChart_h5();
			// #endif
			
			// #ifndef H5
			this.CreateDepthChart_app();
			// #endif
		},
		
		CreateDepthChart_h5()
		{
			if (g_KLine.JSChart) return;
			
			let chart=HQChart.JSChart.Init(this.$refs.kline);
			
			var option=DefaultData.GetDepthOption();
			option.Symbol=this.Symbol;
			option.NetworkFilter=this.NetworkFilter;
			chart.SetOption(option);
			g_KLine.JSChart=chart;
		},
		
		CreateDepthChart_app()
		{
			if (g_KLine.JSChart) return;
			
			var element = new JSCommon.JSCanvasElement();
			// #ifdef APP-PLUS
			element.IsUniApp=true;	//canvas需要指定下 是uniapp的app
			// #endif
			element.ID = 'kline2';
			element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
			element.Width = this.ChartWidth;
			
			//var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
			//JSCommon.JSChart.SetStyle(blackStyle);
					
			var chart = JSCommon.JSChart.Init(element);
			var option=DefaultData.GetDepthOption();
			option.Symbol=this.Symbol;
			option.IsFullDraw=true; 	//每次手势移动全屏重绘
			option.NetworkFilter=this.NetworkFilter;
			chart.SetOption(option);
			g_KLine.JSChart=chart;
		},
		
		ChangeDepthChart()
		{
			if (g_KLine.JSChart && g_KLine.JSChart.JSChartContainer.ClassName=="DepthChartContainer") return;
			
			this.ClearChart();
			this.CreateDepthChart();
		},
		
		NetworkFilter(data, callback)
		{
			console.log('[KLineChart::NetworkFilter] data', data);
			switch(data.Name)
			{
				case 'KLineChartContainer::ReqeustHistoryMinuteData':   //分钟全量数据下载
					console.log("ggggggggggg");
					this.RequestHistoryMinuteData(data, callback);
					break;
				case 'KLineChartContainer::RequestFlowCapitalData':     //数字货币不会调用
					console.log("bbbbbbbbbbbb");
					this.RequestFlowCapitalData(data, callback);
					break;
				case 'KLineChartContainer::RequestHistoryData':         //日线全量数据下载
					console.log("ggasdfggggggggg");
					this.RequestHistoryData(data,callback);
					break;
					
				case "DepthChartContainer::RequestDepthData":
				console.log("gggggggggggsss");
				    this.RequestDepthData(data, callback);
				    break;
			}
		},
		
		//////////////////////////////////////////////////////
		//WS
		//心跳包
		SendWSHeartMessage()
		{console.log("2111111");
			if (this.SocketOpen)
			{
				var pong = {'pong': new Date().getTime()};
				var message=JSON.stringify(pong);
				uni.sendSocketMessage({data:message});
			}
		},

		//取消订阅上一次的信息
		SendUnSubscribeMessage()
		{
			 console.log("2ooooooo");
			if (!this.LastSubString || !this.SocketOpen)  return;
 
			var message=JSON.stringify({unsub:this.LastSubString}); //取消上次订阅的信息
			uni.sendSocketMessage({data:message});
			this.LastSubString=null;    //清空最后一个订阅信息
		},
		
		RequestWSData(data, recvCallback)
		{
			 console.log("2222222222222");
			if (!this.SocketOpen) 
			{
				// uni.connectSocket( {url:this.WSUrl} );//创建连接

				// uni.onSocketOpen((event)=>
				// {
				// 	this.SocketOpen=true;
				// 	console.log(event);
				// 	var message=JSON.stringify(data.SendData);
				// 	uni.sendSocketMessage({data:message});
				// 	if (data.SendData.sub) this.LastSubString=data.SendData.sub;
				// });
			}
			else
			{
				// this.SendUnSubscribeMessage();
				// var message=JSON.stringify(data.SendData);
				// uni.sendSocketMessage({data:message});
				// if (data.SendData.sub) this.LastSubString=data.SendData.sub;    //保存最后一个订阅信息
			}
   
			this.socket.on('kline', recvData => {
 console.log("333333333333");
  console.log("333333333333");
   console.log("33333recvData3333333");
			if(this.periods['4']!==recvData.period) return;
				// const recvData=res;
 console.log("4444444444444444444444444");
	console.log('llllllllllllllllllllllllllllll')
		console.log('llllllllllllllllllllllllllllll')
			console.log('llllllllllllllllllllllllllllll')
				console.log('llllllllllllllllllllllllllllll')
					console.log('llllllllllllllllllllllllllllll')
				console.log(recvData);
				if (recvData.ping)
				{
					console.log("44444444444");
					this.SendWSHeartMessage();  //回复服务器心跳包
				}
				else if (recvData.unsubbed) //取消订阅成功
				{
console.log("5555555555");
				}
				else if (recvData.subbed)   //订阅成功 
				{
console.log("666666666666666");
				}
				else
				{
					console.log("7777777777777777");
					recvCallback(recvData, data); 
				}
			});

			uni.onSocketError((event)=>
			{
				console.log(event);
			});
			
		},
		
		//生成请求数据
		GeneratePostData(symbol, period)
		{
			//1min, 5min, 15min, 30min, 60min,4hour,1day,1week, 1mon
			var MAP_PERIOD=new Map([
				[4,'1min'],
				[5,'5min'],
				[6,"15min"],
				[0,'1day'],
				[1,'1week'],
				[2,'1mon'],
			]);

			var strPeriod=MAP_PERIOD.get(period);

			var reqData=
			{
				req: `market.${symbol}.kline.${strPeriod}`,
				symbol: symbol,
				period: strPeriod
			};

			var subData=
			{
				sub: `market.${symbol}.kline.${strPeriod}`,
				symbol: symbol,
				period: strPeriod
			};

			return { Req:reqData ,Sub:subData };
		},

		//请求分钟历史数据
		RequestHistoryMinuteData(data, callback)  
		{
			data.PreventDefault=true;
			this.Update.Cache=null;
			var symbol=data.Request.Data.symbol;
			var period=data.Self.Period;    //周期

			var postData=this.GeneratePostData(this.OriginalSymbol,period);

			var obj={ SendData:postData.Req , Symbol:symbol, OriginalSymbol:this.OriginalSymbol, Period:period, Callback:callback };
            this.RecvHistoryMinuteData({},obj);
			// this.RequestWSData(obj, (recvData,data)=>{ this.RecvHistoryMinuteData(recvData,data); });
		},
		
		//接收历史分钟数据
		async RecvHistoryMinuteData(recvData, data)   
		{
			// if (recvData.rep!=data.SendData.req) return;
			const res = await this.$H.post('/api/v1/klineMarket',true,{
							   symbol:"BTC/USDT",
							   period:"1min",
							   from:1692842991,
							   to:1692865206
							   
			})

			var hqChartData={code:0, data:[]};
			hqChartData.symbol=data.Symbol;
			hqChartData.name=data.OriginalSymbol;
            var dataes=[
				{"id":1692648120,"open":26127.76,"close":26149.12,"low":26127.76,"high":26149.12,"amount":3.1703699796539158,"vol":82872.18459971,"count":624}
				,{"id":1692648180,"open":26149.11,"close":26124.33,"low":26124.33,"high":26149.12,"amount":3.561071,"vol":93068.68270964,"count":270}
				,{"id":1692648240,"open":26124.25,"close":26141.75,"low":26116.75,"high":26141.75,"amount":2.222965,"vol":58071.53542927,"count":358}
				,{"id":1692648300,"open":26142.0,"close":26143.0,"low":26142.0,"high":26146.0,"amount":0.4815846820163696,"vol":12590.67186504,"count":69}
				,{"id":1692648360,"open":26137.75,"close":26134.0,"low":26134.0,"high":26137.75,"amount":0.292679,"vol":7649.24123688,"count":72}
				,{"id":1692648420,"open":26133.75,"close":26114.76,"low":26114.76,"high":26133.75,"amount":1.9860650705113534,"vol":51877.48392245,"count":219}
				,{"id":1692648480,"open":26114.77,"close":26117.95,"low":26114.77,"high":26117.95,"amount":0.396002,"vol":10341.7342499,"count":17}
				,{"id":1692648540,"open":26117.95,"close":26113.01,"low":26111.85,"high":26117.96,"amount":0.331649,"vol":8660.83733487,"count":50}
				,{"id":1692648600,"open":26111.75,"close":26109.92,"low":26105.25,"high":26111.75,"amount":0.369459,"vol":9645.1703822,"count":71}
				,{"id":1692648660,"open":26109.92,"close":26122.0,"low":26109.92,"high":26122.0,"amount":0.4341653068195742,"vol":11338.87571857,"count":132}
				,{"id":1692648720,"open":26122.0,"close":26122.25,"low":26121.99,"high":26122.49,"amount":0.104607,"vol":2732.57728218,"count":10}
				,{"id":1692648780,"open":26122.26,"close":26105.26,"low":26105.26,"high":26122.26,"amount":1.5722051730187205,"vol":41057.98614584,"count":154}
				,{"id":1692648840,"open":26105.26,"close":26108.45,"low":26105.26,"high":26108.45,"amount":0.826708,"vol":21582.35365874,"count":30}
				,{"id":1692648900,"open":26109.5,"close":26112.11,"low":26109.5,"high":26112.11,"amount":0.136489,"vol":3563.9158214,"count":7}
				,{"id":1692648960,"open":26112.11,"close":26116.5,"low":26112.11,"high":26116.5,"amount":0.563644,"vol":14720.03422313,"count":74}
				,{"id":1692649020,"open":26116.75,"close":26117.25,"low":26116.75,"high":26117.25,"amount":0.289331,"vol":7556.5046915,"count":19}
				,{"id":1692649080,"open":26117.24,"close":26110.5,"low":26110.5,"high":26117.24,"amount":0.455723,"vol":11900.71476728,"count":103}
				,{"id":1692649140,"open":26110.75,"close":26109.25,"low":26109.25,"high":26114.25,"amount":1.119225,"vol":29225.63553902,"count":70}
				,{"id":1692649200,"open":26109.25,"close":26111.47,"low":26109.25,"high":26112.99,"amount":0.184487,"vol":4817.44690879,"count":8}
				,{"id":1692649260,"open":26114.5,"close":26100.75,"low":26100.75,"high":26114.5,"amount":0.6061749495725621,"vol":15823.724119729999,"count":113}
				,{"id":1692649320,"open":26101.02,"close":26109.75,"low":26101.02,"high":26111.0,"amount":0.932342,"vol":24341.3222537,"count":94}
				,{"id":1692649380,"open":26111.24,"close":26105.0,"low":26105.0,"high":26111.25,"amount":0.824523,"vol":21527.57158325,"count":79}
				,{"id":1692649440,"open":26104.75,"close":26094.5,"low":26094.5,"high":26104.75,"amount":0.796899,"vol":20797.69786722,"count":164}
				,{"id":1692649500,"open":26094.51,"close":26101.51,"low":26094.51,"high":26101.51,"amount":0.97853,"vol":25538.09806859,"count":28}
				,{"id":1692649560,"open":26101.75,"close":26110.98,"low":26101.75,"high":26111.0,"amount":0.465958,"vol":12164.75146861,"count":158}
				,{"id":1692649620,"open":26110.98,"close":26106.75,"low":26106.75,"high":26110.98,"amount":0.256987,"vol":6709.9784934,"count":33}
				,{"id":1692649680,"open":26107.0,"close":26111.0,"low":26106.99,"high":26111.0,"amount":0.231988,"vol":6057.13393236,"count":7}
				,{"id":1692649740,"open":26111.49,"close":26106.25,"low":26106.25,"high":26111.5,"amount":0.471333,"vol":12305.95291302,"count":34}
				,{"id":1692649800,"open":26106.25,"close":26108.56,"low":26106.25,"high":26108.56,"amount":0.278562,"vol":7272.59858612,"count":14}
				,{"id":1692649860,"open":26109.37,"close":26115.75,"low":26109.37,"high":26115.75,"amount":0.465002,"vol":12141.80723991,"count":90}
				,{"id":1692649920,"open":26116.0,"close":26124.25,"low":26116.0,"high":26124.25,"amount":1.379353,"vol":36029.75383241,"count":244}
				,{"id":1692649980,"open":26124.25,"close":26123.5,"low":26123.5,"high":26135.24,"amount":3.497415,"vol":91385.58361632,"count":240}
				,{"id":1692650040,"open":26123.25,"close":26108.01,"low":26107.15,"high":26123.25,"amount":0.541615,"vol":14142.79900047,"count":201}
				,{"id":1692650100,"open":26108.01,"close":26105.0,"low":26105.0,"high":26108.01,"amount":0.1098,"vol":2866.49341026,"count":19}
				,{"id":1692650160,"open":26104.75,"close":26098.65,"low":26095.81,"high":26104.75,"amount":0.8019406196320499,"vol":20933.24429256,"count":86}
				,{"id":1692650220,"open":26098.64,"close":26094.51,"low":26092.5,"high":26098.65,"amount":1.503734,"vol":39241.4058343,"count":75}
				,{"id":1692669840,"open":26095.25,"close":26100.0,"low":26095.25,"high":26100.0,"amount":0.312892,"vol":8165.44110232,"count":67}
			];
			// if (recvData.data)
			// {
				var yClose=null; //前收盘
				console.log("count::::::::::::::::::::::::::::");
				
				// console.log(recvData);
				// console.log(recvData.data);
				for(var i in dataes)
				{
					var item=dataes[i];
console.log(i);
					//时间戳转换
					var dateTime = new Date();
					dateTime.setTime(item.id*1000);
					var date=dateTime.getFullYear()*10000+(dateTime.getMonth()+1)*100+dateTime.getDate();
					var time=dateTime.getHours()*100+dateTime.getMinutes();

					var newItem=[ date,yClose, item.open, item.high, item.low, item.close, item.amount, null, time ];

					yClose=item.close;
					hqChartData.data.push(newItem);
				}
			// }
			
			// #ifdef H5
			data.Callback(hqChartData);
			// #endif
			
			// #ifndef H5
			data.Callback({data: hqChartData});
			// #endif
			
			
			this.SubscribeMinuteRealtimeData(data);
		},
		
		//订阅最新分钟K线数据
		SubscribeMinuteRealtimeData(data)   
		{
			console.log("888888888aaaaaaa");
			//订阅最新数据
			var postData=this.GeneratePostData(data.OriginalSymbol,data.Period);
			var obj={SendData:postData.Sub, Symbol:data.Symbol, OriginalSymbol:data.OriginalSymbol, Period:data.Period };
console.log("-1-1-1-1-1-1-1-1-1-1-");
//           this.RecvMinuteRealtimeData({"id": 1692288088,
// "period": "1min",
// "base-currency":"BTC",
// "quote-currency":"USDT",
// "open": 27933.81,
// "close": 26483.61,
// "high": 28086.61,
// "1ow": 24707.94,
// "amount" : "6363 933726774135",
// "time": 1692288088},obj);
			this.RequestWSData(obj, (recvData,data)=>{ this.RecvMinuteRealtimeData(recvData,data); });
		},
		
		RecvMinuteRealtimeData(recvData,data)
		{
			console.log("888888888b");
			console.log(recvData);
			// if (recvData.ch!=data.SendData.sub) return;
			if (!g_KLine.JSChart) return;
	console.log("888888888yyyyyyyyyyyyyy");
			var internalChart=g_KLine.JSChart.JSChartContainer;
			var period=internalChart.Period;
			var symbol=internalChart.Symbol;
			console.log(period);
			console.log(symbol);
			if (symbol!=data.Symbol || period!=data.Period) return;

			var hqChartData={code:0, data:[], ver:2.0}; //更新数据使用2.0版本格式
			hqChartData.symbol=data.Symbol;
			hqChartData.name=data.OriginalSymbol;

			//TODO:把recvData => hqchart内部格式 格式看教程
			//HQChart使用教程30-K线图如何对接第3方数据15-轮询增量更新1分钟K线数据

			var item=recvData;

			var dateTime = new Date();
			console.log('datadatadatadatadatadatadatadatadatadatadatadatadatadatadatadata');
			console.log(dateTime);
			dateTime.setTime(item.id*1000);
			var date=dateTime.getFullYear()*10000+(dateTime.getMonth()+1)*100+dateTime.getDate();
			var time=dateTime.getHours()*100+dateTime.getMinutes();
			var newItem=[ date,null, item.open, item.high, item.low, item.close, item.amount, null, time ];
			
			if (this.Update.EnableLimit)	//是否启动缓存定时更新，降低刷新频率
			{
				if (!this.Update.Cache)
				{
					this.Update.Cache={Data:[newItem]};
				}
				else
				{
					var cache=this.Update.Cache;
					var item=cache.Data[cache.Data.length-1];
					if (item[0]==newItem[0] && item[8]==newItem[8])
						cache.Data[cache.Data.length-1]=newItem;
					else 
						cache.Data.push(newItem)
				}
				
				var bUpdate=true;
				if (this.Update.LastUpdateTime)
				{
					var now=Date.now();
					if (now-this.Update.LastUpdateTime<this.Update.Frequency)	//15s更新一次界面
						bUpdate=false;
				}
				
				if (!bUpdate) return;	//不更新
				
				hqChartData.data=this.Update.Cache.Data;
				this.Update.Cache=null;
				this.Update.LastUpdateTime=Date.now();
			}
			else
			{
				hqChartData.data.push(newItem);
			}
			
			// #ifdef H5
			console.log("888888888t");
			console.log(hqChartData);
			console.log(hqChartData.data);
			internalChart.RecvMinuteRealtimeData(hqChartData);
			// #endif
			
			// #ifndef H5
			console.log("888888888m");
			internalChart.RecvMinuteRealtimeData({data: hqChartData});
			// #endif
			
		},
		
		
		//日K数据下载
        RequestHistoryData(data,callback) 
        {
            data.PreventDefault=true;
			this.Update.Cache=null;
            var symbol=data.Request.Data.symbol;
            var period=data.Self.Period;    //周期
            var postData=this.GeneratePostData(this.OriginalSymbol,period);

            var obj={ SendData:postData.Req , Symbol:symbol, OriginalSymbol:this.OriginalSymbol, Period:period, Callback:callback };

            this.RequestWSData(obj, (recvData,data)=>{ this.RecvHistoryData(recvData,data); });
        },

        RecvHistoryData(recvData,data)  //接收到日线数据 转化成hqchart格式数据
        {
            if (recvData.rep!=data.SendData.req) return;

            var hqChartData={code:0, data:[]};
            hqChartData.symbol=data.Symbol;
            hqChartData.name=data.OriginalSymbol;

            if (recvData.data)
            {
				//console.log("[RecvHistoryData] recvData.data",recvData.data);
                var yClose=null; //前收盘
                for(var i in recvData.data)
                {
                    var item=recvData.data[i];

                    //时间戳转换
                    var dateTime = new Date();
                    dateTime.setTime(item.id*1000);
                    var date=dateTime.getFullYear()*10000+(dateTime.getMonth()+1)*100+dateTime.getDate();
                    var time=dateTime.getHours()*100+dateTime.getMinutes();

                    var newItem=[ date,yClose, item.open, item.high, item.low, item.close, item.amount, null];

                    yClose=item.close;
                    hqChartData.data.push(newItem);
                }
            }

			// #ifdef H5
            data.Callback(hqChartData);
			// #endif
			
			// #ifndef H5
			data.Callback({data: hqChartData});
			// #endif

            this.SubscribRealtimeData(data);
        },

        //订阅最新日K线数据
        SubscribRealtimeData(data)   
        {
            //订阅最新数据
            var postData=this.GeneratePostData(data.OriginalSymbol,data.Period);

            var obj={SendData:postData.Sub, Symbol:data.Symbol, OriginalSymbol:data.OriginalSymbol, Period:data.Period };

            this.RequestWSData(obj, (recvData,data)=>{ this.RecvRealtimeData(recvData,data); });
        },

        RecvRealtimeData(recvData,data)
        {
			console.log("888888888c");
            // if (recvData.ch!=data.SendData.sub) return;
			if (!g_KLine.JSChart) return;

            var internalChart=g_KLine.JSChart.JSChartContainer;
            var period=internalChart.Period;
            var symbol=internalChart.Symbol;
            if (symbol!=data.Symbol || period!=data.Period) return;

            var hqChartData={code:0, stock:[]};
            //TODO:把recvData => hqchart内部格式 格式看教程
            //HQChart使用教程30-K线图如何对接第3方数据14-轮询增量更新日K数据
			
			if (this.Update.EnableLimit)	//是否启动缓存定时更新，降低刷新频率
			{
				var now=Date.now();
				if (this.Update.LastUpdateTime)
				{
					if (now-this.Update.LastUpdateTime<this.Update.Frequency) return;
				}
				
				this.Update.LastUpdateTime=now;
			}

            var stock={symbol:data.Symbol, name:data.OriginalSymbol};
            var item=recvData.tick;
            var dateTime = new Date();
            dateTime.setTime(item.id*1000);
            var date=dateTime.getFullYear()*10000+(dateTime.getMonth()+1)*100+dateTime.getDate();
            var time=dateTime.getHours()*100+dateTime.getMinutes();

            stock.date=date;
            stock.yclose=null;
            stock.open=item.open;
            stock.high=item.high;
            stock.low=item.low;
            stock.price=item.close;
            stock.vol=item.amount;
            stock.amount=null;

            hqChartData.stock.push(stock);
			
			// #ifdef H5
			internalChart.RecvRealtimeData(hqChartData);
			// #endif
			
			// #ifndef H5
			internalChart.RecvRealtimeData({data: hqChartData});
			// #endif
			
        },
		
		
		GeneratePostDepthData(symbol, step)
		{
			var reqData=
			{
				
			};
			
			var subData=
			{
				sub: `market.${symbol}.depth.${step}`,
				symbol: symbol
			};
			
			return { Req:reqData ,Sub:subData };
		},
		
		
		RequestDepthData(data, callback)
		{
			console.log("888888888e");
			data.PreventDefault=true;
			this.Update.Cache=null;
			var symbol=data.Request.Data.symbol;
			var period=data.Self.Period;    //周期
			var postData=this.GeneratePostDepthData(this.OriginalSymbol,'step1');
			
			var obj={ SendData:postData.Sub , Symbol:symbol, OriginalSymbol:this.OriginalSymbol, Callback:callback };
			
			this.RequestWSData(obj, (recvData,data)=>{ this.RecvDepthData(recvData,data); });
		},
		
		
		RecvDepthData(recvData, data)
		{
			console.log("888888888f");
			// if (recvData.ch!=data.SendData.sub) return;
			
			if (!g_KLine.JSChart) return;
			
			var internalChart=g_KLine.JSChart.JSChartContainer;
			var symbol=internalChart.Symbol;
			if (symbol!=data.Symbol || internalChart.ClassName!='DepthChartContainer') return;
			
			var tick=recvData.tick;
			var asks=[], bids=[];
			
			if (tick.asks)
			{
				for(var i=0;i<tick.asks.length;++i)
				{
					var item=tick.asks[i];
					asks.push([item[0], item[1]]);
				}
			}
			
			if (tick.bids)
			{
				for(var i=0;i<tick.bids.length;++i)
				{
					var item=tick.bids[i];
					bids.push([item[0], item[1]]);
				}
			}
			
			var hqChartData=
			{ 
				code:0, 
				asks:asks,     //卖盘
				bids:bids,     //买盘 
				datatype:"snapshot"     //全量数据  
			}; 
			
			internalChart.ChartSplashPaint.EnableSplash(false);
			internalChart.RecvDepthData(hqChartData);
		},


		///////////////////////////////////////////////
		//手势事件 app/小程序才有
		//KLine事件
		KLineTouchStart: function (event) 
		{
		  if (g_KLine.JSChart) g_KLine.JSChart.OnTouchStart(event);
		},
		
		KLineTouchMove: function (event) 
		{
		  if (g_KLine.JSChart) g_KLine.JSChart.OnTouchMove(event);
		},
		
		KLineTouchEnd: function (event) 
		{
		  if (g_KLine.JSChart) g_KLine.JSChart.OnTouchEnd(event);
		},
	}
}
</script>

<style>
	
</style>
