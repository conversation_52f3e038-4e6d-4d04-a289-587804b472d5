<template>
	<view :style="$store.state.bgColor=='black'?'background:#1A1C24':'background:#F7F8FA'">

	<view v-if="$store.state.bgColor=='black'" style="height: 5px; background: #1A1C24;"></view>
	<view :style="$store.state.bgColor=='black'?'width: 90%;  background: #22252F;  margin: 0 auto; margin-top: 10px;':'width: 90%; background: #FFFFFF;  margin: 0 auto; margin-top: 10px;'"  v-for="(orderItem, index2) in indexList"
			:key="index2">
		<view style="display:flex; width: 100%; padding-top: 15px; ">
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center;  margin-left: 5px; ">
				<text style="color:#6D6F7C;font-size: 14px; text-align: left;">{{i18n.number_of_withdrawal}}</text>
				<text class="fonts"  style="font-size: 12px; margin-top: 5px;">{{orderItem.number}}</text>
			</view>
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
				<text style="color:#6D6F7C;font-size: 14px;">{{i18n.number_of_arrival}}</text>
				<text class="fonts" style="font-size: 12px; margin-top: 5px;">{{orderItem.real_number}}</text>
			</view>
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-right: 15px;">
				<text style="color:#6D6F7C;font-size: 14px;">{{i18n.currency}}</text>
				<text class="fonts" style="font-size: 12px;margin-top: 5px;">{{orderItem.currency}}</text>
			</view>
		</view>
		<view :style="$store.state.bgColor=='black'?'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;color: #fff !important;':'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;'">
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-left: 5px; ">
				<text>{{i18n.hand_fee}}</text>
			</view>
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
				<text>{{i18n.status}}</text>
			</view>
			<view style="display: flex; flex-direction:column;   align-items: center;  margin-right: 15px;">
				<text>{{$t("records.time")}}</text>
			</view>
		</view>
		<view style="display:flex; width: 100%; margin-top: -5px; font-size: 12px;">
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: flex-start;width: 98px; text-align: left; margin-left: 15px; ">
				<text class="fonts">{{orderItem.rate}}</text>
			</view>
			<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; text-align: right; flex: 1;">
				<text class="fonts" style="font-size: 14px;">{{orderItem.status_name}}</text>
			</view>
			<view style="display: flex; flex-direction:column;  padding-top: 15px; align-items: center; width: 85px; margin-right: 15px; text-align: right;">
				<text class="fonts">{{orderItem.create_time}}</text>
			</view>
		</view>
		<view style="display:flex;flex-direction: column; width: 100%; padding-top: 18px; padding-bottom: 15px;">
			<view style="display: flex; flex-direction:column;  width: 100%;  margin-left: 15px; ">
				<text style="font-size: 14px;color: #6D6F7C;">{{i18n.withdrawal_adress}}</text>
				<text class="fonts" :style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.address}}</text>
			</view>
			<view style="display: flex; flex-direction:column;  width: 100%;margin-top: 20px;  margin-left: 15px; ">
				<text style="font-size: 14px;color: #6D6F7C;">{{i18n.remarks}}</text>
				<text class="fonts" :style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.remark}}</text>
			</view>
		</view>
	</view>
		<view v-if="this.indexList.length>0" style="height: 15px;"></view>

		<view v-if="this.indexList.length>0" style=" width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #AFAFAF;">
			<text >{{loding}}</text>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				indexList: [],
				pages:1,
							status:false,
							loding:"",
							scrollH:0
			}
		},
		onLoad() {
		  this.getData()
		  const _this = this
		  uni.setNavigationBarTitle({
		  	title:_this.$t("out.withdrawal_record")
		  })
		 if(this.$store.state.bgColor=='black')
		 {
		 			  uni.setNavigationBarColor({
		 			    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
		 			    backgroundColor: '#22252F'
		 			  });
		 }
		},
		computed:{
			i18n() {
				return this.$t("out")
			}
		},
		onReachBottom() {
			this.scrolltolower()
		},
		methods: {

			scrolltolower() {

							if(this.status)return false
							   this.pages+=1
							    this.loding="..........."
								 this.getData()
						},
			getData() {

					this.status=true

							var arr = []
							const res =  this.$H.post('/api/v1/withdrawList',false,{
					page:this.pages
				}).then(res => {
															    // this.loading=false
												    if(res.type=="ok"){




														var arr = []


														if(res.data.data.length<=0)
														{
															this.status=false
															this.loding=""
															this.pages-=1
															return
														}
														res.data.data.map(item=>{


															item.number = parseFloat(item.number).toFixed(2)
															item.real_number = parseFloat(item.real_number).toFixed(2)
															item.rate = parseFloat(item.rate).toFixed(2)
															// let date = new Date(Date.parse(new Date(item.created_at)))
															// item.created_at = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm')


															arr.push(item)



														})
															if(this.indexList.length>0){
																this.indexList=[...this.indexList,...arr]

															}else{
																this.indexList = arr
															}

																	this.status=false


																}else{

																}
												})


						}
		}
	}
</script>

<style>
page{
		/* background-color: #F7F8FA !important; */
		position: inherit;
		overflow: initial;
		/* background:#F7F8FA; */
	}
	.fonts{
		font-size: 13px !important;
		font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
		font-weight: 400;
		color: #222222;

	}
</style>
