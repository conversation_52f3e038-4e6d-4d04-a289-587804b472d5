<template>
	<view style="" v-if="isloading">
		<view v-if="this.$store.state.bgColor=='black'" style="width:100%; height: 12px;background: #1A1C24;"></view>
		<view v-if="this.$store.state.bgColor=='while'" style="width:100%; height: 12px;background: #F7F8FA;"></view>
		<view v-if="userstatus && userstatus.real_data.review_status == 3"
			style="width: 50%; display: flex; flex-direction: column; justify-content: center; align-items:center; margin: 0 auto; margin-top: 50px;">
			<image src="../../../static/error.png" style="width: 100px; height: 100px;"></image>
			<text style="font-size: 16px; font-weight: bold; color: #fff;"
				v-if="$store.state.bgColor=='black'">{{out.rejected}}</text>
			<text style="font-size: 16px; font-weight: bold; "
				v-if="$store.state.bgColor=='while'">{{out.rejected}}</text>
		</view>
		<view v-else-if="userstatus && userstatus.real_data.review_status == 2"
			style="width: 50%; display: flex; flex-direction: column; justify-content: center; align-items:center; margin: 0 auto; margin-top: 50px;">
			<image src="../../../static/scuess.png" style="width: 100px; height: 100px;"></image>
			<text style="font-size: 16px; font-weight: bold; color: #fff;"
				v-if="$store.state.bgColor=='black'">{{out.successful}}</text>
			<text style="font-size: 16px; font-weight: bold; "
				v-if="$store.state.bgColor=='while'">{{out.successful}}</text>
		</view>
		<view v-else-if="userstatus && userstatus.real_data.review_status == 1"
			style="width: 50%; display: flex; flex-direction: column; justify-content: center; align-items:center; margin: 0 auto; margin-top: 50px;">
			<image src="../../../static/loading.png" style="width: 100px; height: 100px;"></image>
			<text style="font-size: 16px; font-weight: bold; color: #fff;"
				v-if="$store.state.bgColor=='black'">{{i18n.under_review}}</text>
			<text style="font-size: 16px; font-weight: bold; "
				v-if="$store.state.bgColor=='while'">{{i18n.under_review}}</text>
		</view>
		<view
			:style="this.$store.state.bgColor=='black'?'display: flex;flex-direction: column; width: 100%; color: #fff;':'display: flex;flex-direction: column; width: 100%; '"
			v-else>
			<text
				style="font-size: 18px; font-weight: bold; margin-left: 15px; padding: 35px; padding-left: 0px;">{{i18n.upload_the_front_and_back}}</text>
			<view style="display: flex; width: 100%;">
				<view
					:style="this.$store.state.bgColor=='black'?'display: flex; flex: 1; margin-left: 15px;height: 144px;background: #22252F;color: #fff;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 40%; flex-direction: column; justify-content: center; align-items: center; padding: 2px; padding-bottom: 10px;':'display: flex; flex: 1; margin-left: 15px;height: 144px;background: #FFFFFF;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 40%; flex-direction: column; justify-content: center; align-items: center; padding: 2px; padding-bottom: 10px;'">
					<text style="margin-top: 8px; font-size: 18px; font-weight: bold;">{{i18n.front}}</text>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" multiple name="1"
						width="226" height="226" :maxCount="1">
						<image src="../../../static/<EMAIL>" mode="widthFix"
							style="width: 275rpx; height: 226rpx;margin-top: 15px;"></image>
					</u-upload>
				</view>
				<view style="width: 4%;"></view>
				<view
					:style="this.$store.state.bgColor=='black'?'display: flex; flex: 1; margin-right: 15px;height: 144px;background: #22252F;color: #fff;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 40%; flex-direction: column; justify-content: center; align-items: center; padding: 2px;padding-bottom: 10px;':'display: flex; flex: 1; margin-right: 15px;height: 144px;background: #FFFFFF;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 40%; flex-direction: column; justify-content: center; align-items: center; padding: 2px;padding-bottom: 10px;'">
					<text style="margin-top: 8px;font-size: 18px; font-weight: bold;">{{i18n.back}}</text>
					<u-upload :fileList="fileList2" @afterRead="afterReadTwo" @delete="deletePicTwo" multiple name="2"
						width="226" height="226" :maxCount="1">
						<image src="../../../static/<EMAIL>" mode="widthFix"
							style="width: 275rpx; height: 226rpx;margin-top: 15px;"></image>
					</u-upload>
				</view>
			</view>
			<view style="width: 100%; padding: 10px; padding-left: 0px;">
				<text style=" color: #6F747C;margin-left: 14px; ">{{i18n.improve_information}}</text>
			</view>
			<view style="display: flex; align-items: center; width: 100%; ">
				<view style="font-size: 12px;width: 25%;  margin-left: 15px;">{{i18n.name}}</view>
				<view style="width: 100%; "> <u--input class="bsone" v-if="this.$store.state.bgColor=='black'"
						color="#fff" :placeholder="i18n.enter_name" v-model="name" style="margin: 1px; "></u--input>
					<u--input border="none" v-if="this.$store.state.bgColor=='while'" :placeholder="i18n.enter_name"
						v-model="name" style="margin-left: 15px; "></u--input>
				</view>
			</view>
			<view style="display: flex; align-items: center; width: 100%; ">
				<view style="font-size: 12px;width: 25%;  margin-left: 15px;">{{i18n.passport_idnumber}}</view>
				<view style="width: 100%; "> <u--input v-if="this.$store.state.bgColor=='black'" class="bsone"
						color="#fff" :placeholder="i18n.enter_passport_idnumber" v-model="card"
						style="margin: 15px;"></u--input>
					<u--input border="none" v-if="this.$store.state.bgColor=='while'"
						:placeholder="i18n.enter_passport_idnumber" v-model="card"
						style="margin-left: 15px;"></u--input>
				</view>
			</view>
			<view style="width:100%">
				<button @click="onsubmits" style="background: #0166fc; margin-top: 20px; border-radius: 60px;
														    border: 0;   font-size: 12px; margin: 15px; height: 45px; color: #FFFFFF;
				line-height: 45px !important;">{{i18n.submit}}</button>
			</view>
		</view>
		<!-- <view v-if="userstatus.real_status==1"
			style="width: 50%; display: flex; flex-direction: column; justify-content: center; align-items:center; margin: 0 auto; margin-top: 50px;">
			<image src="../../../static/<EMAIL>" style="width: 100px; height: 100px;"></image>
			<text style="font-size: 16px; font-weight: bold; color: #fff;"
				v-if="$store.state.bgColor=='black'">{{i18n.under_review}}</text>
			<text style="font-size: 16px; font-weight: bold; "
				v-if="$store.state.bgColor=='while'">{{i18n.under_review}}</text>
		</view>
		<view v-if="userstatus.real_status==2"
			:style="$store.state.bgColor=='black'?'display: flex; flex-direction: column; width: 95%; margin: 0 auto; height: 85px;background: #22252F;color: #fff;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1;':'display: flex; flex-direction: column; width: 95%; margin: 0 auto; height: 85px;background: #FFFFFF;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1;' ">
			<view style="display: flex;  
			width: 90%; justify-content: space-between; height: 20px;  margin: 0 auto;  margin-top: 15px;">
				<text style="font-size: 14px; font-weight: bold;">{{i18n.name}}:</text>
				<text>{{nameFormats(userstatus.real_data.name)}}</text>
			</view>
			<view style="display: flex;
							width: 90%; justify-content: space-between;height: 20px;  margin: 0 auto;  margin-top: 20px;">
				<text style="font-size: 14px; font-weight: bold;">{{i18n.passport_idnumber}}:</text>
				<text>{{formatIDcards(userstatus.real_data.card_id)}}</text>
			</view>
		</view> -->
	</view>
</template>

<script>
	import {
		nameFormat,
		formatIDcard
	} from '@/common/manger'

	export default {
		data() {
			return {
				fileList2: [],
				fileList1: [],
				userstatus: "",
				imgPaths: "",
				imgTwoPaths: "",
				name: "",
				card: "",
				names: "",
				cards: "",
				isloading:false
			}
		},
		computed: {
			i18n() {
				return this.$t("real")
			},
			out() {
				return this.$t("out")
			},
		},
		onLoad() {
			this.getData()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("real.real_name_auth")
			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F' // 导航栏背景颜色
				});
			}
			// this.names=nameFormat('王王王')
			// this.cards=formatIDcard('378987498878879856')
		},
		methods: {
			nameFormats(e) {
				return nameFormat(e)
			},
			formatIDcards(e) {
				return formatIDcard(e)
			},
			getData() {
				const res = this.$H.post('/api/v1/realState', false).then(res => {
					if (res.type == "ok" && res.data.real_data) {
						this.userstatus = res.data
					}
					this.isloading = true
				})
			},
			onsubmits() {
				if (this.name == "") {
					return uni.$u.toast(this.$t('real.enter_name'));
				}
				if (this.card == "") {
					return uni.$u.toast(this.$t('real.enter_passport_idnumber'));
				}
				// if (!this.$u.test.idCard(this.card)) {
				// 	return uni.$u.toast("this.$t('real.enter_passport_idnumber')");
				// }
				const res = this.$H.post('/api/v1/saveUserReal', false, {
					id_type: 0,
					name: this.name,
					card_id: this.card,
					real_type: 1,
					front_pic: this.imgPaths,
					reverse_pic: this.imgTwoPaths
				}).then(res => {
					if (res.type == "ok") {
						uni.$u.toast(this.i18n.upload_success_waiting);
						this.getData()
					}
				})
			},
			deletePicTwo(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			afterReadTwo(event) {
				let that = this
				let token = uni.getStorageSync('token')
				if (!token) {
					return uni.$u.toast(this.$t("real.re_certification"));
				}
				uni.showLoading()
				that.uploadImgFinished = false
				let lists = event.file
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				this.fileList2 = this.fileList2.slice(0, 9)
				this.imgListsTwo = []
				console.log('待上传图片==', this.fileList2)
				uni.uploadFile({
					url: this.$C.webUrl + '/api/v1/upload',
					filePath: this.fileList2[0].url,
					name: 'file',
					formData: {
						type: 0
					},
					header: {
						'Authorization': "Bearer " + token
					},
					success(res) {
						uni.hideLoading()
						let result = res.data.replace(/\n/g, "").replace(/\n/g, "").replace(/\n/g, "").replace(
							/\s/g, "")
						res = JSON.parse(decodeURIComponent(result))
						// that.imgTwoPaths = that.$C.webUrl + "/storage/" + res.data.file_path
						that.imgTwoPaths = res.data.file_path
					},
					fail(res) {
						reject(res)
					},
					complete() {
						uni.hideLoading()
					}
				})
				// this.upimages(this.fileList1, 0)
			},
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			afterRead(event) {
				let that = this
				let token = uni.getStorageSync('token')
				if (!token) {
					return uni.$u.toast(this.$t("real.re_certification"));
				}
				uni.showLoading()
				that.uploadImgFinished = false
				let lists = event.file
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				this.fileList1 = this.fileList1.slice(0, 9)
				this.imgListsTwo = []
				console.log('待上传图片==', this.fileList1)
				this.imgPaths = ""
				uni.uploadFile({
					url: this.$C.webUrl + '/api/v1/upload',
					filePath: this.fileList1[0].url,
					name: 'file',
					formData: {
						type: 0
					},
					header: {
						'Authorization': "Bearer " + token
					},
					success(res) {
						uni.hideLoading()
						let result = res.data.replace(/\n/g, "").replace(/\n/g, "").replace(/\n/g, "").replace(
							/\s/g, "")
						res = JSON.parse(decodeURIComponent(result))
						// that.imgPaths = that.$C.webUrl + "/storage/" + res.data.file_path
						that.imgPaths = res.data.file_path
					},
					fail(res) {
						reject(res)
					},
					complete() {
						uni.hideLoading()
					}
				})
				// this.upimages(this.fileList1, 0)
			}
		},
		filters: {
			hideMiddle(val) {
				return `${val.substring(0,3)}****${val.substring(val.length-4)}`
			}
		}
	}
</script>

<style lang='scss'>
	page {
		background-color: #FFFFFF;

	}

	uni-page {
		height: auto !important;
	}

	.u-input[data-v-fdbb9fe6] {
		padding: 11px 9px !important;
	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
	}

	/deep/.u-upload__deletable {

		background-color: initial;
	}
</style>