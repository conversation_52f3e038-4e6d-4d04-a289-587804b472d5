<template>
	<view :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<view style="height: 15px;"></view>
		<view
			:style="$store.state.bgColor=='black'?'bheight: 430px;background: #22252F;color: #ffffff;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;':'bheight: 430px;background: #FFFFFF;;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;' ">
			<view class="wrap">
				<view style="display: flex; align-items: flex-end; width: 100%; margin-top: 20px;">
					<view style="flex: 1;font-size: 14px;height: 100%; font-weight: bold;width: 100%;">{{i18n.currency}}
					</view>

					<view v-if="$store.state.bgColor=='black'" style="width: 70%; margin-top: 15px;"
						@click="shows = true"> <u--input border="none" v-model="values.name" :disabled="true"
							disabledColor="#222" class="bsone" placeholder="" inputAlign="left" style="height: 25px;"
							suffixIcon="arrow-right" color="#ffffff"
							suffixIconStyle="color: #fff;font-size:16px;font-weight: bold;"></u--input>
					</view>

					<view v-if="$store.state.bgColor=='while'" style="width: 70%; margin-top: 15px;"
						@click="shows = true">
						<u--input v-model="values.name" :disabled="true" disabledColor="#ffffff" placeholder=""
							inputAlign="left" style="height: 25px;" suffixIcon="arrow-right" border="none"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
					</view>

				</view>
				<view style="display: flex; align-items: flex-end; width: 100%; margin-top: 10px;">
					<view style="flex: 1;font-size: 14px; font-weight: bold;width: 100%;">{{i18n.wallet_address}}</view>
					<view style="width: 70%; margin-top: 15px;"> <u--input border="none"
							v-if="$store.state.bgColor=='black'" class="bsone" :placeholder="i18n.wallet_address"
							v-model="adresss" style="height: 33px; color: #fff;"></u--input>
						<u--input border="none" v-if="$store.state.bgColor=='while'" :placeholder="i18n.wallet_address"
							v-model="adresss" style="height: 33px;"></u--input>
					</view>

				</view>
			</view>

			<!-- <view
				style="display: flex; flex-direction: column;  align-items: center; width: 100%; font-size: 12px; margin-top: 15px; padding-bottom: 10px; color: #6D6F7C;">
				<view style="flex: 1;font-size: 15px; font-weight: bold;width: 90%; color: #222222; margin-top: 10px;">
					{{$t('mine.wallet')}}</view>
				<u-upload v-if="$store.state.bgColor=='black'" :fileList="fileList1" @afterRead="afterRead"
					@delete="deletePic" multiple name="1" width="226" height="226" :maxCount="1">
					<image src="../../../../static/buploads.png" mode="widthFix" style="width: 226rpx; height: 226rpx">
					</image>
				</u-upload>

				<u-upload v-if="$store.state.bgColor=='while'" :fileList="fileList1" @afterRead="afterRead"
					@delete="deletePic" multiple name="1" width="226" height="226" :maxCount="1">
					<image src="../../../../static/uploads.png" mode="widthFix" style="width: 226rpx; height: 226rpx">
					</image>
				</u-upload>
			</view> -->
			
			<view style="width:93%;margin: 0 auto; margin-top: 30px; ">
				<u-button :loading="loading" loadingText="......" @click="obutton()" loadingSize="30" style="background: #0166fc; margin-top: 20px;border: 0;  border-radius: 45px; font-size: 12px;  height: 45px; color: #FFFFFF;
						line-height: 55px !important;">{{i18n.add}}</u-button>
			</view>
		</view>
		<u-picker :show="shows" v-if="$store.state.bgColor=='black'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			style="border-radius: 18px;" class="bnsss"></u-picker>
		<u-picker :show="shows" v-if="$store.state.bgColor=='while'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm" style="border-radius: 18px;"
			class="wnsss"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				values: '',
				loading: false,
				dfindex: [0],
				fileList1: [],
				imgPaths: "",
				columns: [
					['USDT-TRC20', '200', '300']
				],
				qrcode: "",
				currency: 0,
				adresss: "",
				shows: false,
			}
		},
		onLoad() {
			this.getData()
			// this.values = this.columns[0][0]
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("wallet.bind_digital_currency_address")
			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff',
					backgroundColor: '#22252F'
				});
			}

		},
		computed: {
			i18n() {
				return this.$t("wallet")
			},
			i18nOther() {
				return this.$t("other")
			},
		},
		methods: {
			afterRead(event) {
				let that = this
				that.uploadImgFinished = false
				let lists = event.file
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				this.fileList1 = this.fileList1.slice(0, 9)
				this.imgLists = []
				let token = this.$store.state.token
				if (!token) {
					return uni.$u.toast(this.$t("real.re_certification"));
				}

				console.log(this.fileList1[0].url)

				uni.showLoading()
				this.imgPaths = ""
				uni.uploadFile({
					url: this.$C.webUrl + '/api/v1/upload',
					filePath: this.fileList1[0].url,
					name: 'file',
					formData: {
						type: 0
					},
					header: {
						'Authorization': "Bearer " + token
					},
					success(res) {
						uni.hideLoading()
						let result = res.data.replace(/\n/g, "").replace(/\n/g, "").replace(/\n/g, "").replace(
							/\s/g, "")
						res = JSON.parse(decodeURIComponent(result))


						that.imgPaths = that.$C.webUrl + "/storage/" + res.data.file_path
						console.log(that.imgPaths)

					},
					fail(res) {

						reject(res)
					},
					complete() {
						uni.hideLoading()
					}
				})






				console.log('待上传图片==', this.fileList1)
				// this.upimages(this.fileList2, 0)
			},
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			confirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e

				this.currency = value[0].id
				this.values = value[0]
				this.shows = false
			},
			cancel(e) {
				this.shows = false
			},
			getData() {


				const res = this.$H.get('/api/v1/extractCurrency', false).then(res => {

					if (res.type == "ok") {
						var currency = []
						var currencyList = []

						res.data.map(item => {
							currency.push({
								"name": item.agreement,
								"id": item.id

							})
						})


						currencyList.push(currency)
						this.columns = currencyList
						this.values = this.columns[0][0]

						this.currency = this.columns[0][0].id

					}
				})











				// const res =  this.$H.get('/api/v1/quotation_new',false).then(res => {

				// 					    if(res.type=="ok"){
				// 							var currency=[]
				// 							var currencyList=[]
				// 						    this.resData =  res.data[0].quotation
				// 								  res.data[0].quotation.map(item=>{
				// 									 currency.push(
				// 									 {
				// 										 "name":item.currency_name+"-"+item.legal_name,
				// 										 "id":item.currency.id

				// 									 }
				// 									 )

				// 									  })


				//                                currencyList.push(currency)
				// 							   this.columns=currencyList  

				// 							this.values = this.columns[0][0]

				// 							this.currency=this.columns[0][0].id

				// 						}
				// 			})
			},
			obutton() {




				let {
					adresss,
					currency,
					qrcode
				} = this



				if (!adresss) {

					uni.$u.toast(this.$t("wallet.enter_the_wallet_address"));
					// uni.showToast({
					// 	title: '钱包地址不能为空' || '校验错误',
					// 	icon: 'none'
					// });
					return false
				}
				this.loading = true


				const res = this.$H.post('/api/v1/saveUsdtInfo', false, {
					// currency:currency,
					money: this.values.name,
					account: adresss,
					digital_currency_id: this.values.id,
					qrcode: this.imgPaths
				}).then(res => {
					this.loading = false
					if (res.type == "ok") {
						this.adresss = ""
						this.fileList1 = []

						uni.$u.toast(res.message);
						// uni.$u.toast(this.$t("out.successful")); 
					}
				})


				// const res =  this.$H.post('/api/v1/userWalletSave',false,{
				// 				   // currency:currency,
				// 				   currency:this.values.id,
				// 				   address:adresss,
				// 				   qrcode:this.imgPaths
				// 				   }).then(res => {
				// 					  	this.loading=false
				// 						  if(res.type=="ok"){
				// 							  this.adresss=""
				// 							  this.fileList1=[]
				// 						  	 uni.$u.toast(this.$t("out.successful")); 
				// 						  }
				// 					})

			}
		}
	}
</script>

<style>
	page {
		background-color: #F7F8FA;
		overflow: initial;
	}

	.bnsss /deep/.uni-picker-view-mask {
		background: inherit;
	}

	.bnsss /deep/.u-picker__view__column__item {
		color: #fff;
	}

	.bnsss /deep/.u-popup__content {

		border-radius: 10px;
		background: #222;
	}

	.wrap {
		padding: 24rpx;
	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
	}

	.u-border {
		border-color: #D2D6E8;
	}

	.u-button--info[data-v-3bf2dba7] {
		color: #4B53F5;
		border-color: #ebedf0;
		border-width: 0px;
		font-weight: bold;
	}

	.blacks /deep/.uni-input-input {
		/* color: #ffff00; */
		/* border: none; */
		/* background-color: #4B53F5; */
	}

	/deep/.u-upload__deletable {

		background-color: initial;
	}

	page {
		position: inherit;
	}
</style>