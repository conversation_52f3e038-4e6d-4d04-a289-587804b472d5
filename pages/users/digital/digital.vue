<template>
	<view :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<view v-for="(orderItem, index) in indexList" :key="index" class="cu-list menu-avatar"
			:style="$store.state.bgColor=='black'?'width: 90%; background: #22252F; color: #fff; margin: 0 auto; margin-top: 10px; height: 90px; border-radius: 5px;':'width: 90%; background: #FFFFFF;  margin: 0 auto; margin-top: 10px; height: 90px; border-radius: 5px;' ">
			<view class="cu-item" :class="modalName=='move-box-'+ index?'move-cur':''"
				:style="$store.state.bgColor=='black'?'background: #22252F;':'background: #ffffff;'"
				@touchstart="ListTouchStart" @touchmove.stop.prevent="ListTouchMove" @touchend="ListTouchEnd"
				:data-target="'move-box-' + index">

				<view style="display:flex; width: 100%; font-size: 14px; ">
					<view
						style="display: flex; justify-content:space-between; margin-top: 0; align-items: center; width: 90%; margin: 0 auto;">
						<text style="font-size: 14px;font-weight: bold;">{{i18n.currency}}:</text>
						<text style="">{{orderItem.digital_currency ? orderItem.digital_currency.agreement:'--'}}</text>
					</view>
				</view>
				<view style="display:flex; width: 100%; font-size: 14px; margin-top: 15px; ">
					<view
						style="display: flex; justify-content:space-between; align-items: center;  width: 90%; margin: 0 auto;">
						<text style="font-size: 14px;font-weight: bold;">{{i18n.withdrawal_address}}:</text>
						<text style="">{{orderItem.account}}</text>
					</view>
				</view>
				<view class="move" @touchstart="vclick(orderItem)" style="height: 95px;">
					<view class="bg-red">{{i18n.delete}}</view>
				</view>
			</view>
		</view>

		<u-modal :show="ishow" @cancel="cancel" @confirm="confirm" :showCancelButton="true" :confirmText="confirmText"
			:cancelText="cancelText" :title="title" :content='content'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ishow: false,
				title: '操作',
				content: '确定要删除操作?',
				modalName: null,
				listTouchStart: 0,
				listTouchDirection: null,
				isdel: true,
				confirmText: "确认",
				cancelText: "取消",
				indexList: [],
				isOrder: null
			}
		},
		onNavigationBarButtonTap() {
			uni.navigateTo({
				url: "/pages/users/digital/addDigital/addDigital"
			})
		},
		computed: {
			i18n() {
				return this.$t("wallet")
			}
		},
		onLoad() {
			this.getData()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("wallet.digital_currency_address")

			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff',
					backgroundColor: '#22252F'
				});
			}
			document.querySelectorAll('.uni-page-head .uni-page-head-ft .uni-page-head-btn')[0].querySelector(
				'.uni-btn-icon').innerText = "+" + this.$t("wallet.add");
		},
		onShow() {
			this.getData()
		},
		methods: {
			getData() {
				const res = this.$H.get('/api/v1/usdtInfo', false).then(res => {
					if (res.type == "ok") {
						this.indexList = res.data
					}
				})

				// const res =  this.$H.post('/api/v1/userWalletList',false).then(res => {
				// 								    // this.loading=false
				// 					    if(res.type=="ok"){
				// 							var arr = []
				// 							res.data.data.map(item=>{
				// 								let date = new Date(Date.parse(new Date(item.create_time)))
				// 								item.create_time = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm')
				// 								arr.push(item)
				// 							})
				// 								this.indexList = arr
				// 									}else{

				// 									}
				// 					})
			},
			confirm() {
				this.modalName = null
				const res = this.$H.get('/api/v1/usdtDelete', false, {
					id: this.isOrder.id
				}).then(res => {
					if (res.type == "ok") {
						uni.$u.toast(res.message);
						// uni.$u.toast(this.$t("deposit.completed"));
						const has = this.indexList.findIndex(item => item.id == this.isOrder.id)
						if (has > -1) {
							this.indexList.splice(has, 1)
						}
						this.isdel = true
						this.ishow = false
					}
				})
			},
			cancel() {
				this.isdel = true
				this.ishow = false
			},
			// ListTouch触摸开始
			ListTouchStart(e) {
				e.stopPropagation();
				this.listTouchStart = e.touches[0].pageX
				if (this.modalName) {
					console.log('dddddddddddddddddddd')
					// if (this.isdel) {
					this.modalName = null
					// }

				} else {
					this.listTouchDirection = 'left'
				}
			},

			// ListTouch计算方向
			ListTouchMove(e) {
				e.stopPropagation();
				this.listTouchDirection = e.touches[0].pageX - this.listTouchStart > 0 ? 'right' : 'left'
			},

			// ListTouch计算滚动
			ListTouchEnd(e) {
				e.stopPropagation();
				if (this.listTouchDirection == 'left') {
					this.modalName = e.currentTarget.dataset.target
				} else {
					if (this.isdel) {
						this.modalName = null
					}
				}
				this.listTouchDirection = null
			},
			vclick(e) {
				console.log('dddd')
				this.isOrder = e
				this.isdel = false
				this.ishow = true
				// this.isdel = true;
			}
		}
	}
</script>

<style>
	.whiles {
		background: #F7F8FA;
	}

	.blacks {
		background: #1A1C24;
		padding-top: 1px;
	}

	.cu-list.menu-avatar>.cu-item {
		height: 85px;
		justify-content: center;
		flex-direction: column;
	}

	.cu-list.menu-avatar>.cu-item:after,
	.cu-list.menu>.cu-item:after {

		border-bottom: 0px;

	}

	.cu-list>.cu-item.move-cur {
		-webkit-transform: translateX(-60px);
		transform: translateX(-60px);
	}

	.cu-list>.cu-item .move {

		width: 60px;

	}

	.bg-red {
		font-size: 14px;
	}

	page {
		background-color: #F7F8FA;
		/* height: auto; */
		position: initial;
		overflow: inherit;
	}
</style>