<template>
	<view :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<view v-if="$store.state.bgColor=='black'" style="height: 10px; background: #1A1C24;"></view>
		<view
			:style="$store.state.bgColor=='black'?'background: #22252F;width: 95%;color:#fff;margin:0 auto; display: flex; flex-direction: column; justify-content: center; align-items: center;':'width: 95%;margin:0 auto; display: flex; flex-direction: column; justify-content: center; align-items: center;'">
			<view style="height: 25px;"></view>
			<!-- 顶部选项卡 -->
			<view class="scroll-view">
				<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
					:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
					:id="'tab'+index">{{item}}</view>
			</view>

			<!-- 使用简单的选项卡切换，移除swiper -->
			<view style="width:95%; padding-left:15px; min-height: calc(100vh - 80px);">
				<view v-show="tabIndex === 1" class="section-area" style="padding-bottom: 50px;">

					<view style="width: 95%; " :ref="'ulists0'">
						<view style="display: flex; align-items: flex-start; width: 100%; margin-top: 25px;">
							<view
								style="font-size: 15px; font-weight: bold;width: 35%; padding-top: 12px; padding-right: 15px;">
								{{i18n.currency}}
							</view>
							<view style="width: 65%; margin-top: 15px;" @click="shows = true">
								<u--input border="none" v-model="values.name" :disabled="true" disabledColor="#ffffff"
									placeholder="" inputAlign="left" style="height: 25px;" suffixIcon="arrow-right"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
							</view>
						</view>
						<!-- 字段下方分隔符 -->
						<view
							:style="$store.state.bgColor=='black'?'width: 100%; height: 1px; background-color: #3A3A3A; margin: 15px 0 10px 0; min-height: 1px; border: none;':'width: 100%; height: 1px; background-color: #E5E5E5; margin: 15px 0 10px 0; min-height: 1px; border: none;'">
						</view>

						<view style="display: flex; align-items: flex-start; width: 100%;">
							<view
								style="font-size: 15px; font-weight: bold;width: 35%; padding-top: 12px; padding-right: 15px;">
								{{i18n.withdrawal_adress}}
							</view>
							<view style="width: 65%; margin-top: 15px;" @click="setdshows()">
								<u--input border="none" v-model="tbvalues" :disabled="true" disabledColor="#ffffff"
									placeholder="" inputAlign="left" style="height: 25px;" suffixIcon="arrow-right"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
							</view>
						</view>
						<!-- 字段下方分隔符 -->
						<view
							:style="$store.state.bgColor=='black'?'width: 100%; height: 1px; background-color: #3A3A3A; margin: 10px 0; min-height: 1px; border: none;':'width: 100%; height: 1px; background-color: #E5E5E5; margin: 10px 0; min-height: 1px; border: none;'">
						</view>
						<view style="display: flex; align-items: flex-start; width: 100%;">
							<view
								style="font-size: 15px; font-weight: bold;width: 35%; padding-top: 12px; padding-right: 15px;">
								{{i18n.number_of_withdrawal}}
							</view>
							<view style="width: 65%; margin-top: 15px;">
								<u--input border="none" :placeholder="i18n.number_of_withdrawal" v-model="cnumber"
									type="digit"></u--input>
							</view>

						</view>
						<!-- 字段下方分隔符 -->
						<view
							:style="$store.state.bgColor=='black'?'width: 100%; height: 1px; background-color: #3A3A3A; margin: 10px 0; min-height: 1px; border: none;':'width: 100%; height: 1px; background-color: #E5E5E5; margin: 10px 0; min-height: 1px; border: none;'">
						</view>
						<view style="display: flex; align-items: flex-start; width: 100%;">
							<view
								style="font-size: 15px; font-weight: bold;width: 35%; padding-top: 12px; padding-right: 15px;">
								{{i18n.remarks}}
							</view>
							<view style="width: 65%; margin-top: 15px;">
								<u--textarea border="none" v-model="remarks" :placeholder="i18n.remarks" autoHeight
									style="padding: 0;"></u--textarea>
							</view>

						</view>

						<view
							:style="$store.state.bgColor=='black'?'display: flex; flex-direction: column; background: #333748; align-items: center; width: 100%; font-size: 12px; margin-top: 15px; color: #6D6F7C;':'display: flex; flex-direction: column; background: #F7F9FE; align-items: center; width: 100%; font-size: 12px; margin-top: 15px; color: #6D6F7C;'">

							<view style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px;">
								<text>{{i18n.hand_fee}}</text>
								<text>{{rateses}}</text>
							</view>
							<view style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px;">
								<text>{{i18n.estimated_amount}}</text>
								<text>{{jbnumber}} USD</text>
							</view>
							<view
								style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px; margin-bottom: 13px;">
								<text>{{i18n.balance}}</text>
								<text>{{users.usdt}} USD</text>
							</view>

						</view>
						<view style="width:100%">
							<button @click="tsubmit()" :loading="loading" :disabled="disableds" loadingText=""
								loadingSize="30" style="background: #0166fc; margin-top: 20px; border-radius: 1000rpx;
																		    border: 0;   font-size: 12px; height: 45px; color: #FFFFFF;
								                line-height: 45px !important;">{{i18n.withdrawal_}}</button>
						</view>

						<!-- 出金记录区域 - 与cjrecord页面完全一致 -->
						<view style="width: 100%; margin-top: 30px;">
							<view style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 20px;">
								{{i18n.coin_withdrawal_record}}
							</view>

							<!-- 记录列表 - 完全复制cjrecord页面的结构 -->
							<view v-if="$store.state.bgColor=='black'" style="height: 5px; background: #1A1C24;"></view>
							<view class="record-card" v-for="(orderItem, index2) in indexList" :key="index2">
								<view style="display:flex; width: 100%; padding-top: 15px; ">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center;  margin-left: 5px; ">
										<text
											style="color:#6D6F7C;font-size: 14px; text-align: left;">{{i18n.number_of_withdrawal}}</text>
										<text class="fonts"
											style="font-size: 12px; margin-top: 5px;">{{orderItem.number}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
										<text style="color:#6D6F7C;font-size: 14px;">{{i18n.number_of_arrival}}</text>
										<text class="fonts"
											style="font-size: 12px; margin-top: 5px;">{{orderItem.real_number}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-right: 15px;">
										<text style="color:#6D6F7C;font-size: 14px;">{{i18n.currency}}</text>
										<text class="fonts"
											style="font-size: 12px;margin-top: 5px;">{{orderItem.currency}}</text>
									</view>
								</view>
								<view
									:style="$store.state.bgColor=='black'?'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;color: #fff !important;':'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;'">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-left: 5px; ">
										<text>{{i18n.hand_fee}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
										<text>{{i18n.status}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column;   align-items: center;  margin-right: 15px;">
										<text>{{$t("records.time")}}</text>
									</view>
								</view>
								<view style="display:flex; width: 100%; margin-top: -5px; font-size: 12px;">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: flex-start;width: 98px; text-align: left; margin-left: 15px; ">
										<text class="fonts">{{formatRecordFee(orderItem)}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; text-align: right; flex: 1;">
										<text class="fonts" style="font-size: 14px;">{{orderItem.status_name}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column;  padding-top: 15px; align-items: center; width: 85px; margin-right: 15px; text-align: right;">
										<text class="fonts">{{orderItem.create_time}}</text>
									</view>
								</view>
								<view
									style="display:flex;flex-direction: column; width: 100%; padding-top: 18px; padding-bottom: 15px;">
									<!-- 数字货币记录显示地址 -->
									<view v-if="tabIndex === 0"
										style="display: flex; flex-direction:column;  width: 100%;  margin-left: 15px; ">
										<text style="font-size: 14px;color: #6D6F7C;">{{i18n.withdrawal_adress}}</text>
										<text class="fonts"
											:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.address}}</text>
									</view>
									<!-- 银行卡记录显示银行信息 -->
									<view v-if="tabIndex === 1">
										<view style="display: flex; flex-direction:column;  width: 100%;  margin-left: 15px; ">
											<text style="font-size: 14px;color: #6D6F7C;">{{i18n.bank_name}}</text>
											<text class="fonts"
												:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.bank_info}}</text>
										</view>
										<view style="display: flex; flex-direction:column;  width: 100%; margin-top: 15px; margin-left: 15px; ">
											<text style="font-size: 14px;color: #6D6F7C;">{{i18n.payee}}</text>
											<text class="fonts"
												:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.payee_info}}</text>
										</view>
									</view>
									<view
										style="display: flex; flex-direction:column;  width: 100%;margin-top: 20px;  margin-left: 15px; ">
										<text style="font-size: 14px;color: #6D6F7C;">{{i18n.remarks}}</text>
										<text class="fonts"
											:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.remark}}</text>
									</view>
								</view>
							</view>
							<view v-if="this.indexList.length>0" style="height: 15px;"></view>

							<view v-if="this.indexList.length>0"
								style=" width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #AFAFAF;">
								<text>{{loding}}</text>
							</view>
						</view>
					</view>


				</view>

				<view v-show="tabIndex === 0" class="section-area" style="padding-bottom: 50px;">

					<view style="width: 95%; " :ref="'ulists1'">
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 25px;">
							<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">{{i18n.currency}}
							</view>
							<!-- <view style="width: 100%; margin-top: 15px;">
                  <u--input v-model="hnames" :disabled="true"
                    disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 25px;"

                    suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
                  <!-- <text>USDT</text> -->
							<!-- <u--input v-model="yhvalues.name" :disabled="true"
                    disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 25px;"
                    suffixIcon="arrow-right"
                    suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input> -->
							<!-- </view> -->
							<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='black'"
								@click="yhshows = true"><u--input v-model="cjyhvalues.name" :disabled="true"
									disabledColor="#22252F" placeholder="" inputAlign="left" style="height: 33px;"
									suffixIcon="arrow-right" class="bsone" color="#ffffff"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
							</view>
							<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='while'"
								@click="yhshows = true"><u--input v-model="cjyhvalues.name" :disabled="true"
									disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 33px;"
									suffixIcon="arrow-right"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
							</view>
						</view>
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 20px;">
							<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%; margin-bottom: 5px;">
								{{i18n.reipient_account}}
							</view>
							<view style="width: 100%; margin-top: 15px;" @click="setyhdshows()">
								<!-- #ifndef APP-NVUE -->
								<u-input v-model="yhtbvalues" :disabled="true" disabledColor="#ffffff" placeholder=""
									inputAlign="left" style="height: 25px;" suffixIcon="arrow-right"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;">
									<u--text :text="yhnames" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
								</u-input>
								<!-- #endif -->
								<!-- #ifdef APP-NVUE -->
								<u--input v-model="yhtbvalues" :disabled="true" disabledColor="#ffffff" placeholder=""
									inputAlign="left" style="height: 25px;" suffixIcon="arrow-right"
									suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;">
									<u--text :text="yhnames" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
								</u--input>
								<!-- #endif -->
							</view>

						</view>
						<!-- 字段下方分隔符 -->
						<view
							:style="$store.state.bgColor=='black'?'width: 100%; height: 1px; background-color: #3A3A3A; margin: 10px 0; min-height: 1px; border: none;':'width: 100%; height: 1px; background-color: #E5E5E5; margin: 10px 0; min-height: 1px; border: none;'">
						</view>
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 20px;">
							<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%; margin-bottom: 5px;">
								{{i18n.number_of_withdrawal}}
							</view>
							<view style="width: 100%; margin-top: 15px;"> <u--input
									:placeholder="i18n.number_of_withdrawal" v-model="yhcnumber"
									type="digit"></u--input></view>

						</view>
						<!-- 字段下方分隔符 -->
						<view
							:style="$store.state.bgColor=='black'?'width: 100%; height: 1px; background-color: #3A3A3A; margin: 10px 0; min-height: 1px; border: none;':'width: 100%; height: 1px; background-color: #E5E5E5; margin: 10px 0; min-height: 1px; border: none;'">
						</view>
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 20px;">
							<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%; margin-bottom: 5px;">
								{{i18n.remarks}}
							</view>
							<view style="width: 100%; margin-top: 15px;"> <u--textarea :maxlength="maxNumber"
									v-model="yhremarks" placeholder="" count style="padding: 0;"></u--textarea></view>

						</view>

						<view
							:style="$store.state.bgColor=='black'?'display: flex; flex-direction: column; background: #333748; align-items: center; width: 100%; font-size: 12px; margin-top: 15px; color: #6D6F7C;':'display: flex; flex-direction: column; background: #F7F9FE; align-items: center; width: 100%; font-size: 12px; margin-top: 15px; color: #6D6F7C;'">
							<view
								style="flex: 1;font-size: 15px; font-weight: bold;width: 90%; color: #222222; margin-top: 13px;">
								{{i18n.details}}
							</view>
							<view style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px;">
								<text>{{i18n.hand_fee}} ({{yhFeeDisplay}})</text>
								<text>{{yhrateses}} USD</text>
							</view>
							<view style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px;">
								<text>{{i18n.estimated_amount}}</text>
								<text>{{yhsnumber.toFixed(4)}} {{cjyhvalues.name || 'USD'}}</text>
							</view>
							<view
								style="display:flex; justify-content: space-between; width: 90%; margin-top: 13px; margin-bottom: 13px;">
								<text>{{i18n.balance}}</text>
								<text>{{users.usdt}} USD</text>
							</view>

						</view>
						<view style="width:100%">
							<button @click="yhtsubmit()" :loading="loading" loadingText="" style="background: #3F47F4; margin-top: 20px;
																		    border: 0;   font-size: 12px; height: 55px; color: #FFFFFF;
								                line-height: 55px !important;">{{i18n.withdrawal_}}</button>
						</view>

						<!-- 出金记录区域 - 与cjrecord页面完全一致 -->
						<view style="width: 100%; margin-top: 30px;">
							<view style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 20px;">
								{{i18n.coin_withdrawal_record}}
							</view>

							<!-- 记录列表 - 完全复制cjrecord页面的结构 -->
							<view v-if="$store.state.bgColor=='black'" style="height: 5px; background: #1A1C24;"></view>
							<view class="record-card" v-for="(orderItem, index2) in indexList" :key="index2">
								<view style="display:flex; width: 100%; padding-top: 15px; ">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center;  margin-left: 5px; ">
										<text
											style="color:#6D6F7C;font-size: 14px; text-align: left;">{{i18n.number_of_withdrawal}}</text>
										<text class="fonts"
											style="font-size: 12px; margin-top: 5px;">{{orderItem.number}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
										<text style="color:#6D6F7C;font-size: 14px;">{{i18n.number_of_arrival}}</text>
										<text class="fonts"
											style="font-size: 12px; margin-top: 5px;">{{orderItem.real_number}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-right: 15px;">
										<text style="color:#6D6F7C;font-size: 14px;">{{i18n.currency}}</text>
										<text class="fonts"
											style="font-size: 12px;margin-top: 5px;">{{orderItem.currency}}</text>
									</view>
								</view>
								<view
									:style="$store.state.bgColor=='black'?'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;color: #fff !important;':'display:flex; width: 100%; padding-top: 18px; color: #6D6F7C;'">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; margin-left: 5px; ">
										<text>{{i18n.hand_fee}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
										<text>{{i18n.status}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column;   align-items: center;  margin-right: 15px;">
										<text>{{$t("records.time")}}</text>
									</view>
								</view>
								<view style="display:flex; width: 100%; margin-top: -5px; font-size: 12px;">
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: flex-start;width: 98px; text-align: left; margin-left: 15px; ">
										<text class="fonts">{{formatRecordFee(orderItem)}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column; justify-content: center; align-items: center; text-align: right; flex: 1;">
										<text class="fonts" style="font-size: 14px;">{{orderItem.status_name}}</text>
									</view>
									<view
										style="display: flex; flex-direction:column;  padding-top: 15px; align-items: center; width: 85px; margin-right: 15px; text-align: right;">
										<text class="fonts">{{orderItem.create_time}}</text>
									</view>
								</view>
								<view
									style="display:flex;flex-direction: column; width: 100%; padding-top: 18px; padding-bottom: 15px;">
									<!-- 数字货币记录显示地址 -->
									<view v-if="tabIndex === 0"
										style="display: flex; flex-direction:column;  width: 100%;  margin-left: 15px; ">
										<text style="font-size: 14px;color: #6D6F7C;">{{i18n.withdrawal_adress}}</text>
										<text class="fonts"
											:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.address}}</text>
									</view>
									<!-- 银行卡记录显示银行信息 -->
									<view v-if="tabIndex === 1">
										<view style="display: flex; flex-direction:column;  width: 100%;  margin-left: 15px; ">
											<text style="font-size: 14px;color: #6D6F7C;">{{i18n.bank_name}}</text>
											<text class="fonts"
												:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.bank_info}}</text>
										</view>
										<view style="display: flex; flex-direction:column;  width: 100%; margin-top: 15px; margin-left: 15px; ">
											<text style="font-size: 14px;color: #6D6F7C;">{{i18n.payee}}</text>
											<text class="fonts"
												:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.payee_info}}</text>
										</view>
									</view>
									<view
										style="display: flex; flex-direction:column;  width: 100%;margin-top: 20px;  margin-left: 15px; ">
										<text style="font-size: 14px;color: #6D6F7C;">{{i18n.remarks}}</text>
										<text class="fonts"
											:style="$store.state.bgColor=='black'?'font-size: 12px; margin-top: 5px;color: #fff !important;':'font-size: 12px; margin-top: 5px;'">{{orderItem.remark}}</text>
									</view>
								</view>
							</view>
							<view v-if="this.indexList.length>0" style="height: 15px;"></view>

							<view v-if="this.indexList.length>0"
								style=" width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #AFAFAF;">
								<text>{{loding}}</text>
							</view>
						</view>
					</view>


				</view>
			</view>


		</view>
		<u-picker :show="yhshows" v-if="$store.state.bgColor=='black'" ref="yhuPicker" :itemHeight="100"
			:defaultIndex="yhdfindex" :columns="yhcolumns" keyName="name" @cancel="yhcancel" @confirm="yhconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker v-if="$store.state.bgColor=='while'" :show="yhshows" ref="yhuPicker" :itemHeight="100"
			:defaultIndex="yhdfindex" :columns="yhcolumns" keyName="name" @cancel="yhcancel" @confirm="yhconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-picker :show="shows" ref="uPicker" v-if="$store.state.bgColor=='black'" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker :show="shows" ref="uPicker" v-if="$store.state.bgColor=='while'" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

		<u-picker :show="dshows" ref="duPicker" v-if="$store.state.bgColor=='black'" :itemHeight="100"
			:defaultIndex="ddfindex" :columns="walletAddress" @cancel="dcancel" @confirm="dconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #222;" class="bnsss" keyName="account"></u-picker>
		<u-picker :show="dshows" ref="duPicker" v-if="$store.state.bgColor=='while'" :itemHeight="100"
			:defaultIndex="ddfindex" :columns="walletAddress" @cancel="dcancel" @confirm="dconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #fff;" class="wnsss" keyName="account"></u-picker>

		<u-picker :show="yhdshows" ref="yhduPicker" v-if="$store.state.bgColor=='black'" :itemHeight="100"
			:defaultIndex="yhddfindex" :columns="yhwalletAddress" keyName="account" @cancel="yhdcancel"
			@confirm="yhdconfirm" style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker :show="yhdshows" v-if="$store.state.bgColor=='while'" ref="yhduPicker" :itemHeight="100"
			:defaultIndex="yhddfindex" :columns="yhwalletAddress" keyName="account" @cancel="yhdcancel"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm" @confirm="yhdconfirm"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				users: '',
				navsList: [],
				tabIndex: 0,
				scrollInto: "",
				remarks: "",
				disableds: false,
				yhremarks: "",
				loading: false,
				currency: 0,
				yhcurrency: 0,
				scrollH: 1200, // 增加高度以容纳出金记录内容
				values: '',
				cjyhvalues: '',
				yhvalues: '',
				hnames: 'USDT',
				dfindex: [0],
				ddfindex: [0],
				yhdfindex: [0],
				coin_trade_fee: 0,
				yhddfindex: [0],
				yhcoin_trade_fee: 0,
				cnumber: 0,
				yhcnumber: '',
				jbnumber: '',
				yhsnumber: 0,
				maxNumber: 30,
				rates: 0,
				yhnames: "",
				shows: false,
				yhshows: false,
				yhdshows: false,
				dshows: false,
				tbvalues: '',
				yhtbvalues: '',
				resData: null,
				walletAddress: [],
				yhwalletAddress: [],
				yhbzid: 0,
				szhbbzid: 0,
				yhcolumns: [],
				columns: [
					['USDT-TRC20', '200', '300']
				],
				tbcolumns: [
					['123456789123456789', '200', '300']
				],
				// 出金记录相关数据 - 与cjrecord页面保持一致
				indexList: [],
				pages: 1,
				status: false,
				loding: "",

				currencyMap: new Map(), // 货币映射表
				bankCurrencyMap: new Map() // 银行货币映射表
			}
		},
		watch: {
			cjyhvalues: function(val) {
				const has = this.yhcolumns[0].find(item => item.name == val.name)
				if (has) {
					this.yhcoin_trade_fee = has.rate
				}
				uni.showLoading({
					title: "",
				})
				const ressCach = this.$H.get('/api/v1/cashInfo', false, {
					digital_bank_id: val.id
				}).then(res => {
					uni.hideLoading();
					var wAddress = []
					var wAddressList = []
					if (res.type == "ok") {
						res.data.map(item => {
							wAddress.push({
								"account": item.bank_account,
								"bank_name": item.bank_name,
								"id": item.id
							})
						})
						wAddressList.push(wAddress)
						this.yhwalletAddress = wAddressList
						if (this.yhwalletAddress.length > 0 && this.yhwalletAddress[0][0]) {
							this.yhbzid = this.yhwalletAddress[0][0].id
							this.yhtbvalues = this.yhwalletAddress[0][0].account
							this.yhnames = this.yhwalletAddress[0][0].bank_name
						}
					}
				})
			},
			yhtbvalues: function(val) {
				const has = this.yhwalletAddress[0].find(item => item.account == val)
				if (has) {
					this.yhbzid = has.id
				}
			},
			yhvalues: function(val) {
				// alert(val)
				// const ressCachs =  this.$H.post('/api/v1/wallet/detail',false,{
				// 	currency:val.currency_id,type:"change"
				// }).then(res => {
				// 	console.log("uuuuucashInfocashInfocashInfocashInfocashInfocashInfocashInfo")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 	console.log("gggggggggggggggggggggggggggggggggggggg")
				// 							console.log(res)
				// 						if(res.type=="ok"){

				// 						this.yhcoin_trade_fee=res.data.coin_trade_fee

				// 						}
				// 			})
			},
			tbvalues: function(val) {
				const has = this.walletAddress[0].find(item => item.account == val)
				if (has) {
					this.szhbbzid = has.id
				}
			},
			values: function(val) {
				const ressCachs = this.$H.get('/api/v1/usdtInfo', false, {
					digital_currency_id: val.id
				}).then(res => {
					if (res.type == "ok") {
						var wAddress = []
						var wAddressList = []
						// 重置地址相关数据
						this.tbvalues = ''
						this.szhbbzid = 0
						if (res.data.length > 0) {
							res.data.map(item => {
								wAddress.push({
									"account": item.account,
									"id": item.id
								})
							})
							wAddressList.push(wAddress)
							this.walletAddress = wAddressList
							// 设置第一个地址为默认选中
							if (this.walletAddress[0] && this.walletAddress[0][0]) {
								this.tbvalues = this.walletAddress[0][0].account
								this.szhbbzid = this.walletAddress[0][0].id
							}
						} else {
							// 没有地址时清空数据
							this.walletAddress = []
							this.tbvalues = ''
							this.szhbbzid = 0
						}
						// 更新汇率
						const has = res.data.find(item => item.digital_currency.agreement == val.name)
						if (has) {
							this.coin_trade_fee = has.digital_currency.rate
						}
					} else {
						// API调用失败时清空地址数据
						this.walletAddress = []
						this.tbvalues = ''
						this.szhbbzid = 0
					}
				}).catch(error => {
					console.error('Failed to get withdrawal address:', error)
					this.walletAddress = []
					this.tbvalues = ''
					this.szhbbzid = 0
				})

				// const walletList =  this.$H.post('/api/v1/userWalletList',false,{
				// 	currency:val.currency_id
				// }).then(res => {
				// 							console.log(res)
				// 						var wAddress=[]
				// 						var wAddressList=[]
				// 						console.log("uuuuugggggggggggggggggggggggggggggggggggggg")
				// 						console.log("gggggggggggggggggggggggggggggggggggggg")

				// 												console.log(res)
				// 										    if(res.type=="ok"){
				// 											   	  res.data.data.map(item=>{
				// 											   		 wAddress.push(item.address)
				// 											   		  })
				// 												wAddressList.push(wAddress)
				// 												this.walletAddress=wAddressList
				// 												this.tbvalues = this.walletAddress[0][0]
				// 											}
				// 			})

			}
		},
		onShow() {


		},
		onLoad() {
			this.users = JSON.parse(uni.getStorageSync('user'))
			//this.tbvalues = this.tbcolumns[0][0]
			// 先加载货币映射数据，再获取其他数据
			this.loadCurrencyMaps().then(() => {
				this.getData()
				this.getRecordData()
			})
			const {
				i18n
			} = this
			this.navsList.push(i18n.bank_card)
			this.navsList.push(i18n.digital_currency)
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F'
				});
			}
			// 设置页面标题
			uni.setNavigationBarTitle({
				title: i18n.withdrawal
			})
		},
		// 页面滚动到底部时触发
		onReachBottom() {
			this.scrolltolower()
		},
		computed: {
			yhrateses() {
				// 实时计算银行卡提现手续费
				if (!this.yhcnumber || !this.cjyhvalues) return 0;
				
				let fee = 0;
				if (this.cjyhvalues.service_charge_type === 1) {
					// 百分比手续费
					fee = this.yhcnumber * (this.cjyhvalues.rate / 100);
				} else {
					// 固定金额手续费
					fee = this.cjyhvalues.rate;
				}
				
				// 计算到账金额：(提币金额 - 手续费) / 汇率
				const afterFee = this.yhcnumber - fee;
				this.yhsnumber = afterFee / (this.cjyhvalues.exchange_rate || 1);
				
				return fee.toFixed(4);
			},
			rateses() {
				let num = this.cnumber * this.coin_trade_fee
				this.jbnumber = this.cnumber * num
				return num.toFixed(4) + ' USD'
			},
			// 格式化银行卡手续费显示
			yhFeeDisplay() {
				if (!this.cjyhvalues) return '';
				if (this.cjyhvalues.service_charge_type === 1) {
					// 百分比类型显示百分比
					return this.cjyhvalues.rate + '%';
				} else {
					// 固定金额类型显示固定值
					return this.cjyhvalues.rate + ' USD';
				}
			},
			i18n() {
				return this.$t("out")
			},
			i18nOther() {
				return this.$t("other")
			},
			...mapState({
				lgStatus: state => state.lgStatus
			})
		},
		methods: {
			// 格式化记录中的手续费显示
			formatRecordFee(orderItem) {
				// 通过tabIndex判断是银行卡提现还是数字货币提现
				if (this.tabIndex === 1) {
					// 银行卡提现：从币种映射中获取配置信息
					const bankConfig = this.bankCurrencyMap.get(orderItem.currency);
					if (bankConfig && bankConfig.service_charge_type === 1) {
						// 百分比类型：显示配置的百分比
						return bankConfig.rate + '%';
					} else {
						// 固定金额类型：显示实际扣除的固定金额
						return orderItem.rate + ' USD';
					}
				} else {
					// 数字货币提现：直接显示手续费加币种
					return orderItem.rate + ' USD';
				}
			},
			setdshows() {
				// 检查是否有提币地址
				if (this.walletAddress.length == 0 || !this.walletAddress[0] || this.walletAddress[0].length == 0) {
					uni.showModal({
						title: this.i18n.prompt,
						content: this.i18n.no_withdrawal_address,
						cancelText: this.i18nOther.cancel,
						confirmText: this.i18nOther.confirm,
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/users/digital/addDigital/addDigital'
								})
							}
						}
					})
					return
				}
				this.dshows = true
			},
			// 银行卡地址检查方法
			setyhdshows() {
				// 检查是否有收款账户
				if (this.yhwalletAddress.length == 0 || !this.yhwalletAddress[0] || this.yhwalletAddress[0].length == 0) {
					uni.showModal({
						title: this.i18n.prompt,
						content: this.i18n.no_recipient_account,
						cancelText: this.i18nOther.cancel,
						confirmText: this.i18nOther.confirm,
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/users/digital/addDigital/addDigital'
								})
							}
						}
					})
					return
				}
				this.yhdshows = true
			},
			yhconfirm(e) {
				const {
					columnIndex,
					value,
					yhvalues, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.yhuPicker
				} = e
				this.cjyhvalues = value[0]
				// 不在这里设置yhcurrency，让yhtsubmit方法直接使用this.cjyhvalues.id
				this.yhshows = false
			},
			yhcancel(e) {
				this.yhshows = false
			},
			confirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				this.values = value[0]
				// 不在这里设置currency，让tsubmit方法直接使用this.values.id
				this.shows = false
			},
			cancel(e) {
				this.shows = false
			},
			dconfirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.duPicker
				} = e


				this.tbvalues = value[0].account;
				//this.multiple0 = value[0];
				this.dshows = false
			},
			dcancel(e) {
				this.dshows = false
			},
			yhdconfirm(e) {
				const {
					columnIndex,
					value,
					yhtbvalues, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.yhduPicker
				} = e

				//this.multiple0 = value[0];
				this.yhtbvalues = value[0].account
				this.yhnames = value[0].bank_name
				this.yhdshows = false
			},
			yhdcancel(e) {
				this.yhdshows = false

			},
			tsubmit() {
				// 验证提币数量
				if (!this.cnumber || this.cnumber <= 0) {
					uni.$u.toast(this.i18n.please_enter_amount);
					return false
				}

				// 验证是否选择了货币
				if (!this.values || !this.values.id) {
					uni.$u.toast(this.i18n.please_select_currency);
					return false
				}

				// 验证提币地址
				if (!this.tbvalues) {
					uni.$u.toast(this.i18n.please_select_address);
					return false
				}

				this.loading = true
				this.disableds = true

				// 使用当前选中的货币ID，而不是固定的1
				let rates = this.cnumber * this.coin_trade_fee
				const res = this.$H.post('/api/v1/postWalletOut', false, {
					money: this.values.name, // 使用当前选中的货币名称
					type: 0,
					currency: this.values.id, // 使用当前选中的货币ID
					number: this.cnumber,
					rate: rates,
					address: this.tbvalues,
					remark: this.remarks,
					wallet_id: this.szhbbzid
				}).then(res => {
					this.loading = false
					this.disableds = false
					if (res.type == "ok") {
						uni.$u.toast(res.message);
						this.cnumber = 0
						this.remarks = ""
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/users/users'
							})
						}, 1000)
					} else {
						uni.$u.toast(res.message || this.i18n.submit_failed);
					}
				}).catch(error => {
					this.loading = false
					this.disableds = false
					uni.$u.toast(this.i18n.network_error);
				})
			},
			yhtsubmit() {
				// 验证提币数量
				if (!this.yhcnumber || this.yhcnumber <= 0) {
					uni.$u.toast(this.i18n.please_enter_amount);
					return false
				}

				// 验证是否选择了货币
				if (!this.cjyhvalues || !this.cjyhvalues.id) {
					uni.$u.toast(this.i18n.please_select_currency);
					return false
				}

				// 验证提币地址
				if (!this.yhtbvalues) {
					uni.$u.toast(this.i18n.please_select_account);
					return false
				}

				this.loading = true
				this.disableds = true

				let rates = this.yhcnumber * this.yhcoin_trade_fee
				const res = this.$H.post('/api/v1/postWalletOutBank', false, {
					digital_bank_id: this.cjyhvalues.id, // 使用当前选中的银行货币ID
					wallet_id: this.yhbzid,
					number: this.yhcnumber,
					remark: this.yhremarks
				}).then(res => {
					this.loading = false
					this.disableds = false
					if (res.type == "ok") {
						uni.$u.toast(res.message);
						this.yhcnumber = 0
						this.yhremarks = ""
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/users/users'
							})
						}, 1000)
					} else {
						uni.$u.toast(res.message || this.i18n.submit_failed);
					}
				}).catch(error => {
					this.loading = false
					this.disableds = false
					uni.$u.toast(this.i18n.network_error);
				})
			},
			getData() {
				const res1 = this.$H.get('/api/v1/extractBank', false).then(res => {
					if (res.type == "ok") {
						var currency = []
						var currencyList = []
						res.data.map(item => {
							currency.push({
								"name": item.name,
								"id": item.id,
								"rate": item.rate,
								"exchange_rate": item.exchange_rate,
								"service_charge_type": item.service_charge_type,
								"min_number": item.min_number,
								"max_number": item.max_number,
								"rate_display": item.rate_display
							})
						})
						currencyList.push(currency)
						this.yhcolumns = currencyList
						this.cjyhvalues = this.yhcolumns[0][0]
						this.yhcoin_trade_fee = this.cjyhvalues.rate
					}
				})

				const res = this.$H.get('/api/v1/extractCurrency', false).then(res => {
					if (res.type == "ok") {
						var currency = []
						var currencyList = []
						res.data.map(item => {
							currency.push({
								"name": item.agreement,
								"id": item.id

							})
						})
						// currency.push(
						// {
						// 	"name":'USDT-TRC20',
						// 	"id":res.data.trc20
						// }
						// )
						currencyList.push(currency)
						// this.yhcolumns=currencyList
						this.columns = currencyList
						console.log(this.columns)
						this.values = this.columns[0][0]
						this.currency = this.columns[0][0].id
						this.values = this.columns[0][0]
						this.yhvalues = this.columns[0][0]
						this.currency = this.columns[0][0].id
						this.yhcurrency = this.columns[0][0].id
					}
				})

				// const res =  this.$H.get('/api/v1/quotation_new',false).then(res => {
				// 							console.log(res)
				// 					    if(res.type=="ok"){
				// 							var currency=[]
				// 							var currencyList=[]
				// 						    this.resData =  res.data[0].quotation
				// 								  res.data[0].quotation.map(item=>{
				// 									 currency.push(
				// 									 {
				// 										 "name":item.currency_name+"-"+item.legal_name,
				// 										 "id":item.id,
				// 										 "currency_id":item.currency_id
				// 									 }
				// 									 )
				// 									  })
				//                                   currencyList.push(currency)
				// 							   this.yhcolumns=currencyList
				// 							   this.columns=currencyList
				// 							console.log(this.columns)
				// 							this.values = this.columns[0][0]
				// 							this.yhvalues = this.columns[0][0]
				// 							this.currency=this.columns[0][0].id
				// 						    this.yhcurrency=this.columns[0][0].id
				// 						}
				// 			})

				// const ress =  this.$H.post('/api/v1/userWalletList',false).then(res => {

				// 	var wAddress=[]
				// 	var wAddressList=[]
				// 							console.log(res)
				// 					    if(res.type=="ok"){
				// 						   	  res.data.data.map(item=>{
				// 						   		 wAddress.push(item.address)

				// 						   		  })
				// 							wAddressList.push(wAddress)
				// 							this.walletAddress=wAddressList
				// 							this.tbvalues = this.walletAddress[0][0]
				// 						}
				// 			})

				const ressCach = this.$H.get('/api/v1/cashInfo', false, {
					money: this.cjyhvalues.name
				}).then(res => {
					var wAddress = []
					var wAddressList = []
					if (res.type == "ok") {
						res.data.map(item => {
							wAddress.push({
								"account": item.bank_account,
								"bank_name": item.bank_name
							})
						})
						wAddressList.push(wAddress)
						this.yhwalletAddress = wAddressList
						if (this.yhwalletAddress.length > 0 && this.yhwalletAddress[0][0]) {
							if (this.yhwalletAddress[0][0] && this.yhwalletAddress[0][0].account) {
								this.yhtbvalues = this.yhwalletAddress[0][0].account
							} else {
								console.error('Account is undefined');
							}
							this.yhnames = this.yhwalletAddress[0][0].bank_name
						}
					}
				})
			},
			changesTabs(id) {
				if (this.tabIndex === id) {
					return;
				}
				this.tabIndex = id;
				this.scrollInto = 'tab' + id;

				// 切换tab时重新加载出金记录
				this.indexList = []; // 清空当前记录
				this.pages = 1; // 重置页码
				this.getRecordData(); // 重新加载数据
			},
			// 加载货币映射数据
			async loadCurrencyMaps() {
				try {
					// 加载数字货币映射
					const digitalRes = await this.$H.get('/api/v1/extractCurrency', false)
					if (digitalRes.type === "ok") {
						digitalRes.data.forEach(item => {
							this.currencyMap.set(item.id, item.agreement)
						})
					}

					// 加载银行货币映射
					const bankRes = await this.$H.get('/api/v1/extractBank', false)
					if (bankRes.type === "ok") {
						bankRes.data.forEach(item => {
							this.bankCurrencyMap.set(item.id, item.name)
						})
					}
				} catch (error) {
					console.error('加载货币映射失败:', error)
				}
			},

			// 根据currency字段获取货币名称
			getCurrencyName(currencyId, currencyName) {
				// 如果已经有currency_name，优先使用
				if (currencyName && currencyName !== '') {
					return currencyName
				}

				// 尝试从数字货币映射中获取
				if (this.currencyMap.has(currencyId)) {
					return this.currencyMap.get(currencyId)
				}

				// 尝试从银行货币映射中获取
				if (this.bankCurrencyMap.has(currencyId)) {
					return this.bankCurrencyMap.get(currencyId)
				}

				// 如果都没找到，返回默认值
				return currencyId ? `${currencyId}` : 'Unknown'
			},

			// 获取出金记录 - 根据当前tab选择不同接口
			getRecordData() {
				this.status = true

				// 根据当前选中的tab决定调用哪个接口
				const isBankTab = this.tabIndex === 0; // 0: 银行卡, 1: 数字货币
				const apiUrl = isBankTab ? '/api/v1/withdrawListBank' : '/api/v1/withdrawList';

				console.log('getRecordData - tabIndex:', this.tabIndex, 'isBankTab:', isBankTab, 'apiUrl:', apiUrl);

				const res = this.$H.post(apiUrl, false, {
					page: this.pages
				}).then(res => {
					if (res.type == "ok") {
						var arr = []

						if (res.data.data.length <= 0) {
							this.status = false
							this.loding = ""
							this.pages -= 1
							return
						}

						res.data.data.map(item => {
							// 格式化数字
							// item.number = parseFloat(item.number).toFixed(2)
							// item.real_number = parseFloat(item.real_number).toFixed(2)
							// item.rate = parseFloat(item.rate).toFixed(2)

							if (isBankTab) {
								// 银行卡记录：添加银行信息显示
								item.bank_info = `${item.bank_name || ''} ${item.address || ''}`.trim();
								item.payee_info = item.real_name || '';
								// 银行卡记录的currency字段已经是货币名称
							} else {
								// 数字货币记录：根据currency字段设置正确的货币名称
								item.currency = this.getCurrencyName(item.currency, item.currency_name)
							}

							arr.push(item)
						})

						if (this.indexList.length > 0) {
							this.indexList = [...this.indexList, ...arr]
						} else {
							this.indexList = arr
						}

						this.status = false
					} else {
						this.status = false
					}
				}).catch(error => {
					this.status = false
					console.error('获取出金记录失败:', error)
				})
			},

			// 滚动到底部加载更多 - 与cjrecord页面完全一致
			scrolltolower() {
				if (this.status) return false
				this.pages += 1
				this.loding = "..........."
				this.getRecordData()
			}


		}
	}
</script>

<style scoped lang="scss">
	.bnsss /deep/.uni-picker-view-mask {
		background: inherit;
	}

	/deep/ .u-radio__icon-wrap--circle {
		width: 16px !important;
		height: 16px !important;
	}

	/deep/ .u-radio__text {
		font-size: 16px !important;
	}

	.bnsss /deep/.u-picker__view__column__item {
		color: #fff;
	}

	.bnsss /deep/.u-popup__content {
		border-radius: 10px;
		background: #222;
	}

	.wnsss /deep/.u-popup__content {
		border-radius: 10px;
	}

	.scroll-view {
		width: 95% !important;
		height: 85rpx;
		line-height: 85rpx;
		white-space: nowrap;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 148px;
	}

	uni-button {
		line-height: 30px !important;
		color: #FFFFFF !important;
		font-size: 14px !important;
	}

	.scroll-view .scroll-items {
		display: inline-block;
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #0166fc !important;
		border-radius: 6px;
		color: #ffffff !important;
		font-weight: bold;
		font-size: 14px;
	}

	.scroll-view .scroll-item {
		display: inline-block;
		width: 100%;
		line-height: 85rpx;
		height: 85rpx;
		text-align: center;
		border-radius: 148px;
		color: #626779;
		font-size: 14px;
	}

	.section-area ::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	.blacks {
		background: #22252F;
	}

	.whiles {
		background: #fff;
	}

	/deep/.uni-scroll-view-content {
		overflow: hidden !important;
	}

	.blacks /deep/.u-border {
		border-color: #333748 !important;
	}

	.blacks /deep/.uni-input-input {
		color: #fff;
		padding: 0 !important;
	}

	.blacks .u-input[data-v-fdbb9fe6] {
		background-color: #ffffff;
	}

	.blacks .u-input {
		background-color: #222 !important;
		color: #222 !important;
	}

	.blacks .u-textarea {
		background: #222;
		color: #fff;
	}

	.blacks /deep/.u-textarea__field {
		color: #fff;
	}

	page {
		position: inherit;
		overflow: initial;
	}

	/* 提现状态文字样式 */
	.status-text {
		font-size: 14px !important;
		margin-top: 10px !important;
	}

	.status-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center !important;
		text-align: center !important;
		flex: 1;
	}

	.record-row {
		display: flex;
		width: 100%;
		margin-top: 12px !important;
		font-size: 12px;
	}

	/* 卡片样式 */
	.record-card {
		width: 96%;
		margin: 0 auto;
		margin-top: 15px;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.record-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	}

	/* 黑色主题卡片样式 */
	.blacks .record-card {
		background: #22252F;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
		border: 1px solid #333748;
	}

	.blacks .record-card:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
	}

	/* 白色主题卡片样式 */
	.whiles .record-card {
		background: #FFFFFF;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		border: 1px solid #F0F0F0;
	}

	.whiles .record-card:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
	}

	/* 状态标题文字样式 - 控制为2行显示 */
	.status-header {
		font-size: 13px !important;
		line-height: 1.2 !important;
		text-align: center !important;
		word-break: break-word;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		max-height: 32px;
		width: 100%;
	}

	/* 状态容器样式优化 */
	.record-card .status-column {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		flex: 1;
		min-height: 35px;
	}

	.record-card .status-column text {
		font-size: 13px !important;
		line-height: 1.3 !important;
		text-align: center !important;
		word-break: break-word;
		max-width: 100%;
		white-space: pre-wrap;
	}

	/* 通用状态列样式 */
	.record-card view[style*="flex: 1"] {
		min-height: 35px;
	}

	.record-card view[style*="flex: 1"] text {
		font-size: 12px !important;
		line-height: 1.4 !important;
		text-align: center !important;
		word-break: break-word;
		max-width: 150px;
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
</style>