<template>
	<view style="" :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<scroll-view scroll-y class="DrawerPage" :class="modalName=='viewModal'?'show':''">

			<view @click="goArticle()"
				style="height: 23vh;  width: 100%; z-index: 9999; position: relative;  background: #0361ec; margin: 0 auto; display: flex; flex-direction: column; padding-top: 15px;padding-bottom: 15px;  align-items: center;justify-content: space-around; ">
				<view style="margin-left: 20px;display: flex;flex-direction: column;
    justify-content: center; width: 95%; height: 11px; ">
					<!-- 历史记录 -->
					<text style="margin-top: 30rpx; font-weight: bold;color: #ffffff;font-size: 40rpx;"
						v-if="!VirtualAccounts">{{users.account_number}}</text>
					<text style="margin-top: 30rpx; font-weight: bold;color: #ffffff;font-size: 40rpx;"
						v-if="VirtualAccounts">{{i18n.mock_account}}</text>
					<text style="color: #ffffff; font-size: 12px; color: #d9d9d9;">{{users.id}}</text>
					<!-- <text
						style="font-size: 13px;color: #ffffff; flex: 1; text-align: right;"
						@click="goArticle()">{{i18n.financial_records}}</text> -->
					<!-- <image src="../../static/arrow-right-1.png" style="width: 16px; height: 14px; margin-left: 3px;">
					</image> -->
				</view>

				<view style="display: flex; width: 100%; margin-top: 12px;">
					<view @click="goArticle()"
						style="margin-left: 20px; display: flex; flex: 1; color: #ffffff; border: 0px #fff;flex-direction: column; ">
						<view style="display: flex;justify-content: space-between;">
							<text style="color: #ffffff; font-size: 50rpx;"
								v-if="hideProperty">${{users.usdt.toFixed(2)}} </text>
							<text style="color: #ffffff; font-size: 50rpx;" v-if="!hideProperty">$*****</text>
							<view style="color: #ffffff; font-size: 50rpx;" @click="onHideProperty()"><img
									style="width: 60rpx;margin-right: 20px;" src="../../static/open.png" alt="" />
							</view>
						</view>
						<text style="color: #ffffff; font-size: 12px;">{{i18n.balance}}</text>
						<text style="color: #ffffff;margin-top: 2px;" v-if="users.simulation != 1">{{i18n.creditscore}}：{{users.score}}</text>
					</view>
					<view style="width: 2px;  background: #F4F4F4; height: 40px;"></view>
					<view
						style="display: flex;display: none;flex: 1; flex-direction: column; justify-content: center; align-items: center;">
						<text style="color: #7B7B7B; font-size: 14px;">{{i18n.net_worth}}$</text>
						<text>1234567.78</text>
					</view>
				</view>
				<view
					style="display: flex;display: none;  width: 100%; margin-top: 15px; padding-top: 10px; padding-bottom: 10px;">
					<view
						style="display: flex;  flex: 1; flex-direction: column; justify-content: center; align-items: center; border: 0px #fff;">
						<text style="color: #7B7B7B; font-size: 14px;">{{i18n.used_margin}}$</text>
						<text>1234567.78</text>
					</view>
					<view style="width: 2px;  background: #F4F4F4; height: 30px;margin-left: 9px;margin-right: 9px;">
					</view>
					<view
						style="display: flex;flex: 1;  flex-direction: column; justify-content: center; align-items: center;">
						<text style="color: #7B7B7B; font-size: 14px;">可用保证金$</text>
						<text>1234567.78</text>
					</view>
					<view style="width: 2px;  background: #F4F4F4; height: 30px;margin-left: 9px;margin-right: 9px;">
					</view>
					<view
						style="display: flex;flex: 1; flex-direction: column; justify-content: center; align-items: center;">
						<text style="color: #7B7B7B; font-size: 14px;">保证金比例$</text>
						<text>1234567.78</text>
					</view>
				</view>
			</view>
			<!-- 	<view style="height: 35px;"></view> -->
			<!-- 	<view
				style="width: 90%; z-index: 9999; position: relative;  background: #fff; margin: 0 auto; display: flex; flex-direction: column; padding-top: 15px;padding-bottom: 15px; border-radius: 10px;  align-items: center; ">


				<view></view>
			</view> -->
			<view
				style="width: 90%; margin: 0 auto; display: flex; margin-top: 15px;  align-items: center; background: initial !important; ">
				<view @click="goamount()"
					:style="$store.state.bgColor=='black'?'display: flex;  width: 50%; justify-content:flex-start; align-items: center;border: 2px solid #C8E5FF; background: #DBF4FF; border-radius: 10px; padding: 1px; height: 70px;background: #3D50844D !important; border: 1px solid #29455EFF !important;':'display: flex;  width: 50%; justify-content:  flex-start; align-items: center;background: #DBF4FF;background-color: #0166fc; border-radius: 10px; padding: 1px; height: 55px;'">
					<view :style="$store.state.bgColor=='black'?'background:initial !important;':''">
						<image src="../../static/<EMAIL>" style="width: 60px; height: 60px;"></image>
					</view>
					<view
						:style="$store.state.bgColor=='black'?'display: flex;width: 50%; font-size: 15px; margin-bottom: 5px;  flex-direction: column; justify-content: space-between; height: 45px;background:initial !important;':'display: flex; font-size: 15px; margin-bottom: 5px; flex-direction: column; justify-content: space-between; height: 45px;'">
						<text
							style="color: #ffffff; font-weight: bold;font-size: 15px;">{{i18n.cash_deposit}}</text><text
							style="color: #d4d4d4; font-size: 12px;">{{i18n.billing_details}}</text>
					</view>
				</view>
				<view style="width: 15px;"></view>
				<view @click="goWithdraw()"
					:style="$store.state.bgColor=='black'?'display: flex;width: 50%; justify-content:  flex-start; align-items: center;border: 2px solid #DEE0FF;border-radius: 10px; padding: 1px;  height: 70px;background: #3F47F433 !important; border: 1px solid #313481FF !important;':'display: flex;width: 50%; justify-content:  flex-start; align-items: center;background: #E8EDFF;background-color: #f23c48;border-radius: 10px; padding: 1px;  height: 55px;'">
					<view :style="$store.state.bgColor=='black'?'background:initial !important;':''">
						<image src="../../static/<EMAIL>" style="width: 60px; height: 60px;"></image>
					</view>
					<view
						:style="$store.state.bgColor=='black'?'display: flex;width: 50%; font-size: 15px; margin-bottom: 5px; margin-left: 1px; flex-direction: column; justify-content: space-between; height: 45px; width: 50%; flex:1; background:initial !important;':'display: flex; font-size: 15px; margin-bottom: 5px;  flex-direction: column; justify-content: space-between; height: 45px;'">
						<text style="font-weight: bold;font-size: 15px;color: #ffffff;">{{i18n.cash_out}}</text><text
							style="color: #faaeb3; font-size: 12px;">{{i18n.billing_details}}</text>
					</view>
				</view>
			</view>
			<view v-if="$store.state.bgColor=='while'" style="height: 15px;"></view>
			<view v-if="$store.state.bgColor=='black'" style="height: 15px; background: #1A1C24 !important;"></view>
			<view
				:style="$store.state.bgColor=='black'?'background: #22252F !important; width: 90%;  margin: 0 auto; border-radius: 10px;':' width: 90%;  margin: 0 auto; border-radius: 10px; '">
				<uni-list-item :thumb="$store.state.bgColor=='black'?'../../static/u1.png':'../../static/<EMAIL>'"
					link :title="wallet" @click="gowallet()" to=""></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u2.png'"
					@click="goauthen()" clickable link :title="real_name_authentication"></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u3.png'"
					@click="goinvite()" link :title="invite_friends"></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u4.png'"
					@click="gouppwd()" link :title="change_password"></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u5.png'"
					@click="gocomplaint()" link :title="complaint_email"></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u6.png'"
					@click="gocontact()" link :title="contact_customer_service"></uni-list-item>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u7.png'"
					@click="gonotice()" link :title="announcement"></uni-list-item>
			</view>
			<view v-if="$store.state.bgColor=='while'" style="height: 15px;"></view>
			<view v-if="$store.state.bgColor=='black'" style="height: 15px; background: #1A1C24 !important;"></view>
			<view
				:style="$store.state.bgColor=='black'?' width: 90%; margin: 0 auto; border-radius: 10px;':'border: 1px solid #E8EBF3; width: 90%; margin: 0 auto; border-radius: 10px;' ">
				<view @click="showModal" data-target="viewModal"><uni-list-item
						:thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u8.png'"
						:title="languages" link>
					</uni-list-item>
				</view>
				<uni-list-item :thumb="$store.state.bgColor=='while'?'../../static/<EMAIL>':'../../static/u10.png'"
					@click="goothers()" link :title="other"></uni-list-item>

			</view>
			<view v-if="$store.state.bgColor=='black'" style="height: 15%; background: #1A1C24 !important;"></view>
			<view v-if="$store.state.bgColor=='while'" style="height: 30%;">
			</view>

		</scroll-view>

		<view class="DrawerClose" :class="modalName=='viewModal'?'show':''" @tap="hideModal">
			<text class="cuIcon-pullright"></text>
		</view>

		<tab-bar></tab-bar>
	</view>


</template>

<script>
	import {
		langs
	} from "@/common/language.js"
	import {
		mapState
	} from 'vuex'

	export default {
		data() {
			return {
				modalName: null,
				wallet: '',
				switchChecked: uni.getStorageSync('bgColor') || false,
				real_name_authentication: '',
				invite_friends: '',
				change_password: '',
				complaint_email: '',
				contact_customer_service: '',
				announcement: '',
				languages: '',
				style: '',
				other: '',
				darkness: '',
				VirtualAccounts: '',
				light: '',
				clangs: {},
				hideProperty: true,
				value6: 0,



				language: [
					// {
					// "name": "English",
					// "img": "../../static/l1.png"
					//    },
					// {
					// "name": "日本語",
					// "img": "../../static/l2.png"
					// },
					// {
					// "name": "한국어",
					// "img": "../../static/l3.png"
					// },
					// {
					// "name": "繁体中文",
					// "img": "../../static/l4.png"
					// },
					// {
					// "name": "ไทย",
					// "img": "../../static/l5.png"
					// },
					// {
					// "name": "Tiếng Việt",
					// "img": "../../static/l6.png"
					// },
					// {
					// "name": "français",
					// "img": "../../static/l7.png"
					// },
					// {
					// "name": "Deutsche",
					// "img": "../../static/l8.png"
					// },
					// {
					// "name": "Русский язык",
					// "img": "../../static/l9.png"
					// },
					// {
					// "name": "Español",
					// "img": "../../static/l10.png"
					// },
					// {
					// "name": "Português",
					// "img": "../../static/l11.png"
					// },
					// {
					// "name": "Italiano",
					// "img": "../../static/l12.png"
					// },
					// {
					// "name": "عربي",
					// "img": "../../static/l13.png"
					// },
					// {
					// "name": "Türkçe",
					// "img": "../../static/l14.png"
					// }
				]
			};
		},
		onHide() {
			// window.addEventListener("popstate", function(){

			//       uni.switchTab({

			//         url: '/pages/users/users'

			//       });

			//     }, false)
		},
		onUnload() {
			// window.removeEventListener("popstate", this.show11, false);
		},
		onBackPress(e) {

		},

		onLoad() {
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
			this.setConfig()
			this.language = langs
			// if (window.history && window.history.pushState) {

			// 	   alert('gheree')
			// 				history.pushState(null, null, document.URL);
			// 				// window.addEventListener("popstate", this.show, false);
			// }

		},
		onShow() {
			getApp().setUser()
			this.$store.commit('changeTabbarIndex', 4)
			this.auths(() => {})
			this.setLgs()
			this.setConfig()
		},
		computed: {
			i18n() {
				let VirtualAccount = localStorage.getItem('VirtualAccount');
				if (VirtualAccount) {
					this.VirtualAccounts = VirtualAccount
				}
				return this.$t("mine")
			},
			...mapState({
				lgStatus: state => state.lgStatus,
				users: state => state.users,

			}),



		},
		methods: {
			show11() {
				// alert('hhg')
				// uni.navigateBack({
				//     delta: 1  // 返回上一页面
				//   })
			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})

			},
			setConfig() {
				const i18n = this.$t("mine")

				this.wallet = i18n.wallet
				this.real_name_authentication = i18n.real_name_authentication
				this.invite_friends = i18n.invite_friends
				this.change_password = i18n.change_password
				this.complaint_email = i18n.complaint_email
				this.contact_customer_service = i18n.contact_customer_service
				this.announcement = i18n.announcement
				this.languages = i18n.language
				this.style = i18n.style
				this.other = i18n.other
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})
				console.log(langs)
				this.language = langs
				this._i18n.locale = item.value
				console.log(item.value)
				uni.setStorageSync('lang', item.value)
				this.setConfig()
				this.$utils.setTabbar(this)

				this.clangs = item
				this.hideModal()
				// this.$store.commit('setLang', item.value)

				// setTimeout(() => {
				// 	this.showLanguage = false
				// }, 200)
			},
			showsLan() {
				console.log(this.$refs['openLanges'].$el)
				let fileInput = this.$refs.openLanges
				setTimeout(function() {
					fileInput.click()
				}, 10)
				// this.$refs.openLanges.$el.click()
			},
			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
			},
			hideModal(e) {
				this.modalName = null
				uni.showTabBar()
			},
			disconnected() {
				console.log('disconnected');
			},
			connected() {
				console.log('connected');
			},
			// 隐藏资产
			onHideProperty() {
				event.stopPropagation(); // 阻止事件冒泡到父级元素
				this.hideProperty = !this.hideProperty;
			},
			retry() {
				console.log('retry');
			},
			goArticle() {
				uni.navigateTo({
					url: "/pages/users/financial/financial"
				})
			},
			goWithdraw() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				this.$H.get('/api/v1/cashInfo', false).then(res => {
					if (res.type == "ok") {
						if(res.data.length > 0){
							uni.navigateTo({
								url: "/pages/users/withdraw/withdraw"
							})
						}else{
							uni.navigateTo({
								url: "/pages/users/banks/addBanks/addBanks"
							})
						}
					}
				})
			},
			goothers() {
				uni.navigateTo({
					url: "/pages/users/others/others"
				})
			},
			goamount() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				console.log(412)
				uni.navigateTo({
					url: "/pages/users/amount/amount"
				})
			},
			goauthen() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				uni.navigateTo({
					url: "/pages/users/authen/authen"
				})
			},
			gowallet() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				uni.navigateTo({
					url: "/pages/users/wallet/wallet"
				})
			},
			gouppwd() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				uni.navigateTo({
					url: "/pages/users/uppwd/uppwd"
				})
			},
			gocontact() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				window.open('https://go.crisp.chat/chat/embed/?website_id=bcff0798-7842-4743-9101-a1ff72296654')
				// uni.navigateTo({
				// 	url: "/pages/users/contact/contact"
				// })
			},
			gocomplaint() {
				uni.navigateTo({
					url: "complaint/complaint"
				})
			},

			gonotice() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				uni.navigateTo({
					url: "/pages/users/notice/notice"
				})
			},
			goinvite() {
				if(this.users.simulation == 1){
					uni.showToast({
						title: 'Please register or log in to your real account.',
						icon:'none',
						duration: 2000
					});
					return
				}
				uni.navigateTo({
					url: "/pages/users/invite/invite"
				})
			}
		},
	}
</script>

<style>
	.uni-list-item {
		border-radius: 10px;
	}

	/deep/.uni-list-item__container {

		height: 30px;
	}

	.blacks /deep/.uni-list-item__extra-text {
		color: #fff !important;
	}

	page {
		/* background-color: #F7F8FA; */
		width: 100vw;

	}

	.blacks /deep/.uni-scroll-view {
		background: #1A1C24 !important;
	}

	.blacks /deep/.uni-scroll-view-content uni-view {
		background: #22252F !important;

	}

	.blacks /deep/.uni-list-item {
		position: initial;
		border-bottom: 1px solid #1A1C24 !important;
		border-radius: 10px 10px 0 10px;
	}

	.blacks {
		color: #fff !important;
	}

	.whiles /deep/.uni-scroll-view {}

	.whiles /deep/.uni-scroll-view-content uni-view,
	uni-image {}

	.whiles /deep/.uni-list-item {
		margin-bottom: 8px;
		height: 100rpx;
	}

	.blacks /deep/.uni-list-item__content-title {
		color: #fff;
	}

	.blacks /deep/uni-switch .uni-switch-input {
		background: #424656;
	}

	.DrawerPage {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		background-color: #F7F8FA;
		transition: all;
	}

	.DrawerPage.show {
		transform: scale(0.9, 1);
		left: 65vw;
		box-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);
		/* transform-origin: 0; */
	}

	.DrawerWindow {
		position: absolute;
		width: 65vw;
		height: 100vh;
		left: 0;
		top: 0;
		transform: scale(0.5, 0.5) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.6s;
		padding: 100upx 0;
	}

	.DrawerWindow.show {
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	.DrawerClose {
		position: absolute;
		width: 65vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30upx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50upx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.6s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 35vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.cuIcon {
		width: 64upx;
		height: 64upx;
		line-height: 64upx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10upx;
		height: 10upx;
		background-color: currentColor;
		position: absolute;
		bottom: 10upx;
		border-radius: 10upx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}

	.blacks .cu-list.menu-avatar>.cu-item:after,
	.cu-list.menu>.cu-item:after {

		border-bottom: 1px solid #1A1C24;

	}

	.whiles .cu-list.menu-avatar>.cu-item:after,
	.cu-list.menu>.cu-item:after {

		border-bottom: 1px solid #ddd;

	}

	.DrawerWindow view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	.DrawerClose view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	view,
	image {
		box-sizing: border-box;
	}

	/*#ifdef MP*/
	view,
	image {
		box-sizing: content-box !important;
	}

	/*#endif*/
	.uni-scroll-view-content view,
	image {
		box-sizing: content-box !important;
	}

	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}
</style>
