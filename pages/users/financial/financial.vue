<template>
	<view style="" :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<u-list class="sfds" style="   width: 100%; margin-top: 10px;   font-weight: bold;"
			@scrolltolower="scrolltolower">
			<u-list-item style=" padding-right: 20px; " v-for="(orderItem, index2) in indexList" :key="index2">
				<u-cell>
					<view slot="title" style="display: flex; flex-direction: column;  padding: 10px 0;  ">
						<view style="display: flex; ">
							<text class="u-cell-text"
								style="flex: 1; justify-content: center; text-align: left; padding-left: 12px; font-size: 12px;"><text
									style="font-weight:1; margin-right: 10px; color: #AFAFAF;">{{i18n.quantity}}</text>{{orderItem.value}}</text>
						</view>
						<view
							style="display: flex;font-size: 12px;padding-left: 12px;  text-align: left; margin-top: 15px;">
							<text class="u-cell-text"
								style="display: block; margin-right: 10px;color: #AFAFAF;">{{i18n.time}}</text><text
								style="">{{orderItem.created_time}}</text>
						</view>
						<view
							style="display: flex;font-size: 12px;padding-left: 12px;  text-align: left; margin-top: 15px;">
							<text class="u-cell-text" style="font-size: 12px;"><text
									style="color: #AFAFAF;margin-right: 10px; ">{{i18n1.profit_Loss}}</text>{{orderItem.transactions ? orderItem.transactions.fact_profits:''}}</text>
						</view>
						<view
							style="display: flex;font-size: 12px;padding-left: 12px;  text-align: left; margin-top: 15px;">
							<text class="u-cell-text" style="font-size: 12px;"><text
									style="color: #AFAFAF;margin-right: 10px; ">{{i18n1.deposit}}</text>{{orderItem.transactions ? orderItem.transactions.caution_money:''}}</text>
						</view>
						<view
							style="display: flex;font-size: 12px;padding-left: 12px;  text-align: left; margin-top: 15px;">
							<text class="u-cell-text" style="font-size: 12px;"><text
									style="color: #AFAFAF;margin-right: 10px; ">{{i18n.record}}</text>{{orderItem.info}}</text>
						</view>
					</view>
				</u-cell>
			</u-list-item>
			<view
				style=" width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #AFAFAF;">
				<text>{{loding}}</text>
			</view>
		</u-list>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				indexList: [],
				pages: 1,
				status: false,
				loding: ""
			}
		},
		onLoad() {
			this.getData()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("records.financial_records")
			})

			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F' // 导航栏背景颜色
				});
			}

		},
		computed: {
			i18n() {
				return this.$t("records")
			},
			i18n1() {
				return this.$t("order")
			},
		},
		methods: {
			scrolltolower() {

				if (this.status) return false
				this.pages += 1
				this.loding = "..........."
				this.getData()
			},
			getData() {
				this.status = true
				var arr = []
				const res = this.$H.post('/api/v1/wallet/legalLog', false, {
					page: this.pages
				}).then(res => {
					// this.loading=false
					if (res.type == "ok") {
						// 检查数据结构 - 尝试多种可能的路径
						let dataList = [];
						if (res.data && res.data.list) {
							dataList = res.data.list;
						} else if (res.data && res.data.data && res.data.data.list) {
							dataList = res.data.data.list;
						}
						console.log('dataList:', dataList)
						if (dataList.length <= 0) {
							this.status = false
							this.loding = ""
							this.pages -= 1
							return
						}
						dataList.map(item => {
							// 后端已经通过getCreatedTimeAttribute()方法格式化了时间，不需要前端再转换
							arr.push(item)
						})
						if (this.indexList.length > 0) {
							this.indexList = [...this.indexList, ...arr]
						} else {
							this.indexList = arr
						}
						this.status = false
					} else {

					}
				})





				// 	var arr = []
				// 	for (let i = 0; i < 10; i++) {
				// 		let obj = {
				// 			count: '11111111',
				// 			jl: '链上充值到账',
				// 			date: "2023-07-20 08:30"
				// 		}
				// 		arr.push(obj);

				// 	}
				// 	this.indexList = arr
			}
		}
	}
</script>

<style>
	page {}

	.whiles /deep/.uni-scroll-view,
	.uni-scroll-view-content {
		/* background-color: #F7F8FA; */
	}

	.blacks /deep/.uni-scroll-view,
	.uni-scroll-view-content {
		color: #fff !important;
	}

	.blacks /deep/.u-line[data-v-e778bab2] {
		margin: 0 auto !important;
		width: 90% !important;
		border-bottom: 1px solid #1A1C24 !important;
	}

	.whiles /deep/.u-line[data-v-e778bab2] {
		margin: 0 auto !important;
		width: 90% !important;
	}

	.uni-nav-bar {
		background-color: #222;
	}

	.blacks /deep/.u-cell__body {
		color: #fff !important;
	}

	.whiles /deep/.u-cell__body {}

	.blacks {
		background: #1A1C24;
	}
</style>
