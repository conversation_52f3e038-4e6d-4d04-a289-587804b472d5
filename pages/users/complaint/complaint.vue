<template>
	<view>


		<view>
			<view :style="$store.state.bgColor=='black'?'height: 10px;background: #1A1C24;':'height: 10px;'"></view>
			<view
				:style="$store.state.bgColor=='black'?'bheight: 430px;background: #22252F;color: #ffffff;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;':'bheight: 430px;background: #FFFFFF;;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;' ">

				<view
					style="display: flex;flex-direction: column;
			width: 90%; justify-content: center;align-items: center; height: auto;  margin: 0 auto;  margin-top: 15px; padding-bottom: 13px;">
					<text style="text-align: left; width: 100%;"></text>
					<text style="font-size: 14px; font-weight: bold; ">{{this.kefu}}</text>

					<text style="margin-top: 20px; font-size: 12px;"><u-button
							style="background: #0166fc;color: #FFFFFF; width: 70px;border-radius: 5px;" @click="obutton"
							:text="i18n.copy"></u-button></text>
				</view>




			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				kefu: ""
			}
		},
		computed: {
			i18n() {
				return this.$t("service")
			}
		},
		onLoad() {
			this.getData()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("service.complaints_mailbox")

			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F' // 导航栏背景颜色
				});
			}
		},
		methods: {
			getData() {
				var arr = []
				const res = this.$H.get('/api/v1/getSiteConfig', false).then(res => {
					// this.loading=false
					if (res.type == "ok") {

						this.kefu = res.data.kf




					}
				})
			},
			obutton() {

				this.seccendCopy()

			},
			seccendCopy() {
				const _this = this
				this.$copyText(this.kefu).then(
					function(e) {

						uni.$u.toast(_this.$t("out.successful"));
					},
					function(e) {


					}
				);
			}
		}
	}
</script>

<style>
	page {
		background: #F7F8FA;
	}
</style>