<template>
	<view>
		<view
			style="width: 85%; background: #FFFFFF; margin: 0 auto; margin-top: 10px; border-radius: 5px; padding: 15px;"
			:class="modalName=='move-box-'+ index?'move-cur':''" v-for="(item,index) in 4" :key="index"
			@touchstart="ListTouchStart" @touchmove="ListTouchMove" @touchend="ListTouchEnd"
			:data-target="'move-box-' + index">
			<view style="display:flex; width: 100%; font-size: 14px; font-weight: bold;">
				<view style="display: flex; justify-content:space-between; align-items: center; width: 96%;">
					<text style="font-size: 14px;">货币:</text>

					<text style="margin-top: 5px;">USD--TRC20</text>
				</view>

			</view>
			<view style="display:flex; width: 100%; font-size: 14px; margin-top: 10px; font-weight: bold;">
				<view style="display: flex; justify-content:space-between; align-items: center; width: 96%;">
					<text style="font-size: 14px;">提货地址:</text>

					<text style="margin-top: 5px;">123123123123123</text>
				</view>

			</view>
			<view class="move">
				<view class="bg-grey">置顶</view>
				<view class="bg-red">删除</view>
			</view>
		</view>
		<view
			style="width: 85%; background: #FFFFFF; margin: 0 auto; margin-top: 10px; border-radius: 5px; padding: 15px;">
			<view style="display:flex; width: 100%; font-size: 14px; font-weight: bold;">
				<view style="display: flex; justify-content:space-between; align-items: center; width: 96%;">
					<text style="font-size: 14px;">货币:</text>

					<text style="margin-top: 5px;">USD--TRC20</text>
				</view>

			</view>
			<view style="display:flex; width: 100%; font-size: 14px; margin-top: 10px; font-weight: bold;">
				<view style="display: flex; justify-content:space-between; align-items: center; width: 96%;">
					<text style="font-size: 14px;">提货地址:</text>

					<text style="margin-top: 5px;">123123123123123</text>
				</view>

			</view>
		</view>

		<!-- <view class="cu-list menu-avatar">
			<view class="cu-item" :class="modalName=='move-box-'+ index?'move-cur':''" v-for="(item,index) in 4"
				:key="index" @touchstart="ListTouchStart" @touchmove="ListTouchMove" @touchend="ListTouchEnd"
				:data-target="'move-box-' + index">
				<view class="cu-avatar round lg"
					:style="[{backgroundImage:'url(https://ossweb-img.qq.com/images/lol/web201310/skin/big2100'+ (index+2) +'.jpg)'}]">
				</view>
				<view class="content">
					<view class="text-grey">文晓港</view>
					<view class="text-gray text-sm">
						<text class="cuIcon-infofill text-red  margin-right-xs"></text> 消息未送达
					</view>
				</view>
				<view class="action">
					<view class="text-grey text-xs">22:20</view>
					<view class="cu-tag round bg-grey sm">5</view>
				</view>
				<view class="move">
					<view class="bg-grey">置顶</view>
					<view class="bg-red">删除</view>
				</view>
			</view>
		</view> -->



	</view>
</template>

<script>
	export default {
		data() {
			return {
				modalName: null,
				listTouchStart: 0,
				listTouchDirection: null
			}
		},
		methods: {

			// ListTouch触摸开始
			ListTouchStart(e) {
				this.listTouchStart = e.touches[0].pageX
			},
			// ListTouch计算方向
			ListTouchMove(e) {
				this.listTouchDirection = e.touches[0].pageX - this.listTouchStart > 0 ? 'right' : 'left'
			},
			// ListTouch计算滚动
			ListTouchEnd(e) {
				if (this.listTouchDirection == 'left') {
					this.modalName = e.currentTarget.dataset.target
				} else {
					this.modalName = null
				}
				this.listTouchDirection = null
			}
		}
	}
</script>

<style>
	page {
		background-color: #ffffff;
	}
</style>