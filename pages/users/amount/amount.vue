<template>
	<view :style="$store.state.bgColor=='black'?'background: #22252F;':'background: #fff;'">
		<view style="height: 20px;"></view>
		<view
			:style="$store.state.bgColor=='black'?'width:100%; display: flex; flex-direction: column; justify-content: center; align-items: center;    width: 90%;margin: 0 auto;padding: 5px; background: #22252F; color: #fff;':'width:100%; display: flex; flex-direction: column; justify-content: center; align-items: center;    width: 90%;margin: 0 auto;padding: 5px; '">
			<!-- 顶部选项卡 -->
			<view class="scroll-view">
				<view v-for="(item,index) in navsList" :key="index" class="scroll-item"
					:class="tabIndex===index?'scroll-items':'scroll-item'" @click="changesTabs(index)"
					:id="'tab'+index">{{item}}</view>
			</view>

			<!-- <swiper :duration="150" :current="tabIndex" @change="onChangeTab" disable-touch="true"
				:style="'height:'+scrollH+'px;overflow: hidden;width:95%;padding-left:15px'"> -->
			<swiper :duration="150" :current="tabIndex" @change="onChangeTab" disable-touch="true"
				:style="'height:'+scrollH+'px;width:95%;padding-left:15px'">
				<swiper-item>
					<!-- 	<scroll-view scroll-y="true"  scroll-x="false" enable-flex="true" class="section-area"
						:style="'height:'+scrollH+'px;'"> -->
					<scroll-view scroll-y="true" scroll-x="false" enable-flex="true" class="section-area"
						:style="'height:'+scrollH+'px;'">
						<view style="width: 95%; " :ref="'ulists0'">
							<view
								style="display: flex;  flex-direction: column; align-items: center; width: 100%; margin-top: 15px;">
								<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">
									{{i18n.digital_currency}}</view>


								<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='black'"
									@click="shows = true">
									<u--input v-model="values.name" :disabled="true"
										disabledColor="#222" placeholder="" inputAlign="left" style="height: 25px;"
										suffixIcon="arrow-right" class="bsone" color="#ffffff"
										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
								</view>


								<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='while'"
									@click="shows = true">
									<u--input border="none" v-model="values.name" :disabled="true"
										disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 25px;"
										suffixIcon="arrow-right"
										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
								</view>
							</view>
							<view
								style="display: flex;  flex-direction: column; align-items: center; width: 100%; margin-top: 15px;">
								<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">
									{{i18n.wallet_adress}}</view>
								<view
									style="display: flex; flex-direction: column; ;width: 100%; margin-top: 10px; justify-content: center; align-items: center; ">
									<view
										style="width: 145px; height: 145px; background:url('../../static/196.png'); margin: 0 auto; display: flex;justify-content: center; align-items: center;">


										<u--image :src="urls" style="width: 120px; height: 120px;"></u--image>
									</view>
									<view style="margin-top: 20px; ">{{address}}</view>
									<view
										style="display: flex;justify-content: center;align-items: center;margin-top: 10px;"
										@click="obuttons">
										<image src="../../../static/copy1 <EMAIL>" style="width: 32px; height: 32px;">
										</image>
										<text style="color: #3F47F4; ">{{i18n.copy}}</text>
									</view>
								</view>

							</view>
							<view
								style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 10px;">
								<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">
									{{i18n.number_of_coins}}</view>
								<view style="width: 100%; margin-top: 15px;">
									<u--input border="none" :placeholder="i18n.enter_number_coins"
										v-if="$store.state.bgColor=='black'" color="#fff" v-model="cnumbers"
										type="digit" ></u--input>
									<u--input border="none" :placeholder="i18n.enter_number_coins"
										v-if="$store.state.bgColor=='while'" v-model="cnumbers"
										type="digit" ></u--input>
								</view>

							</view>


							<view
								style="display: flex; flex-direction: column;  align-items: center; width: 100%; font-size: 12px; margin-top: 15px; padding-bottom: 10px; color: #6D6F7C;">
								<view
									style="flex: 1;font-size: 15px; font-weight: bold;width: 90%; color: #222222; margin-top: 10px;">
									{{i18n.payment_voucher}}</view>


								<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" multiple
									name="1" width="226" height="226" :maxCount="1">
									<image v-if="$store.state.bgColor=='black'" src="../../../static/buploads.png"
										mode="widthFix" style="width: 226rpx; height: 226rpx"></image>
									<image v-if="$store.state.bgColor=='while'" src="../../../static/uploads.png"
										mode="widthFix" style="width: 226rpx; height: 226rpx"></image>
								</u-upload>




							</view>
							<view style="width:100%">
								<button @click="asubmit()" style="background: #0166fc; margin-top: 20px;border-radius: 1000rpx;
																		    border: 0;   font-size: 12px; height: 45px; color: #FFFFFF;
								line-height: 45px !important;">{{i18n.submit}}</button>
							</view>

						</view>


					</scroll-view>
				</swiper-item>

				<swiper-item>
					<scroll-view scroll-y="false" enable-flex="true" class="section-area">

						<view style="width: 95%; " :ref="'ulists1'">
							<!-- <view
								style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 25px;"> -->
							<!-- <view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">{{i18n.currency}}</view> -->
							<!-- <view style="width: 100%; margin-top: 15px;">
									<u--input v-model="hnames" :disabled="true"
										disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 25px;"

										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
									<!-- <text>USDT</text> -->
							<!-- <u--input v-model="yhvalues.name" :disabled="true"
										disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 25px;"
										suffixIcon="arrow-right"
										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input> -->
							<!-- </view> -->
							<!-- <view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='black'"  @click="yhshows = true"><u--input v-model="cjyhvalues.name"  :disabled="true"
										disabledColor="#22252F" placeholder="" inputAlign="left"
										suffixIcon="arrow-right" class="bsone" color="#ffffff"
										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
								</view>


								<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='while'"  @click="yhshows = true"><u--input v-model="cjyhvalues.name"  :disabled="true"
										disabledColor="#ffffff" placeholder="" inputAlign="left"
										suffixIcon="arrow-right"
										suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
								</view> -->





							<!-- </view> -->

							<view
								style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 20px;">
								<view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">
									{{i18n.number_of_coins}}</view>
								<view style="width: 100%; margin-top: 15px;"> <u--input
										:placeholder="i18n.number_of_coins" v-model="yhcnumber"
										type="digit" ></u--input></view>

							</view>

							<view
								style="display: flex; flex-direction: column;  align-items: center; width: 100%; font-size: 12px; margin-top: 15px; padding-bottom: 10px; color: #6D6F7C;">
								<view
									style="flex: 1;font-size: 15px; font-weight: bold;width: 90%; color: #222222; margin-top: 10px;">
									{{i18n.payment_voucher}}</view>

								<u-upload :fileList="fileList2" @afterRead="afterReadTwo" @delete="deletePicTwo"
									multiple name="2" width="226" height="226" :maxCount="1">
									<image v-if="$store.state.bgColor=='black'" src="../../../static/buploads.png"
										mode="widthFix" style="width: 226rpx; height: 226rpx"></image>
									<image v-if="$store.state.bgColor=='while'" src="../../../static/uploads.png"
										mode="widthFix" style="width: 226rpx; height: 226rpx"></image>
								</u-upload>

							</view>

							<view style="width:100%">
								<button @click="yhtsubmit()" loadingText="" style="background: #3F47F4; margin-top: 20px;
																		    border: 0;   font-size: 12px; height: 55px; color: #FFFFFF;
								line-height: 55px !important;">{{i18n.submit}}</button>
							</view>
						</view>


					</scroll-view>
				</swiper-item>
			</swiper>

		</view>
		<u-picker :show="shows" v-if="$store.state.bgColor=='black'" ref="uPicker" :itemHeight="100"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker :show="shows" v-if="$store.state.bgColor=='while'" ref="uPicker" :itemHeight="100" :cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			:defaultIndex="dfindex" :columns="columns" keyName="name" @cancel="cancel" @confirm="confirm"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>
	</view>
</template>

<script>
	import $C from '@/common/config.js'
	import QRCode from 'qrcode'
	import {
		nextTick
	} from "vue"
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				navsList: ['数字货币'],
				tabIndex: 0,
				scrollInto: "",
				remarks: "",
				scrollH: 718,
				address: "",
				currency: 3,
				values: '',
				maxNumber: 30,
				cnumbers: null,
				types: 0,
				shows: false,
				tbvalues: '',
				imgPaths: "",
				imgPathstwo: "",
				urls: "",
				dfindex: [0],
				fileList1: [],
				fileList2: [],
				imgList4: [],
				imgList2: '',
				imgLists: [],
				imgListsTwo: [],
				cjyhvalues: '',
				yhtbvalues: '',
				yhnames: "",
				yhcnumber: null,
				yhremarks: "",
				yhrateses: 0,
				yhsnumber: 0,
				columns: [
					[]
				],
				tbcolumns: [
					['123456789123456789', '200', '300']
				]
			}
		},
		onNavigationBarButtonTap() {
			uni.navigateTo({
				url: "/pages/users/rjrecord/rjrecord"
			})
		},
		watch: {
			values: function(val) {

				const has = this.columns[0].find(item => item.name == val.name)

				if (has) {
					this.address = has.id
					this.makeQRCode(this.address)
				}
			},
		},
		onLoad() {
			this.tbvalues = this.tbcolumns[0][0]
			this.getData()
			this.sertNavs()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("deposit.deposit")
			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F'
				});
			}
			this.$nextTick(() => {

				let systemInfo = uni.getSystemInfoSync();
				this.scrollH = systemInfo.windowHeight - 100

				document.querySelectorAll('.uni-page-head .uni-page-head-ft .uni-page-head-btn')[0].querySelector(
					'.uni-btn-icon').innerText = this.$t("deposit.deposit_record");
			})

		},
		onShow() {

		},
		onReady() {
		},
		computed: {
			i18n() {
				return this.$t("deposit")
			},
			i18nOther() {
				return this.$t("other")
			},
			...mapState({
				lgStatus: state => state.lgStatus,
				users: state => state.users
			})

		},
		methods: {
			getData() {
				const res = this.$H.get('/api/v1/coinTopUpList', false).then(res => {


					if (res.type == "ok") {
						var currency = []
						res.data.map(item => {
							var currencyList = []

							currency.push({
								"name": item.agreement,
								"id": item.payment_address,
								"currency": item.id,


							})
							currencyList.push(currency)
							this.columns = currencyList

							this.values = this.columns[0][0]
							this.currency = this.columns[0][0].id
							this.currencyID = this.columns[0][0].currency
						})
					}

				})
			},
			confirm(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				console.log(e,888888888);
				this.currency = value[0].id
				this.currencyID = value[0].currency
				this.values = value[0]
				this.shows = false
			},
			cancel(e) {
				this.shows = false
			},
			sertNavs() {
				const {
					i18n,
				} = this
				this.navsList=[i18n.digital_currency]
			},
			asubmit() {
				const {
					cnumbers,
					fileList1
				} = this

				// if(this.cnumbers<=0)
				// {
				// 	 uni.$u.toast("请输入充币数量");
				// 	return false
				// }

				const res = this.$H.post('/api/v1/chargeReq', false, {
					currency: this.currencyID,
					account: this.imgPaths,
					amount: this.cnumbers
				}).then(res => {
					if (res.type == "ok") {
						this.fileList1 = []
						this.cnumbers = 0
						this.imgPaths = ""
						setTimeout(()=>{
							uni.reLaunch({
								url:'/pages/users/users'
							})
						},1000)
						uni.$u.toast(this.$t("deposit.completed"));
					}
				})


			},
			yhtsubmit() {
				const {
					yhcnumber,
					fileList2
				} = this

				// if(this.cnumbers<=0)
				// {
				// 	 uni.$u.toast("请输入充币数量");
				// 	return false
				// }

				const res = this.$H.post('/api/v1/chargeReqBank', false, {
					currency: 1,
					account: this.imgPathstwo,
					amount: this.yhcnumber
				}).then(res => {
					if (res.type == "ok") {
						this.fileList2 = []
						this.yhcnumber = 0
						this.imgPathstwo = ""
						uni.$u.toast(this.$t("deposit.completed"));

					}
				})


			},
			obuttons() {

				this.seccendCopy()

			},
			seccendCopy() {
				const _this = this
				this.$copyText(this.address).then(
					function(e) {

						uni.$u.toast(_this.$t("out.successful"));
					},
					function(e) {

					}
				);
			},
			async makeQRCode(address) {

				//             if(!address){

				// 	// address='33ie5hPoF4thkuk6YWZ9uMR8oiETXynaX9'
				// }
				this.urls = await QRCode.toDataURL(address)


			},
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},

			afterRead(event) {
				let that = this
				that.uploadImgFinished = false
				let lists = event.file
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				this.fileList1 = this.fileList1.slice(0, 9)
				this.imgLists = []
				let token = this.$store.state.token
				if (!token) {
					return uni.$u.toast(this.$t("real.re_certification"));
				}

				console.log(this.fileList1[0].url)


				this.imgPaths = ""
				uni.uploadFile({
					url: this.$C.webUrl + '/api/v1/upload',
					filePath: this.fileList1[0].url,
					name: 'file',
					formData: {
						type: 0
					},
					header: {
						'Authorization': "Bearer " + token
					},
					success(res) {

						let result = res.data.replace(/\n/g, "").replace(/\n/g, "").replace(/\n/g, "").replace(
							/\s/g, "")
						res = JSON.parse(decodeURIComponent(result))


						that.imgPaths = res.data.file_path


					},
					fail(res) {

						reject(res)
					},
					complete() {
						uni.hideLoading()
					}
				})

				console.log('待上传图片==', this.fileList1)
				// this.upimages(this.fileList2, 0)
			},
			deletePicTwo(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},

			afterReadTwo(event) {
				let that = this
				that.uploadImgFinished = false
				let lists = event.file
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
					})
				})
				this.fileList2 = this.fileList2.slice(0, 9)
				this.imgListsTwo = []
				console.log('待上传图片==', this.fileList2[0].url)
				// this.upimages(this.fileList1, 0)

				let token = this.$store.state.token
				if (!token) {
					return uni.$u.toast(this.$t("real.re_certification"));
				}

				this.imgPathstwo = ""
				uni.uploadFile({
					url: this.$C.webUrl + '/api/v1/upload',
					filePath: this.fileList2[0].url,
					name: 'file',
					formData: {
						type: 0
					},
					header: {
						'Authorization': "Bearer " + token
					},
					success(res) {

						let result = res.data.replace(/\n/g, "").replace(/\n/g, "").replace(/\n/g, "").replace(
							/\s/g, "")
						res = JSON.parse(decodeURIComponent(result))


						that.imgPathstwo = res.data.file_path


					},
					fail(res) {

						reject(res)
					},
					complete() {
						uni.hideLoading()
					}
				})


			},
			changesTabs(id) {
				if (this.tabIndex === id) {
					return;
				}
				this.tabIndex = id;
				this.scrollInto = 'tab' + id;
			},
			onChangeTab(e) {
				this.changesTabs(e.detail.current)
			}
		}
	}
</script>

<style>
	/deep/.uni-swiper-slide-frame {
		height: 100% !important;
	}

	.bnsss /deep/.uni-picker-view-mask {
		background: inherit;
	}

	.bnsss /deep/.u-picker__view__column__item {
		color: #fff;
	}

	.bnsss /deep/.u-popup__content {

		border-radius: 10px;
		background: #222;
	}

	.wnsss /deep/.u-popup__content {

		border-radius: 10px;
	}

	.scroll-view {
		width: 95% !important;
		height: 85rpx;
		line-height: 85rpx;
		white-space: nowrap;
		background: #F5F6FA;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
		border-radius: 148px;

	}

	uni-button {
		line-height: 30px !important;
		color: #FFFFFF !important;
		font-size: 14px !important;
	}

	.scroll-view .scroll-items {
		display: inline-block;
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #0166fc !important;
		border-radius: 6px;
		color: #ffffff !important;
		font-weight: bold;
		font-size: 14px;
	}

	.scroll-view .scroll-item {
		display: inline-block;
		width: 100%;
		line-height: 85rpx;
		height: 85rpx;
		text-align: center;
		border-radius: 148px;
		color: #626779;
		font-size: 14px;

	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
	}

	.section-area ::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	/deep/.uni-scroll-view-content {
		/* overflow: hidden !important; */
	}

	uni-page-head /deep/.uni-btn-icon {

		color: #fff !important;
	}

	/deep/.u-input {
		padding: 12px 9px !important;
		/* border: 1px solid #2F3142!important; */
		background-color: #f8f8f8 !important;
		border-radius: 1000rpx;
	}

	/deep/.u-image__image {
		width: 120px !important;
		height: 120px !important;
		padding-top: 8px;
	}

	page {
		position: inherit;
		overflow: hidden;
	}
</style>
