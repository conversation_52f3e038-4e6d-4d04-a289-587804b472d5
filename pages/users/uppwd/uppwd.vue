<template>
	<view :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<view style="height: 15px;"></view>
		<view
			:style="$store.state.bgColor=='black'?'bheight: 430px;background: #22252F;color: #ffffff;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;':'bheight: 430px;background: #FFFFFF;;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;' ">
			<view class="wrap">
				<view
					style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 0px;">
					<!-- <view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">{{i18n.enter_new_password}}</view> -->
					<uni-easyinput  :class="$store.state.bgColor=='black'?'bsone':'uni-mt-5'"
						style="margin-top: 10px;" :type="types" :passwordIcon="false" :suffixIcon="suffixIcon"
						v-model="passwords" :placeholder="i18n.enter_new_password" @iconClick="suffix"></uni-easyinput>

				</view>

			</view>
			<view class="wrap">
				<view
					style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: -5px;">
					<!-- <view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">{{i18n.enter_new_password_again}}</view> -->
					<view style="width: 100%; margin-top: 1px;">

						<uni-easyinput  class="uni-mt-5" :type="confirmtypes" :passwordIcon="false"
							:suffixIcon="confirmsuffixIcon" v-model="confirmpasswords"
							:placeholder="i18n.enter_new_password_again" @iconClick="confirmsuffix"></uni-easyinput>
					</view>
				</view>
			</view>
			<view class="wrap" style="margin-top: 0px;">
				<view
					style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-bottom: 0px;">
					<!-- <view style="flex: 1;font-size: 15px; font-weight: bold;width: 100%;">{{i18n.enter_vercode}}</view> -->



				</view>
				<!-- #ifndef APP-NVUE -->
				<u-input  border="none" v-model="code" style=" padding: 2px;" :placeholder="i18n.enter_vercode">
				<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					<u--input v-model="code" style="padding: 12px;" :placeholder="i18n.enter_vercode">
					<!-- #endif -->
						<template slot="suffix">
							<u-code ref="uCode" @change="codeChange" seconds="60"
								:changeText="'X'+i18n.get_ver_code"></u-code>
							<u-button @tap="getCode" :text="tips" size="normal"></u-button>


						</template>
				<!-- #ifndef APP-NVUE -->
				</u-input>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				</u--input>
				<!-- #endif -->
				<view style="width:100%;margin-top: 30px;">
					<u-button :loading="loading" loadingText="......" @click="obuttons()" loadingSize="30" style="background: #0166fc; margin-top: 20px;
														    border: 0;   font-size: 12px;  height: 45px;border-radius: 30px; color: #FFFFFF;
				line-height: 55px !important;">{{$t("order.modify")}}</u-button>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tips: '111',
				code: '',
				confirmpasswords: null,
				passwords: null,
				seconds: "",
				loading: false,
				passwordShow: true,
				confirmpasswordShow: true,
				getcode: null,
				email: "",
				types: "password",
				confirmtypes: "password"
			}
		},
		watch: {
			// values(newValue, oldValue) {
			// 	console.log('v-model', newValue);
			// }
		},
		onLoad() {
			let token = this.$store.state.token
			if (!token) {
				// return uni.$u.toast(this.$t("real.re_certification"));
			}
			this.email = this.$store.state.users.email

			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("mine.change_password")

			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F' // 导航栏背景颜色
				});
			}

		},
		computed: {
			i18n() {
				return this.$t("reset")
			},
			suffixIcon() {

				if (this.passwordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			},
			confirmsuffixIcon() {

				if (this.confirmpasswordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			}
		},
		methods: {
			suffix() {

				this.types = this.types === "password" ? "text" : "password"
				this.passwordShow = !this.passwordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			},
			confirmsuffix() {

				this.confirmtypes = this.confirmtypes === "password" ? "text" : "password"
				this.confirmpasswordShow = !this.confirmpasswordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			},
			obuttons() {

				let {
					code,
					passwords,
					confirmpasswords
				} = this



				if (!passwords) {

					uni.$u.toast(this.$t("reset.enter_new_password"));
					return false
				}
				// if(this.$utils.charTest(passwords) ){
				// uni.showToast({
				// 	title: '密码有特殊字符，请重新填写密码' || '校验错误',
				// 	icon: 'none'
				// });
				// 	// this.$utils.showToast(this.$t("common.specialChart"))
				// 	return false
				// }
				// if(passwords != confirmpasswords){
				// 	 uni.$u.toast(_this.$t("reset.enter_new_password_again")); 
				// 	return false
				// }

				// if(!this.getcode)
				// {
				// 	uni.showToast({
				// 		title: '请先获取验证码' || '校验错误',
				// 		icon: 'none'
				// 	});
				// 	return false
				// }
				// if(!code)
				// {
				// 	uni.showToast({
				// 		title: '验证码不能为空' || '校验错误',
				// 		icon: 'none'
				// 	});
				// 	return false
				// }
				this.loading = true
				const res = this.$H.post('/api/v1/forgetPassword', false, {
					code: code,
					password: passwords,
					repassword: confirmpasswords
				}).then(res => {
					console.log(res.data)
					this.loading = false

					if (res.data.code == 403) {

						uni.$u.toast(res.data.data.message);
						return false;
					}
					if (res.type == "ok") {
						uni.$u.toast(this.$t("deposit.completed"));
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							});

						}, 300)

					} else {

					}
				})


			},
			obutton() {
				this.loading = true

			},
			codeChange(text) {
				if ("获取验证码" == text || "重新获取" == text) {
					text = this.$t("reset.get_ver_code")
				}
				this.tips = text;
			},
			getCode() {

				let {
					email
				} = this

				if (this.$refs.uCode.canGetCode) {
					uni.showLoading({
						// title: '正在获取验证码...'
					})
					const res = this.$H.post('/api/v1/sms_mail', false, {
						user_string: email
					}).then(res => {
						uni.hideLoading();
						if (res.type == "ok") {
							// uni.$u.toast('验证码已发送');
							console.log(res)
							this.getcode = 1;
							this.$refs.uCode.start();
						}
					})

				} else {
					// uni.$u.toast('倒计时结束后再发送');
				}

			},
			// getCode() {
			// 	if (this.$refs.uCode.canGetCode) {
			// 		// 模拟向后端请求验证码
			// 		uni.showLoading({
			// 			title: '正在获取验证码...'
			// 		})
			// 		setTimeout(() => {
			// 			uni.hideLoading();
			// 			// 这里此提示会被this.start()方法中的提示覆盖
			// 			uni.$u.toast('验证码已发送');
			// 			// 通知验证码组件内部开始倒计时
			// 			this.$refs.uCode.start();
			// 		}, 2000);
			// 	} else {
			// 		uni.$u.toast('倒计时结束后再发送');
			// 	}
			// },
			change(e) {
				console.log('change', e);
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #ffffff;

	}

	.bsone {
		background: #1A1C24 !important;
		border: 1px solid #2F3142 !important;
		color: #fff;
	}

	.wrap {
		padding: 24rpx;
	}

	.blacks .u-border {

		color: #fff;
		border: 1px solid #2F3142 !important;
	}

	.u-border {

		border-color: #D2D6E8 !important;

	}

	.borderNone {
		border: none !important;
	}


	// 显示边框
	.is-input-border {
		border: none !important;
	}

	.u-button--info[data-v-3bf2dba7] {
		height: 35px;
		color: #ffffff;
		background-color: #0166fc;
		border-color: #ebedf0;
		border-width: 0px;
		font-weight: bold;
	}

	.inps {
		padding: 10px !important;
	}

	/deep/.u-icon__img {
		width: 20px !important;
		height: 20px !important;
	}

	/* /deep/.u-icon__icon[data-v-172979f2] {
		
		    font-size: 22px !important;
		} */
	/deep/.content-clear-icon {
		color: initial !important;
	}

	/deep/.uni-easyinput__content[data-v-abe12412] {
		border: none !important;
		// padding: 2px;
	}

	.blacks /deep/.is-input-border {
		border: none !important;
		border: 1px solid #2F3142 !important;
		background: #222 !important;
		color: #fff;
	}

	.blacks /deep/.uni-input-input {
		color: #fff;
	}

	/deep/.uni-easyinput__placeholder-class {
border: none !important;
		font-size: 15px;
		color: rgb(192, 196, 204);
		padding-left: 3px !important;
	}

	/deep/.uni-easyinput__content-input {
		border: none !important;
		padding-left: 3px !important;
	}
</style>