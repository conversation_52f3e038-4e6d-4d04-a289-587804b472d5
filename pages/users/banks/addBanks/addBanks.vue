<template>
	<view :class="$store.state.bgColor=='black'?'bnsss':'wnsss'">
		<view style="height: 5px;"></view>
		<view
			:style="$store.state.bgColor=='black'?'height: auto;background: #22252F;color: #fff;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto; padding: 5px;':'height: auto;background: #FFFFFF;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto; padding: 5px;' ">
			<view class="wrap">
				<view class="flex-bank">
					<view class="flex-bank-item">{{i18n.currency}}</view>
					<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='black'"
						@click="yhshows = true"><u--input v-model="cjyhvalues.name" :disabled="true"
							disabledColor="#22252F" placeholder="" inputAlign="left" style="height: 33px;"
							suffixIcon="arrow-right" class="bsone" color="#ffffff"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
					</view>
					<view style="width: 100%; margin-top: 10px;" v-if="$store.state.bgColor=='while'"
						@click="yhshows = true"><u--input v-model="cjyhvalues.name" :disabled="true"
							disabledColor="#ffffff" placeholder="" inputAlign="left" style="height: 33px;"
							suffixIcon="arrow-right"
							suffixIconStyle="color: #909399;font-size:16px;font-weight: bold;"></u--input>
					</view>
				</view>

				<view class="flex-bank">
					<view class="flex-bank-item">{{i18n.bank_name}}</view>
					<view style="width: 100%; margin-top: 15px;"> <u--input :placeholder="i18n.enter_bank_name"
							v-model="bank_name" style="height: 33px;"></u--input>
					</view>
				</view>
				<view class="flex-bank">
					<view class="flex-bank-item">{{i18n.bank_address}}</view>
					<view style="width: 100%; margin-top: 15px;"> <u--input :placeholder="i18n.enter_bank_address"
							style="height: 33px;" v-model="bank_address"></u--input>
					</view>
				</view>

				<!-- <view class="flex-bank">
					<view class="flex-bank-item">SWIFT</view>
					<view style="width: 100%; margin-top: 15px;"> <u--input placeholder="SWIFT" v-model="swift"
							style="height: 33px;"></u--input>
					</view>
				</view> -->

				<view class="flex-bank">
					<view class="flex-bank-item">{{i18n.payee}}</view>
					<view style="width: 100%; margin-top: 15px;"> <u--input :placeholder="i18n.enter_the_payee"
							v-model="real_name" style="height: 33px;"></u--input>
					</view>
				</view>
				<view class="flex-bank">
					<view class="flex-bank-item">{{i18n.payee_account}}</view>
					<view style="width: 100%; margin-top: 15px;"> <u--input :placeholder="i18n.enter_account"
							v-model="bank_account" style="height: 33px;" type="number"></u--input>
					</view>

				</view>
			</view>
			<view style="width:93%;margin: 0 auto; margin-top: 5px; padding-bottom: 5px; ">
				<u-button :loading="loading" loadingText="" @click="obutton()" loadingSize="30" style="background: #3F47F4; margin-bottom: 10px; margin-top: 10px;
																    border: 0;   font-size: 12px;  height: 55px; color: #FFFFFF;
						line-height: 55px !important;">{{i18n.add}}</u-button>
			</view>
		</view>

		<u-picker :show="yhshows" v-if="$store.state.bgColor=='black'" ref="yhuPicker" :itemHeight="100"
			:defaultIndex="yhdfindex" :columns="yhcolumns" keyName="name" @cancel="yhcancel" @confirm="yhconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #222;" class="bnsss"></u-picker>

		<u-picker v-if="$store.state.bgColor=='while'" :show="yhshows" ref="yhuPicker" :itemHeight="100"
			:defaultIndex="yhdfindex" :columns="yhcolumns" keyName="name" @cancel="yhcancel" @confirm="yhconfirm"
			:cancelText="i18nOther.cancel" :confirmText="i18nOther.confirm"
			style="border-radius: 18px;background: #fff;" class="wnsss"></u-picker>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				bank_name: "",
				bank_address: "",
				swift: "",
				real_name: "",
				bank_account: "",
				loading: false,
				dfindex: [0],
				cjyhvalues: '',
				yhshows: false,
				yhdfindex: [0],
				yhcolumns: [],
			}
		},
		onLoad() {
			this.$H.get('/api/v1/extractBank', false).then(res => {
				if (res.type == "ok") {
					var currency = []
					var currencyList = []
					res.data.map(item => {
						currency.push({
							"name": item.name,
							"id": item.id,
							"rate": item.rate
						})
					})
					currencyList.push(currency)
					this.yhcolumns = currencyList
					this.cjyhvalues = this.yhcolumns[0][0]
				}
			})

			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("wallet.bind_bank_card")
			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F'
				});
			}
		},
		computed: {
			i18n() {
				return this.$t("wallet")
			},
			i18nOther() {
				return this.$t("other")
			},
		},
		methods: {
			yhconfirm(e) {
				const {
					columnIndex,
					value,
					yhvalues, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.yhuPicker
				} = e
				this.cjyhvalues = value[0]
				// 不在这里设置yhcurrency，让yhtsubmit方法直接使用this.cjyhvalues.id
				this.yhshows = false
			},
			yhcancel(e) {
				this.yhshows = false
			},
			obutton() {
				let {
					bank_name,
					bank_address,
					swift,
					real_name,
					bank_account
				} = this
				const _this = this

				if (!bank_name) {
					uni.$u.toast(_this.$t("wallet.enter_bank_name"));
					return false
				}
				if (!bank_address) {
					uni.$u.toast(_this.$t("wallet.enter_bank_address"));
					return false
				}
				// if (!swift) {
				// 	uni.$u.toast(_this.$t("wallet.swift"));
				// 	return false
				// }
				if (!real_name) {
					uni.$u.toast(_this.$t("wallet.enter_the_payee"));
					return false
				}
				if (!bank_account) {
					uni.$u.toast(_this.$t("wallet.enter_account"));
					return false
				}
				this.loading = true
				const res = this.$H.post('/api/v1/saveCashInfo', false, {
					bank_name: bank_name,
					digital_bank_id:this.cjyhvalues.id,
					bank_address: bank_address,
					// swift: swift,
					real_name: real_name,
					bank_account: bank_account
				}).then(res => {
					this.loading = false
					if (res.type == "ok") {
						this.bank_name = ""
						this.bank_address = ""
						this.swift = ""
						this.real_name = ""
						this.bank_account = ""
						// uni.$u.toast("添加成功");
						uni.$u.toast(this.$t("deposit.completed"));
						setTimeout(()=>{
							this.navBack()
						},1000)
					}
				})
			},
			navBack() {
				const pages = getCurrentPages()
				if (pages && pages.length > 1) {
					uni.navigateBack({
						delta: 1
					})
				} else {
					uni.reLaunch({
						url: '/pages/users/users'
					})
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	page {
		background-color: #F7F8FA;
		position: inherit;
		overflow: hidden;
		height: auto !important;
	}

	.flex-bank {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		margin-top: 10px;

		.flex-bank-item {
			flex: 1;
			font-size: 15px;
			font-weight: bold;
			width: 100%;
		}
	}

	.wrap {
		padding: 24rpx;
	}

	.u-border {
		border-color: #D2D6E8 !important;
	}

	.bnsss .u-border {
		border: 1px solid #2F3142 !important;
	}

	.u-button--info[data-v-3bf2dba7] {
		color: #4B53F5;
		border-color: #ebedf0;
		border-width: 0px;
		font-weight: bold;
	}

	.bnsss /deep/.uni-input-input {
		color: #fff;
	}
</style>