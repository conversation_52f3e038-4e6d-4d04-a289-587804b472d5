<template>
	<view :class="$store.state.bgColor=='black'?'bnsss':'wnsss'">
<view style="height: 1px;"></view>
<view v-for="(orderItem, index) in indexList"
			:key="index" class="cu-list menu-avatar" :style="$store.state.bgColor=='black'?'width: 85%;  background: #22252F; color: #fff; margin: 0 auto; margin-top: 12px; padding: 15px;padding-right: 0px;border-radius: 5px;height: 95px;':'width: 85%; background: #FFFFFF;  margin: 0 auto; margin-top: 12px; padding: 15px;padding-right: 0px;border-radius: 5px;height: 95px;'">
	<view style="width: 100%; " class="cu-item" :class="modalName=='move-box-'+ index?'move-cur':''" @touchstart="ListTouchStart"
				@touchmove="ListTouchMove" @touchend="ListTouchEnd" :data-target="'move-box-' + index">
			<view  style="display:flex; width: 95%; font-size: 14px; font-weight: bold; padding-right: 12px;">
				<view style="display: flex; flex-direction:column; justify-content: center; align-items: left; text-align: left;   ">
					<text style="color:#AFAFAF;font-size: 14px;">{{i18n.pay_currency}}</text>
		
					<text style="margin-top: 5px;">{{orderItem.digital_bank_set.name}}</text>
				</view>
				<view
					style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
					<text style="color:#AFAFAF;font-weight: bold;font-size: 14px;">{{i18n.bank_name}}</text>
					<text style="margin-top: 5px;">{{orderItem.bank_name}}</text>
				</view>
				<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; ">
					<text style="color:#AFAFAF;font-weight: bold;font-size: 14px;">{{i18n.payee}}</text>
					<text style="margin-top: 5px;">{{orderItem.real_name}}</text>
				</view>
			</view>
			<view
				style="display:flex; width: 95%; padding-top: 18px; font-size: 14px; font-weight: bold; color: #AFAFAF; padding-right: 12px;">
				<view style="display: flex; flex: 1; flex-direction:column;  ">
					<text>{{$t('order.hanling_fee')}}</text>
					<text style="color: #222222;margin-top: 5px;">0.00</text>
				</view>
		
				
			</view>
			<view class="move" @touchstart="vclick(orderItem)" style="height: 135px;">
				<view class="bg-red">{{i18n.delete}}</view>
			</view>
			</view>
		</view>

<view style="height: 15px;"></view>





		<u-modal :show="ishow"  @cancel="cancel" @confirm="confirm"  :showCancelButton="true"  :confirmText="confirmText" :cancelText="cancelText" :title="title" :content='content'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ishow: false,
				title: '操作',
				content: '确定要删除操作?',
				modalName: null,
				listTouchStart: 0,
				listTouchDirection: null,
				isdel: true,
				confirmText:"确认",
				cancelText:"取消",
				banks:{},
				indexList: [],
				longClick: 0,
				timeOutEvent: 0,
				 Loop:0
			}
		},
		onNavigationBarButtonTap(){
			uni.navigateTo({
				url:"/pages/users/banks/addBanks/addBanks"
			})
		},
		onLoad() {
		  this.getData()
		  const _this = this
		  uni.setNavigationBarTitle({
		  	title:_this.$t("wallet.bank_card")
		  })
		  if(this.$store.state.bgColor=='black')
		  {
		  			  uni.setNavigationBarColor({
		  			    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
		  			    backgroundColor: '#22252F' 
		  			  });
		  }
this.$nextTick(()=>
		  {
		    document.querySelectorAll('.uni-page-head .uni-page-head-ft .uni-page-head-btn')[0].querySelector('.uni-btn-icon').innerText="+"+this.$t("wallet.add");
		 })
		  
		},
		onShow() {
		  this.getData()
		},
		computed:{
			i18n() {
				return this.$t("wallet")
			}
		},
		methods: {
getData() {
							
							
							var arryh = []
							const resyh =  this.$H.get('/api/v1/cashInfo',false).then(res => {
															    // this.loading=false
												    if(res.type=="ok"){
														
														
														
														
														var arryh = []
														
													
														
														res.data.map(item=>{
															
															
														
															
															arryh.push(item)
															
													
																						
														})
															this.indexList = arryh
														
																	
																}else{
																	
																}
												})
						
						},
confirm(){
	
	this.modalName = null
	const res =  this.$H.get('/api/v1/cashDelete',false,{
					   id:this.banks.id
					   }).then(res => {
						  
							  if(res.type=="ok"){
							  	 uni.$u.toast(this.$t("deposit.completed"));
								 const has = this.indexList.findIndex(item => item.id == this.banks.id)
								  
								 if(has > -1){
								 	
								 	this.indexList.splice(has, 1)
								 }
								
								this.isdel=true
								this.ishow=false
							  }
						})

},
cancel(){
	this.isdel=true
	this.ishow=false
},
			// ListTouch触摸开始
			ListTouchStart(e) {
				this.listTouchStart = e.touches[0].pageX
			// e.stopPropagation();
			// var that = this;
			//       this.longClick = 0;
			//       this.timeOutEvent = setTimeout(function() {
			//         that.longClick = 1;
			//         console.log("这是长按")
			//       }, 500);

   //                     console.log(this.longClick)
			// 				this.listTouchStart = e.touches[0].pageX
				
			// 				if(this.modalName){
							
								
			// 					// if (this.isdel) {
			// 						this.modalName = null
			// 					// }
								
			// 				}else{
								
			// 					this.listTouchDirection ='left'
			// 				}
let that=this;
    this.Loop = setTimeout(function() {
      that.Loop = 0;
    
    }, 200);
    return false;							
						},
			// ListTouch计算方向
			ListTouchMove(e) {

				this.listTouchDirection = e.touches[0].pageX - this.listTouchStart > 0 ? 'right' : 'left'
			},

			// ListTouch计算滚动
			ListTouchEnd(e) {
				let that=this;
				    clearTimeout(this.Loop);
				    if(that.Loop!==0){
				      				
				      	
				      				if(this.modalName){
				      				
				      					
				      					// if (this.isdel) {
				      						this.modalName = null
				      					// }
				      					
				      				}else{
				      					
				      					this.listTouchDirection ='left'
				      				}
									e.stopPropagation();
													if (this.listTouchDirection == 'left') {
														this.modalName = e.currentTarget.dataset.target
													} else {
									
														if (this.isdel) {
															this.modalName = null
														}
									
													}
													this.listTouchDirection = null
				    }
				   
		
						},
			vclick(item) {
               
                this.banks=item
				
				this.isdel = false
				this.ishow=true
				// this.isdel = true;

			}
		}
	}
</script>

<style>
	.cu-list.menu-avatar>.cu-item {
		height: 85px;
		justify-content: center;
		flex-direction: column;
		
	}
	.wnsss
	{
		 background-color: #F7F8FA;
	}
	.bnsss
	{
		 background-color: #1A1C24;
	}
  .bnsss .cu-list.menu-avatar>.cu-item {
		height: 85px;
		justify-content: center;
		flex-direction: column;
		background: #22252F;
		
	}
	.cu-list.menu-avatar>.cu-item:after,
	.cu-list.menu>.cu-item:after {

		border-bottom: 0px;

	}

	.cu-list>.cu-item.move-cur {
		-webkit-transform: translateX(-80px);
		transform: translateX(-80px);
	}

	.cu-list>.cu-item .move {

		width: 75px;

	}
.bg-red {
    font-size: 14px;
}
.bg-red {
   
    width: 60px;
}
	page {
		/* background-color: #F7F8FA; */
		/* height: auto; */
		position: inherit;
		overflow: initial;
		 background-color: #F7F8FA;
	}
</style>