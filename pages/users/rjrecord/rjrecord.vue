<template>
	<view :style="$store.state.bgColor=='black'?'background: #1A1C24;':'background:#F7F8FA;height: 100%'">

		<view v-if="$store.state.bgColor=='black'" style="height: 2px; background: #1A1C24;"></view>
		<view :style="$store.state.bgColor=='black'?'width: 85%; background: #22252F; color: #fff;  margin: 0 auto; margin-top:10px;   padding: 15px;':'width: 85%; background: #fff;  margin: 0 auto; margin-top: 10px; padding: 15px;'" v-for="(orderItem, index2) in indexList"
				:key="index2">
			<view style="display:flex; width: 100%; font-size: 12px; font-weight: bold;">
				<view style="display: flex; flex-direction:column; justify-content: center;    ">
					<text style="color:#AFAFAF;font-size: 14px;">{{i18n.number_of_coins}}</text>

					<text style="margin-top: 5px;">{{orderItem.amount}}</text>
				</view>
				<view
					style="display: flex; flex-direction:column; justify-content: center; align-items: center; flex: 1;">
					<text style="color:#AFAFAF;font-weight: bold;font-size: 14px;">{{i18n.type}}</text>
					<!-- <text style="margin-top: 5px;" v-if="orderItem.status==2">{{orderItem.amount}}</text> -->
					<text style="margin-top: 5px;" >{{i18n.digital_currency}}</text>
				</view>
				<view style="display: flex; flex-direction:column; justify-content: center; align-items: center; ">
					<text style="color:#AFAFAF;font-weight: bold;font-size: 14px;">{{i18n.unit}}</text>
					<text style="margin-top: 5px;">{{orderItem.name}}</text>
				</view>
			</view>
			<view
				style="display:flex; width: 100%; padding-top: 18px; font-size: 14px; font-weight: bold; color: #AFAFAF; ">
				<view style="display: flex; flex: 1; flex-direction:column;  ">
					<text>{{i18n.status}}</text>
					<text :style="$store.state.bgColor=='black'?'color: #fff;margin-top: 5px;':'color: #222222;margin-top: 5px;'">{{orderItem.status_name}}</text>
				</view>

				<view style="display: flex; flex: 1; flex-direction:column;    text-align: right;  ">
					<text>{{i18n.time}}</text>
					<text :style="$store.state.bgColor=='black'?'color: #fff;margin-top: 5px;':'color: #222222;margin-top: 5px;'">{{orderItem.created_at}}</text>
				</view>
			</view>
		</view>
		<view style="height: 10px;"></view>
		<view :style="$store.state.bgColor=='black'?'width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px; color: #fff;':'width: 100%; height: 35px; display: flex; justify-content: center; padding-top: 10px;'">
			<text >{{loding}}</text>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
indexList: [],
	pages:1,
				status:false,
				loding:"",
				scrollH:0
			}
		},
		onLoad() {
		  this.getData()
		    const _this = this
		  uni.setNavigationBarTitle({
		  	title:_this.$t("deposit.deposit_record")
		  })
		  if(this.$store.state.bgColor=='black')
		  {
		  			  uni.setNavigationBarColor({
		  			    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
		  			    backgroundColor: '#22252F'
		  			  });
		  }

		},
		onReachBottom() {
			this.scrolltolower()
		},
		computed:{
			i18n() {
				return this.$t("deposit")
			}
		},
		methods: {
			// 日期格式化方法
			formatDate(dateStr, format = 'yyyy-MM-dd hh:mm') {
				if (!dateStr) return '';

				// 处理日期字符串，确保兼容性
				let date;
				if (typeof dateStr === 'string') {
					// 处理ISO格式的日期字符串
					if (dateStr.indexOf('T') > -1) {
						date = new Date(dateStr);
					} else {
						// 处理其他格式，替换-为/以兼容iOS
						date = new Date(dateStr.replace(/-/g, '/'));
					}
				} else {
					date = new Date(dateStr);
				}

				// 检查日期是否有效
				if (isNaN(date.getTime())) {
					return dateStr; // 如果无法解析，返回原字符串
				}

				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');

				return format
					.replace('yyyy', year)
					.replace('MM', month)
					.replace('dd', day)
					.replace('hh', hours)
					.replace('mm', minutes)
					.replace('ss', seconds);
			},
			scrolltolower() {

				if(this.status)return false
				   this.pages+=1
				   this.loding="..........."
					 this.getData()
			},
getData() {
	this.status=true


				var arr = []
				const res =  this.$H.post('/api/v1/rechargeLog',false,{
					page:this.pages
				}).then(res => {
												    // this.loading=false
									    if(res.type=="ok"){

									// 检查数据结构 - 尝试多种可能的路径
									let dataList = [];
									if (res.data && res.data.data && Array.isArray(res.data.data)) {
										dataList = res.data.data;
									} else if (res.data && res.data.data && res.data.data.data && Array.isArray(res.data.data.data)) {
										dataList = res.data.data.data;
									}
									console.log('dataList:', dataList)

											var arr = []
											if(dataList.length<=0)
											{
												this.status=false
												this.loding=""
												this.pages-=1
												return
											}


											dataList.map(item=>{

												// 使用正确的日期格式化方法
												item.created_at = this.formatDate(item.created_at, 'yyyy-MM-dd hh:mm')

												arr.push(item)

											})

											if(this.indexList.length>0){
												this.indexList=[...this.indexList,...arr]

											}else{
												this.indexList = arr
											}

													this.status=false





													}else{

													}
									})


			}




		}
	}
</script>

<style>
	page{
		/* background-color: #F7F8FA; */
		position:initial;
		overflow: initial;
	}
</style>
