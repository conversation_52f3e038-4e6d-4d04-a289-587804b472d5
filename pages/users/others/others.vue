<template>
	<view :class="$store.state.bgColor=='black'?'blacks':'whiles'">
		<view :style="$store.state.bgColor=='black'?' background: #22252F; width: 90%; margin: 0 auto; border-radius: 10px; margin-top: 11px;':'border: 1px solid #E8EBF3;width: 90%; margin: 0 auto; border-radius: 10px; margin-top: 11px;'">
			<uni-list-item :thumb="$store.state.bgColor=='while'?'../../../static/tcdl2 <EMAIL>':'../../../static/u11.png'" link clickable :title="i18n.logout" @click="goexit()" to=""></uni-list-item>
			
		</view>
		<u-modal :show="ishow" :style="$store.state.bgColor=='black'?'background: #222;':''"  @cancel="cancel" @confirm="confirm"  :showCancelButton="true"  :confirmText="confirmText" :cancelText="cancelText" :title="title" :content='content'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ishow:false,
				title: this.$t("other.alerts"),
				content: this.$t("other.to_log_out"),
				confirmText: this.$t("other.confirm"),
				cancelText: this.$t("other.cancel")
			}
		},
		computed: {
			i18n() {
				return this.$t("other")
			}
		},
		onLoad() {
			const _this = this
			uni.setNavigationBarTitle({
				title:_this.$t("other.other")
						
			})
			if(this.$store.state.bgColor=='black')
			{
						  uni.setNavigationBarColor({
						    frontColor: '#ffffff', 
						    backgroundColor: '#22252F'
						  });
			}
		},
		methods: {
			goexit(){
				this.ishow=true
			},
			cancel(){
				this.ishow=false
			},
			confirm(){
				this.logout()
				this.ishow=false
			},
			logout(){
				this.$store.commit('logout')
				
				setTimeout(() => {
					// uni.navigateBack({
					// 	delta:1
					// })
					uni.reLaunch({
						url:'/pages/login/login'
					})
				
				}, 1000)
			}
		}
	}
</script>

<style>
.blacks .uni-list-item{
	background: #22252F !important;
	color: #fff !important;
}
.blacks /deep/.uni-list--border:after{
	position: initial;
}

.whiles .uni-list-item{
	
}
.whiles /deep/.uni-list--border:after{
	
}
.blacks /deep/.u-popup__content{
	background: #222;
	    color: #fff;
}
.whiles /deep/.u-popup__content{
	
}
.blacks /deep/.u-modal__title{
	color: #fff;
}
.blacks /deep/.u-modal__content__text{
	color: #fff;
}

.blacks /deep/.uni-list-item__content-title
{
	color: #fff;
}
</style>
