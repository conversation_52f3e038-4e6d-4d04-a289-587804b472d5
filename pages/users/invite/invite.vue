<template>
	<view>
		<view style="height: 15px;"></view>
		<view
			:style="$store.state.bgColor=='black'?'bheight: 430px;background: #22252F;color: #ffffff;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;':'bheight: 430px;background: #FFFFFF;;border-radius: 6px 6px 6px 6px;opacity: 1; width: 90%; margin: 0 auto;' ">
			<view class="wrap">

				<view
					style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 15px;">
					<view style="flex: 1;font-size: 14px;color: #dad5da; width: 100%; text-align: center;">
						{{i18n.invitation_code}}</view>
					<view
						style="display: flex; flex-direction: column; ;width: 100%; margin-top: 10px; justify-content: center; align-items: center; ">
						<text
							style="color: #0166fc;font-weight:bold; font-size: 18px;">{{this.$store.state.users.extension_code}}</text>
						<view
							style="width: 205px; height: 205px;  margin: 0 auto; display: flex;justify-content: center; align-items: center;">


							<u--image :src="urls" mode="widthFix" width="380" height="380"></u--image>



						</view>
						<view
							style="margin-top: 1px;width: 260px; font-size: 14px; overflow: hidden; word-wrap: break-word; text-align: center;">
							{{this.erwims}}</view>
						<view style="display: flex;justify-content: center;align-items: center;margin-top: 10px;">

							<u-button @click="obutton()" loadingSize="30" style="background: #0166fc; margin-top: 2px; border-radius:100px ;
																    border: 0;   font-size: 14px; width: 140px;   color: #FFFFFF;
						height: 55px; line-height: normal; display: flex; align-items: center; justify-content: center;">Save</u-button>
						</view>
					</view>

				</view>






			</view>

		</view>
	</view>
</template>

<script>
	import QRCode from 'qrcode'

	export default {
		data() {
			return {
				urls: "",
				erwims: ""
			}
		},
		onLoad() {

			this.makeQRCode(this.$store.state.users.extension_code)
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("invite.invite_friends")

			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F' // 导航栏背景颜色
				});
			}
		},
		computed: {
			i18n() {
				return this.$t("invite")
			}
		},
		watch: {
			// 监听语言变化，强制更新组件
			'$i18n.locale'() {
				this.$forceUpdate()
			}
		},
		methods: {
			obutton() {

				this.seccendCopy()

			},
			seccendCopy() {
				const _this = this
				this.$copyText(this.erwims).then(
					function(e) {

						uni.$u.toast(_this.$t("out.successful"));
					},
					function(e) {

						uni.$u.toast("复制失败");
					}
				);
			},
			async makeQRCode(invite_code) {
				this.erwims = "http://m.tickmailltrades.com/#/pages/registerTwo/registerTwo?invite_code=" + this
					.$store.state.users.extension_code
				this.urls = await QRCode.toDataURL(
					"http://m.tickmailltrades.com/#/pages/registerTwo/registerTwo?invite_code=" + this.$store
					.state.users.extension_code)
			}
		}
	}
</script>

<style>
	page {
		background-color: #F7F8FA;

	}

	.wrap {
		padding: 24rpx;
	}

	.u-border {
		border-color: #D2D6E8 !important;
	}

	.u-button--info[data-v-3bf2dba7] {
		color: #4B53F5;
		border-color: #ebedf0;
		border-width: 0px;
		font-weight: bold;
	}
</style>
