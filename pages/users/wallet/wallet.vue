<template>
	<view>
		<view
			style="background: #ffffff; width: 93%; margin: 0 auto; margin-bottom: 12px; height: 100px; margin-top: 5px; border-radius: 5px; display: flex;justify-content: center;align-items: center;">
			<view style="flex: 1;display: flex; flex-direction: column;">
				<text style=" font-size: 25rpx">{{i18n.balance}}</text>
				<text v-if="this.$store.state.users"
					style="color: #0166fc; font-weight: bold; margin-top: 5px; font-size: 50rpx;">{{this.$store.state.users.usdt.toFixed(2)}}</text>
			</view>
		</view>

		<view
			:style="$store.state.bgColor=='black'?'display: flex; justify-content: center;margin-top:15px;border-radius: 5px;align-items: center; font-weight: bold;background: #ffffff; background: #22252F; color: #fff; height: 80px; width: 93%; margin: 0 auto;':'display: flex; justify-content: center;margin-top:15px;border-radius: 5px;align-items: center; font-weight: bold;background: #ffffff; height: 80px; width: 93%; margin: 0 auto;'">
			<view @click="gobanks()" style="flex: 1;  display: flex;  flex-direction: column; justify-content: center;align-items: center;">
				<text>{{i18n.bank_card}}</text>
				<text style="margin-top: 5px;">{{yhindexList.length}}</text>
			</view>
			<view
				:style="$store.state.bgColor=='black'?'height: 25px; width: 2px;  background: #1A1C24;':'height: 25px; width: 2px; background: #F0F0F0;'">
			</view>
			<view @click="godigital()"
				style="flex: 1;display: flex; flex-direction: column; justify-content: center;align-items: center;">
				<text>{{i18n.add}}{{i18n.digital_currency_address}}</text>
				<text style="margin-top: 5px;">{{indexList.length}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				indexList: [],
				yhindexList: []
			}
		},
		computed: {
			i18n() {
				return this.$t("wallet")
			}
		},
		onLoad() {
			this.getData()
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("mine.wallet")
			})
			if (this.$store.state.bgColor == 'black') {
				uni.setNavigationBarColor({
					frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
					backgroundColor: '#22252F'
				});
			}
		},
		methods: {
			getData() {


				var arr = []
				const res = this.$H.get('/api/v1/usdtInfo', false).then(res => {
					// this.loading=false
					if (res.type == "ok") {




						var arr = []



						res.data.map(item => {



							// let date = new Date(Date.parse(new Date(item.create_time)))
							// item.create_time = this.$u.test.formatDate(date, 'yyyy-MM-dd hh:mm')


							arr.push(item)



						})
						this.indexList = arr


					} else {

					}
				})

				var arryh = []
				const resyh = this.$H.get('/api/v1/cashInfo', false).then(res => {
					// this.loading=false
					if (res.type == "ok") {




						var arryh = []



						res.data.map(item => {




							arryh.push(item)



						})
						this.yhindexList = arryh


					} else {

					}
				})
			},
			godigital() {
				uni.navigateTo({
					url: "/pages/users/digital/digital"
				})
			},
			gobanks() {
				uni.navigateTo({
					url: "/pages/users/banks/banks"
				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #F7F8FA;
	}
</style>