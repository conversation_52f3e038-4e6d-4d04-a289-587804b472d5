<template>
	<view>


		<view v-for="(item,index) in messageList" :key="index"  @click="goArticle(item)" >
			<view style="height: 10px;"></view>
			<view :style="$store.state.bgColor=='black'?'display: flex; flex-direction: column; width: 95%; margin: 0 auto;  height: auto;background: #22252F;color: #fff;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1;':'display: flex; flex-direction: column; width: 95%; margin: 0 auto;  height: auto;background: #FFFFFF;box-shadow: 0px 0px 12px 0px rgba(0,33,123,0.16);border-radius: 6px 6px 6px 6px;opacity: 1;' ">

				<view
					style="display: flex;flex-direction: column;
			width: 90%; justify-content: space-between; height: auto;  margin: 0 auto;  margin-top: 15px; padding-bottom: 13px;">
					<text style="font-size: 14px; font-weight: bold; color: #A0A2A5;">{{item.create_time}}</text>

					<text
						style="margin-top: 10px; font-size: 12px;">{{item.title}}</text>
				</view>




			</view>
		</view>



	


	
	<view style="height: 15px;">
		
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				messageList: [],
				pages:1,
							status:false,
							loding:"",
							scrollH:0
			}
		},
		computed: {
			i18n() {
				return this.$t("service")
			}
		},
		onLoad() {
		
		  const _this = this
		  uni.setNavigationBarTitle({
		  	title:_this.$t("mine.announcement")
		  			
		  })
		  if(this.$store.state.bgColor=='black')
		  {
		  			  uni.setNavigationBarColor({
		  			    frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
		  			    backgroundColor: '#22252F' // 导航栏背景颜色
		  			  });
		  }
		},
		onShow() {
			  this.getData()
		},
		onReachBottom() {
			this.scrolltolower()
		},
		methods: {
			scrolltolower() {
							
							if(this.status)return false
							   this.pages+=1
							
								 this.getData()
						},
			goArticle(item){
				
				
				let urls ="/pages/article/article?item="+encodeURIComponent(JSON.stringify(item))
				if(urls.indexOf('%') > -1) {
				    urls = urls.replace(/%/g, '%25');
				}
				
				uni.navigateTo({
					url:urls
				})
			},
				getData() {
				this.status=true
				var arr = []
				const res =  this.$H.post('/api/v1/getAnnouncement',false,{
					language:uni.getStorageSync('lang') || 'en',
					page:this.page
				}).then(res => {
												  //  this.loading=false
									    if(res.type=="ok"){
											
											
											
											
											var arr = []
											
											
											if(res.data.data.length<=0)
											{
												this.status=false
												
												this.pages-=1
												return 
											}
											res.data.data.map(item=>{
												
												
												
												let date = new Date(Date.parse(new Date(item.create_time)))
												item.create_time = this.formatDate(date, 'yyyy-MM-dd')

												
												arr.push(item)
												
										
																			
											})
											
											if(this.messageList.length>0 && this.pages>1){
												this.messageList=[...this.messageList,...arr]
												
											}else{
												this.messageList = arr
											}
												this.status=false
											
														
													}else{
														
													}
									})
				
				
				
				
				
				
			},
			formatDate(date, fmt) {
				if (/(y+)/.test(fmt)) {
					fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
			}
				let o = {	
					'M+': date.getMonth() + 1,
					'd+': date.getDate(),
					'h+': date.getHours(),
					'm+': date.getMinutes(),
					's+': date.getSeconds()
				}
				for (let k in o) {
					if (new RegExp(`(${k})`).test(fmt)) {
						let str = o[k] + ''
						fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str))
					}
				}
				return fmt
			},
			padLeftZero(str) {
				return ('00' + str).substr(str.length)
			}
		}
	}
</script>

<style>
	page {
		background: #F7F8FA;
		overflow: initial;
		position: initial;
	}
</style>