<template>
	<view>
		<iframe src="https://alb.priectw.cc/chatlink.html" :width="width" :height="height" sandbox="allow-scripts allow-same-origin">
		</iframe>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				messageList: [],
				pages: 1,
				status: false,
				loding: "",
				scrollH: 0,
				src: 'https://test.lklee.org/', // 替换为你想要嵌入的外部网址
				width: window.innerWidth,
				height: window.innerHeight-44,
				kefu: "",
				storedLang: "",
				zxkefu: ""
			}
		},
		computed: {
			i18n() {
				return this.$t("service")
			},
			...mapState({
				lgStatus: state => state.lgStatus,
				users: state => state.users
			})
		},
		 mounted() {
			 
		  },
		onLoad() {
			
			const _this = this
			uni.setNavigationBarTitle({
				title: _this.$t("service.contact_customer_service")

			})
			// if (this.$store.state.bgColor == 'black') {
			// 	uni.setNavigationBarColor({
			// 		frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
			// 		backgroundColor: '#22252F' // 导航栏背景颜色
			// 	});
			// }
		},
		methods: {
			}
	}
</script>

<style>
	page {
		background: #F7F8FA;
	}
	iframe {
	    border: none; /* 移除边框 */
	    padding: 0; /* 移除内边距 */
	    margin: 0; /* 移除外边距 */
	    overflow: hidden; /* 隐藏溢出内容 */
	    display: block; /* 让它作为一个块级元素显示 */
	}
</style>