<template>
	<view>
	<scroll-view scroll-y class="DrawerWindow" :class="modalName=='viewModal'?'show':''">
		<view class="cu-list menu card-menu margin-top-xl margin-bottom-xl shadow-lg">
			<view class="cu-item arrow" v-for="(item,index) in language" :key="index" @click="setLang(item)"
				:class="{isactive : item.selected}">
				<view class="content">
					<image :src="item.img" style="width: 30px; height: 25px;"></image>
					<text class="text-grey" style="font-weight: bold;">{{item.name}}</text>
				</view>
			</view>
		</view>
	</scroll-view>	
	</view>
</template>
<script>
import {
		langs
	} from "@/common/language.js"
	export default {
		data() {
			return {
				formData: {
					"password": ""
				},
				types: "password",
				loading: false,
				tyloading: false,
				modalName: null,
				values: null,
				value: null,
				passwordShow: true,
				usestring: "",
				passwords: "",
				imgcode: "",
				imgkey: "",
				clangs: {},
				language: [],//语言
				mnUser: {}
			}
		},

		onLoad() {
			const _this = this
			_this.changeCodeImg();
			this.language = langs
			this.setLoads()
			this.setLgs()

		},
		onShow() {
			// window.addEventListener("popstate",this.setHistory,false)
			this.setLgs()
		},
		methods: {
			setHistory() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			setLoads() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					el.selected = false
					if (el.value == lgs) el.selected = true
					return el
				})
			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})
				this.language = langs
				this._i18n.locale = item.value
				localStorage.setItem('langs', item.value);
				uni.setStorageSync('lang', item.value)
				this.$utils.setTabbar(this)
				this.clangs = item
				this.hideModal()
				// this.$store.commit('setLang', item.value)

				setTimeout(() => {
					uni.navigateBack()
				}, 200)
			},
			
			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
			},
			hideModal(e) {
				this.modalName = null

			},
			changeCodeImg() {

				this.$H.get('/api/v1/verification?' + Math.random(), false).then(result => {


					this.imgcode = result.img
					this.imgkey = result.key
				})
			},
			goregister() {
				uni.navigateTo({
					url: "/pages/register/register"
				})
			},
			goresetPass() {
				uni.navigateTo({
					url: "/pages/resetPass/resetPass"
				})
			},
			backs() {
				uni.navigateTo({
					url: '/pages/index/index'
				})
			}

		},
		computed: {
			i18n() {
				return this.$t("login")
			},
			suffixIcon() {
				console.log('gggggggggggg')
				if (this.passwordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			}
		}
	}
</script>

<style>

</style>
