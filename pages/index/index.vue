<template>
	<view style="display: flex; flex-direction: column;" data-theme="pink"
		:class="$store.state.bgColor=='black'?'blacks':'whiles'">

		<scroll-view class="DrawerPage" style="" :class="modalName=='viewModal'?'show':''">
			<view v-if="$store.state.bgColor=='black'"
				style="display: flex;padding: 20rpx 12px 0;  align-items: center;  justify-content: space-between;  border-bottom: 2px solid #22252F;">
				<!-- <view style="width: 124px;
						opacity: 1; width: 40px; padding: 2px 0px; ; align-items: center;" @click="showModal" data-target="viewModal">
					<view>
						<image :src="clangs.img" style="width:25px; height: 20px;"></image>
					</view>
				</view> -->
				<view class="imgClass" data-target="viewModal">
					<u-image :src="$store.state.bgColor=='black'?'../../static/<EMAIL>':'../../static/logo.png'"
						width="320" height="60"></u-image>
				</view>
				<!-- <u-subsection v-if="$store.state.bgColor=='black'" :list="navs" inactiveColor="#828397"
					activeColor="#ffffff" :current="currentNav" style="flex:1; color: #828397;"
					@change="sectionChange"></u-subsection> -->
				<!--
				<u-subsection v-if="$store.state.bgColor=='while'" :list="navs" :current="currentNav"
					style="flex:1; color: #828397;" @change="sectionChange"></u-subsection> -->
				<uni-icons v-if="$store.state.bgColor=='black'" type="search" @click="searchs()" style="color: #fff;"
					size="30"></uni-icons>
				<uni-icons v-if="$store.state.bgColor=='while'" type="search" @click="searchs()" size="30"></uni-icons>
			</view>

			<!-- 导航栏 -->
			<view v-if="$store.state.bgColor=='while'"
				style=" display: flex;  margin: 20rpx 12px 0; justify-content: space-between; align-items: center; ">

				<view style="width: 124px;
						opacity: 1; width: 40px; padding: 2px 0px; ; display: flex; justify-content: center; align-items: center;"
					@click="showModal" data-target="viewModal">
					<view>
						<image :src="clangs.img" style="width:25px; height: 20px;"></image>
					</view>
				</view>
				<view class="imgClass" data-target="viewModal">
					<u-image :src="$store.state.bgColor=='black'?'../../static/<EMAIL>':'../../static/logo.png'"
						width="320" height="60"></u-image>
				</view>
				<u-subsection v-if="$store.state.bgColor=='black'" :list="navs" inactiveColor="#828397"
					activeColor="#ffffff" :current="currentNav" style="flex:1; color: #828397;"
					@change="sectionChange"></u-subsection>
				<!--
				<u-subsection v-if="$store.state.bgColor=='while'" :list="navs" :current="currentNav"
					style="flex:1; color: #828397;" @change="sectionChange"></u-subsection> -->
				<uni-icons type="search" @click="searchs()" size="30"></uni-icons>

			</view>


			<view v-show="currentNav == 0" class="jypzs">
				<view v-if="$store.state.bgColor=='black'"
					style="width: 100%;overflow: hidden;background: #1A1C24 ;  overflow-x: scroll;white-space: nowrap;  ">
					<view style="margin-top: 8px; " v-for="(item, index) in dataList"
						:class="tabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="itemCliks(item)">
						{{item.name}}
					</view>
				</view>
				<view v-if="$store.state.bgColor=='while'"
					style="width: 100%;overflow: hidden;background: #fff ;  overflow-x: scroll;white-space: nowrap;  ">
					<view style="margin-top: 8px; " v-for="(item, index) in dataList"
						:class="tabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="itemCliks(item)">
						{{item.name}}
					</view>
				</view>

				<view class="u-page" style="margin-top: 0px;">
					<u-list class="sfds" style="  width: 100%;overflow: hidden; ">
						<u-list-item v-if="resData" v-for="(item, index) in resData" :key="index">
							<!-- <u-cell :title="`列表长度-${index + 1}`" class="u-cell__title-texts" >
							<u-avatar slot="icon" shape="square" size="35"  :src="item.url"
								customStyle="margin: -3px 5px -3px 0"></u-avatar>

						</u-cell> -->
							<u-cell style="padding: 0px 5px !important; border-bottom: 1px solid #22252F;"
								v-if="$store.state.bgColor=='black'">
								<!-- <u-avatar  v-if="item.isCollect==1" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
							<u-avatar  v-if="item.isCollect==0" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
								<u-avatar slot="icon" shape="square" size="55"
									:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo"
									customStyle="margin: -3px 1px -3px 0"></u-avatar>

								<view slot="title" v-if="$store.state.bgColor=='while'"
									style="display: flex;margin-left:5px; justify-content: center;align-items: center;  font-weight: bold;"
									@click="clickItems(item)">

									<text class="u-cell-text"
										style="text-align:left; font-size: 13px; font-weight: bold; flex:1; ">{{item.show_name}}</text>
									<text class="u-cell-text" v-if="item.currency.opening==1"
										style="flex: 1; justify-content: center; text-align: center;font-size: 13px; font-weight: bold; padding-left: 10px;">{{Number(item.now_price) }}</text>

									<text class="u-cell-text" v-if="item.currency.opening==0"
										style=" justify-content: center; text-align: left;font-size: 13px; font-weight: bold;    padding-left: 25px;">0</text>
									<text
										style="border: 1px solid #E8EBF4; color: #Ah7A9C4; width: 89px; height: 30px; line-height: 30px; text-align: center;"
										v-if="item.currency.opening==0">{{$t("market.closed")}}</text>

								</view>

								<view slot="title" v-if="$store.state.bgColor=='black'"
									style="padding: 5px 5px;display: flex;margin-left:5px; justify-content: center;align-items: center; color: #fff; font-weight: bold;"
									@click="clickItems(item)">
									<!-- 币种数据 暗 -->
									<text class="u-cell-text"
										style="text-align:left; font-size: 13px; font-weight: bold; flex:1; ">{{item.show_name}}</text>
									<view class="u-cell-text" v-if="item.currency.opening==1"
										style="flex: 1;display: flex; flex-direction: column; align-items: center;font-size: 12px;color: #ffffff; font-weight: bold; padding-left: 10px;">
										{{Number(item.now_price) }}
										<view class="u-cell-text" v-if="$utils.getColor(item.change)==12"
											style="color: #0166fc;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==12" size="30"
												src="../../static/Up_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
										<view v-if="$utils.getColor(item.change)==13" class="u-cell-text"
											style="color: #EF263D;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==13" size="30"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
										<view v-if="$utils.getColor(item.change)==14" class="u-cell-text"
											style="color: #EF263D;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==14" size="30"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
									</view>

									<text class="u-cell-text" v-if="item.currency.opening==0"
										style="flex: 1; justify-content: center; text-align: left;    padding-left: 25px;">0</text>
									<text
										style="border: 1px solid #E8EBF4; color: #A7A9C4; width: 89px; height: 30px; line-height: 30px; text-align: center;"
										v-if="item.currency.opening==0">{{$t("market.closed")}}

									</text>

									<view
										style="flex:1;text-align:right;display: flex;justify-content: center;align-items: center;"
										v-if="item.currency.opening==1">

									</view>

								</view>

							</u-cell>
							<u-cell style="padding: 5px 5px !important; box-shadow: 0 0 6.4vw rgba(91, 91, 91, 0.1);"
								v-if="$store.state.bgColor=='while'">
								<u-avatar slot="icon" shape="square" size="60"
									:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo"
									customStyle="margin: -3px 1px -3px 0"></u-avatar>
								<view slot="title" v-if="$store.state.bgColor=='while'"
									style="display: flex;margin-left:5px; justify-content: center;align-items: center;  font-weight: bold;"
									@click="clickItems(item)">

									<text class="u-cell-text"
										style="text-align:left; font-size: 15px; flex:1; color: #1a222b; font-weight: 300;">{{item.show_name}}</text>
									<view class="u-cell-text" v-if="item.currency.opening==1"
										style="flex: 1;display: flex; flex-direction: column; align-items: center;font-size: 12px;color: #747474;  padding-left: 10px;">
										<!-- 币种数据 光 -->
										<view class="u-cell-text" style="height: 25px;"> {{Number(item.now_price) }}
										</view>
										<view class="u-cell-text" v-if="$utils.getColor(item.change)==12"
											style="color: #0166fc;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==12" size="30"
												src="../../static/Up_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
										<view v-if="$utils.getColor(item.change)==13" class="u-cell-text"
											style="color: #EF263D;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==13" size="30"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
										<view v-if="$utils.getColor(item.change)==14" class="u-cell-text"
											style="color: #EF263D;flex:1;display: flex;align-items: center; ">
											{{item.change}}%
											<!-- <u-avatar v-if="$utils.getColor(item.change)==14" size="30"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
										</view>
									</view>
									<!--
									<text class="u-cell-text" v-if="item.currency.opening==0"
										style="flex: 1; justify-content: center; text-align: left;font-size: 13px; font-weight: bold; padding-left: 25px;">0</text> -->
									<text
										style="background-color: #cbcbcb; color: #ffffff; width: 89px;border-radius: 5px; height: 22px; line-height: 22px;font-size: 12px; text-align: center; "
										v-if="item.currency.opening==0">{{$t("market.closed")}}</text>
									<!-- 画图 -->
									<view style="flex:1.4;text-align:right ;display: flex; justify-content: flex-end;">
										<div :key="item.show_name" :id="item.show_name+'main'"
											style="width: 100px;height: 24px; ">
										</div>
									</view>
								</view>

								<view slot="title" v-if="$store.state.bgColor=='black'"
									style="display: flex;margin-left:5px; justify-content: center;align-items: center; color: #fff; font-weight: bold;"
									@click="clickItems(item)">

									<text class="u-cell-text"
										style="text-align:left; font-size: 13px; font-weight: bold; flex:1; ">{{item.show_name}}</text>
									<text class="u-cell-text" v-if="item.currency.opening==1"
										style="flex: 1; justify-content: center; text-align: center;font-size: 13px; font-weight: bold; padding-left: 10px;">{{Number(item.now_price) }}</text>

									<text class="u-cell-text" v-if="item.currency.opening==0"
										style="flex: 1; justify-content: center; text-align: left;    padding-left: 25px;">0</text>
									<text
										style="border: 1px solid #E8EBF4; color: #A7A9C4; width: 89px; height: 30px; line-height: 30px; text-align: center;"
										v-if="item.currency.opening==0">{{$t("market.closed")}}</text>

									<view style="flex:1;text-align:right" v-if="item.currency.opening==1">
										<text class="u-cell-text" v-if="$utils.getColor(item.change)==12"
											style="color: #0166fc;flex:1;">{{item.change}}%</text>
										<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
											style="color: #EF263D;flex:1;">{{item.change}}%</text>
										<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
											style="color: #EF263D;flex:1;">{{item.change}}%</text>


									</view>

								</view>
								<!-- <view v-if="item.currency.opening==1">
									<u-avatar v-if="$utils.getColor(item.change)==12" size="35"
										src="../../static/Up_S <EMAIL>"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
									<u-avatar v-if="$utils.getColor(item.change)==13" size="35"
										src="../../static/Down_S <EMAIL>"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
									<u-avatar v-if="$utils.getColor(item.change)==14" size="35"
										src="../../static/Down_S <EMAIL>"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
								</view> -->

							</u-cell>

						</u-list-item>
					</u-list>
				</view>


			</view>

			<view v-show="currentNav == 1" class="jypzs">
				<!-- <block v-if="dataList">
			<view v-if="$store.state.bgColor=='black'"  style="margin-top: 18px;background: #1A1C24 ;border: 1px solid #4A5258; color: #fff;"   v-for="(item, index) in dataList" :class="sctabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="scitemCliks(item)">{{item.name}}</view>
			<view v-if="$store.state.bgColor=='while'"  style="margin-top: 18px;"   v-for="(item, index) in dataList" :class="sctabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="scitemCliks(item)">{{item.name}}</view>

			</block> -->

				<view v-if="$store.state.bgColor=='black'"
					style="width: 100%;overflow: hidden;background: #1A1C24 ;  overflow-x: scroll;white-space: nowrap;  ">
					<!-- <view  style="margin-top: 18px; background: #1A1C24;border: 1px solid #4A5258; color: #fff;"  v-for="(item, index) in dataList" :class="sctabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="scitemCliks(item)">{{item.name}}</view> -->

				</view>

				<view v-if="$store.state.bgColor=='while'"
					style="width: 100%;overflow: hidden;background: #fff ;  overflow-x: scroll;white-space: nowrap;  ">

					<!-- <view  style="margin-top: 18px; "  v-for="(item, index) in dataList" :class="sctabsIndexs==item.name?'cdhbts':'dhbts'" :key="index" @click="scitemCliks(item)">{{item.name}}</view> -->

				</view>


				<view class="u-page" style=" margin-top: 5px;">
					<u-list class="sfds" style="  width: 100%;overflow: hidden; height: 100% ">
						<block v-if="sctabsIndexs==''">
							<u-list-item v-if="item.isCollect==1" v-for="(item, index) in resData" :key="index">
								<!-- <u-list-item > -->
								<!-- <u-cell :title="`列表长度-${index + 1}`" class="u-cell__title-texts" >
							<u-avatar slot="icon" shape="square" size="35"  :src="item.url"
								customStyle="margin: -3px 5px -3px 0"></u-avatar>

						</u-cell> -->
								<u-cell style="padding: 0px 5px !important;border-bottom: 1px solid #22252F; "
									v-if="$store.state.bgColor=='black'">

									<!-- 	<u-avatar v-if="item.isCollect==1" style="display: none;" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" style="display: none;" customStyle="margin: -3px 1px -3px 0"></u-avatar>
							<u-avatar v-if="item.isCollect==0" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
									<u-avatar slot="icon" shape="square" size="55"
										:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
									<view slot="title"
										style="display: flex; margin-left:5px; color: #fff;justify-content: center; align-items: center; text-align: center;"
										@click="clickItems(item)">


										<text class="u-cell-text"
											style="text-align:left;flex:1">{{item.show_name}}</text>


										<text class="u-cell-text" v-if="item.currency.opening==1"
											style="flex: 1; justify-content: center; text-align: center;font-size: 13px; font-weight: bold; padding-left: 10px;">{{Number(item.now_price) }}</text>

										<text class="u-cell-text" v-if="item.currency.opening==0"
											style="flex: 1; justify-content: center; text-align: left;font-size: 13px; font-weight: bold; padding-left: 25px;">0</text>
										<text
											style="border: 1px solid #E8EBF4; color: #A7A9C4; width: 89px; height: 30px; line-height: 30px; text-align: center;    "
											v-if="item.currency.opening==0">{{$t("market.closed")}}</text>





										<view style="flex: 1; text-align: right;" v-if="item.currency.opening==1">
											<text class="u-cell-text" v-if="$utils.getColor(item.change)==12"
												style="color: #0166fc; flex: 1;text-align:right">{{item.change}}%</text>
											<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
												style="color: #EF263D;flex: 1;text-align:right">{{item.change}}%</text>
											<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
												style="color: #EF263D;flex: 1;text-align:right">{{item.change}}%</text>
										</view>
										<!-- <view v-if="item.currency.opening==1">
											<u-avatar v-if="$utils.getColor(item.change)==12" size="35"
												src="../../static/Up_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
											<u-avatar v-if="$utils.getColor(item.change)==13" size="35"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
											<u-avatar v-if="$utils.getColor(item.change)==14" size="35"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
										</view> -->
									</view>




								</u-cell>

								<u-cell style="padding: 0px 5px !important;border-bottom: 1px solid rgb(214, 215, 217);"
									v-if="$store.state.bgColor=='while'">

									<!-- <u-avatar v-if="item.isCollect==1" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
							<u-avatar v-if="item.isCollect==0" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
									<u-avatar slot="icon" shape="square" size="55"
										:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
									<view slot="title"
										style="display: flex; justify-content: center; align-items: center; margin-left:5px; "
										@click="clickItems(item)">


										<text class="u-cell-text"
											style="text-align:left;flex:1">{{item.show_name}}</text>
										<text class="u-cell-text" v-if="item.currency.opening==1"
											style="flex: 1; justify-content: center; text-align: center;font-size: 13px; font-weight: bold; padding-left: 10px;">{{Number(item.now_price) }}</text>

										<text class="u-cell-text" v-if="item.currency.opening==0"
											style="flex: 1; justify-content: center; text-align: left;font-size: 13px; font-weight: bold; padding-left: 25px;">0</text>
										<text
											style="border: 1px solid #E8EBF4; color: #A7A9C4; width: 89px; height: 30px; line-height: 30px; text-align: center;    "
											v-if="item.currency.opening==0">{{$t("market.closed")}}</text>
										<view style="flex: 1; text-align: right;" v-if="item.currency.opening==1">
											<text class="u-cell-text" v-if="$utils.getColor(item.change)==12"
												style="color: #0166fc;flex: 1;text-align:right">{{item.change}}%</text>
											<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
												style="color: #EF263D;flex: 1;text-align:right">{{item.change}}%</text>
											<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
												style="color: #EF263D;flex: 1;text-align:right">{{item.change}}%</text>
										</view>
										<!-- <view v-if="item.currency.opening==1">
											<u-avatar v-if="$utils.getColor(item.change)==12" size="35"
												src="../../static/Up_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
											<u-avatar v-if="$utils.getColor(item.change)==13" size="35"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
											<u-avatar v-if="$utils.getColor(item.change)==14" size="35"
												src="../../static/Down_S <EMAIL>"
												customStyle="margin: -3px 1px -3px 0"></u-avatar>
										</view> -->
									</view>
								</u-cell>
								<u-cell>
									<view style="height: 50px;"></view>
								</u-cell>
							</u-list-item>
						</block>


						<block v-if="sctabsIndexs!=''">
							<u-list-item v-if="item.isCollect==1" v-for="(item, index) in scresData" :key="index">
								<!-- <u-list-item > -->
								<!-- <u-cell :title="`列表长度-${index + 1}`" class="u-cell__title-texts" >
							<u-avatar slot="icon" shape="square" size="35"  :src="item.url"
								customStyle="margin: -3px 5px -3px 0"></u-avatar>

						</u-cell> -->
								<u-cell class="jdgs" style="padding: 0px 5px !important;">

									<!-- 	<u-avatar v-if="item.isCollect==1" style="display: none;" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
							<u-avatar v-if="item.isCollect==0" style="display: none;" @click="collects(item)" slot="icon" shape="square" size="55"
								src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
									<u-avatar slot="icon" shape="square" size="55"
										:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo"
										customStyle="margin: -3px 1px -3px 0"></u-avatar>
									<view slot="title" style="display: flex;  " @click="clickItems(item)">

										<text style="flex:1" class="u-cell-text">{{item.show_name}}</text>
										<text class="u-cell-text"
											style="flex: 1; justify-content: center; text-align: center;">{{Number(item.now_price) }}</text>
										<text class="u-cell-text" v-if="$utils.getColor(item.change)==12"
											style="color: #0166fc;flex: 1;text-align:right">{{item.change}}%</text>
										<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
											style="color: #EF263D;flex: 1;text-align:right">{{item.change}}%</text>
										<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
											style="color: #EF263D;flex: 1; text-align:right">{{item.change}}%</text>

									</view>

									<!-- 	<u-avatar v-if="$utils.getColor(item.change)==12" slot="right-icon" shape="square" size="35"
								src="../../static/Up_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
							<u-avatar v-if="$utils.getColor(item.change)==13" slot="right-icon" shape="square" size="35"
								src="../../static/Down_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
								<u-avatar v-if="$utils.getColor(item.change)==14" slot="right-icon" shape="square" size="35"
									src="../../static/Down_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
								</u-cell>
								<u-cell>
									<view style="height: 50px;"></view>
								</u-cell>
							</u-list-item>
						</block>

					</u-list>
				</view>


			</view>
		</scroll-view>
		<view class="DrawerClose" :class="modalName=='viewModal'?'show':''" @tap="hideModal">
			<text class="cuIcon-pullright"></text>
		</view>


		<tab-bar style="height: 100px;"></tab-bar>
		<!-- <view class="">
			<u-modal :show="show" :title="title" :content='content' @confirm="confirm" ref="uModal" :asyncClose="true">
				<template slot='confirmButton'>
					<u-button @click="notClick" style="background-color: rgb(1 102 252); color: #fff; width: 100%;"
						:disabled="disableds" :text="values"></u-button>
				</template>

			</u-modal>
		</view> -->
	</view>
</template>

<script>
	// import colorGradient from 'uview-ui/libs/function/colorGradient';
	import {
		langs
	} from "@/common/language.js"
	import {
		mapState
	} from 'vuex'
	import * as echarts from 'echarts';

	// var chartDom = document.getElementById('main');
	// var myChart = echarts.init(chartDom);
	// var option;
	export default {

		data() {
			return {
				navs: [],
				currentNav: 0,
				disableds: true,
				src: '../../static/<EMAIL>',
				wheight: 0,

				urls: [],
				indexList: [],
				usdt: '',
				countdown: 10,
				values: '',
				scindexList: [],
				quotationOriginal: [],
				quotation: null,
				modalName: null,
				resData: [],
				scresData: [],
				mLists: null,
				dataList: [],
				sctabsIndexs: "",
				tabsIndexs: "",
				lang: null,
				clangs: {},
				language: [],
				show: false,
				chart: null, //图
				title: 'Welcome to fx7',
				content: 'This platform is only used for demonstration, communication, and learning purposes, and is not for commercial use. No recharge transactions are allowed ' +
					'\n\n Due to policy reasons.services are not provided to North Korea, Israel, China,Vanuatu,and Cuba.'
			}
		},
		computed: {
			i18n() {
				return this.$t("market")
			},
			...mapState({
				socket: state => state.socket,
				token: state => state.token,
				users: state => state.users
			})

		},
		mounted() {
			// setTimeout(() => {
			// this.ongitinitChart(); // 调用你的函数
			// }, 2000)
			// this.initChart();
			// this.$nextTick(() => {
			// 	const divs = document.querySelectorAll('[v-if="$utils.getColor(item.change)==12"]');
			// 	divs.forEach(div => {
			// 		const id = div.id;
			// 		this.initChart('BTC/USDT', 1); // 调用你的函数
			// 	});
			// });

		},
		updated() {
			// this.ongitinitChart(); // 调用你的函数
		},
		beforeDestroy() {
			if (this.chart) {
				this.chart.dispose(); // ECharts 5.x 之后版本提供了 dispose 方法来清理资源
			}
		},
		unmounted() {
			dispose('chart')
		},

		onLoad() {
			//  setInterval(() => {
			// this.loadmore();
			//  }, 1000)

			// this.myLists();
			//    this.sertNavs()
			// 	this.loadmore();
			// this.language=langs

			// uni.setBackgroundColor({
			// 	backgroundColor: "#1A1C24"
			// })
			// uni.getSystemInfo({
			// 	success: function(e) {
			// 		uni.setBackgroundColor({
			// 			backgroundColor: "#1A1C24"
			// 		})
			// 	}
			// })
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
			getApp().setUser()
			this.language = langs
			this.setLgs()
			this.sertNavs()
			this.$utils.setTabbar(this)
			this.loadmore()
			// this.ongitinitChart(); // 调用你的函数

			// uni.setBackgroundColor({
			// 	backgroundColor: "#1A1C24"
			// })
			uni.getSystemInfo({
				success: function(e) {
					// uni.setBackgroundColor({
					// 	backgroundColor: "#1A1C24"
					// })
				}
			})

		},

		onShow() {
			this.$store.commit('changeTabbarIndex', 0)
			if (!uni.getStorageSync('isLoads') && uni.getStorageSync('token')) {
				uni.hideTabBar()
				this.show = true;
				this.values = 'Accept (countdown' + this.countdown + 'second)'
				this.timer = setInterval(() => {
					this.values = 'Accept (countdown' + this.countdown + 'second)'
					if (this.countdown <= 0) {
						clearInterval(this.timer);
						this.timer = null;
						this.values = 'Accept'
						this.disableds = false
						return;
					}
					this.countdown--;

				}, 1000);
				uni.setStorageSync('isLoads', 'not')

			}

			this.setLgs()
			this.sertNavs()
			this.$utils.setTabbar(this)
			this.ongitinitChart(); // 调用你的函数
		},
		onHide() {
			// this.socket.removeListener('daymarket')

			// window.addEventListener("popstate", function(){

			//       uni.switchTab({

			//         url: '/pages/index/index'

			//       });

			//     }, false)

		},

		onUnload() {
			this.socket.removeListener('daymarket')
		},
		watch: {
			currentNav: function(val) {
				if (val == 1) {

					this.loadmore()
				}
			},
			//当语言发生变化时
			'$store.state.lang'(val) {

				const {
					i18n
				} = this

				this.navs = this.$t("nav")
				// 	 const i18n = this.$t("nav")
				//      const i18ns = this.$t("home")
				// console.log(uni.getStorageSync('lang'))
				// 	console.log(i18n[0])
				// 	console.log(i18ns.app_text)

				// 	setTimeout(() => {

				// 		this.navs=i18n
				// 	}, 5200)






				// this.$utils.setTabbar(this)
				// this.setDefaultLang()

				// this.navs.forEach(item=>{
				// 	item = i18n[item.title]
				// })
			}
		},
		methods: {
			notClick() {
				uni.showTabBar()
				this.show = false
			},
			// showModal() {
			// 	this.show = true;
			// },
			confirm() {
				setTimeout(() => {
					// 3秒后自动关闭
					this.show = false;
				}, 3000)
			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})
			},
			sertNavs() {
				this.navs = []
				const i18n = this.$t("market")
				this.navs.push(i18n.traded_item)
				this.navs.push(i18n.my_fav)
			},
			setLang(item) {

				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})

				this.language = langs
				this._i18n.locale = item.value

				uni.setStorageSync('lang', item.value)
				this.sertNavs()
				this.$utils.setTabbar(this)

				this.clangs = item
				this.hideModal()
			},
			collects(item) {
				// let iscoll=item.isCollect===1? 0 :1;
				const _this = this
				this.auths(() => {
					if (item.isCollect) {
						item.isCollect = 0
						const res = this.$H.post('/api/v1/optional/del', false, {
							currency_id: item.currency_id
						}).then(res => {

							if (res.type == "ok") {
								this.$nextTick(() => {
									item.isCollect = 0

								})
							}

						})
					} else {
						item.isCollect = 1
						const res = this.$H.post('/api/v1/optional/add', false, {
							currency_id: item.currency_id
						}).then(res => {

							if (res.type == "ok") {
								this.$nextTick(() => {
									item.isCollect = 1

								})
							}

						})
					}


				})
			},
			startSocket() {
				//修改成60min数据
				const _this = this
				this.socket.on('kline', res => {
					if(res.period == '60min'){
						const has = _this.resData.findIndex(item => item.currency_id == res.currency_id)
						if (has > -1) {
							const current = _this.resData[has]
							const isJPStock = current && current.currency && current.currency.currency_type == 2
							if (isJPStock) {
								if (!res.api_form || res.api_form !== 'jpstock_1min') {
									return
								}
							}
							const item = {
								..._this.resData[has],
								...res
							}
							_this.resData.splice(has, 1, item)
						}
						if (_this.scresData.length > 0) {
							const schas = _this.scresData.findIndex(item => item.currency_id == res.currency_id)
							if (schas > -1) {
								const currentSc = _this.scresData[schas]
								const isJPStockSc = currentSc && currentSc.currency && currentSc.currency.currency_type == 2
								if (isJPStockSc) {
									if (!res.api_form || res.api_form !== 'jpstock_1min') {
										return
									}
								}
								const scitem = {
									..._this.scresData[schas],
									...res
								}
								_this.scresData.splice(schas, 1, scitem)
							}
						}
					}
				})
				this.ongitinitChart()
				
				return
				// 避免重复监听导致多次覆盖
				this.socket.off && this.socket.off('daymarket')
				this.socket.on('daymarket', res => {
					const has = _this.resData.findIndex(item => item.currency_id == res.currency_id)

					if (has > -1) {

						const current = _this.resData[has]
						const isJPStock = current && current.currency && current.currency.currency_type == 2
						if (isJPStock) {
							if (!res.api_form || res.api_form !== 'jpstock_1min') {
								return
							}
						}

						const item = {
							..._this.resData[has],
							...res
						}

						_this.resData.splice(has, 1, item)
					}


					if (_this.scresData.length > 0) {
						const schas = _this.scresData.findIndex(item => item.currency_id == res.currency_id)

						if (schas > -1) {

							const currentSc = _this.scresData[schas]
							const isJPStockSc = currentSc && currentSc.currency && currentSc.currency.currency_type == 2
							if (isJPStockSc) {
								if (!res.api_form || res.api_form !== 'jpstock_1min') {
									return
								}
							}

							const scitem = {
								..._this.scresData[schas],
								...res
							}

							_this.scresData.splice(schas, 1, scitem)
						}
					}
				})
				this.ongitinitChart()
			},
			itemCliks(items) {
				// this.ongitinitChart()
				if (this.tabsIndexs == items.name) {
					this.tabsIndexs = ""
					this.loadmore()
					return
				}
				this.tabsIndexs = items.name
				const has = this.dataList.findIndex(item => item.id == items.id)
				if (has > -1) {
					const item = {
						...this.dataList[has],
					}
					this.resData = item.quotation
				}
				this.ongitinitChart()
			},
			scitemCliks(items) {
				if (this.sctabsIndexs == items.name) {
					this.sctabsIndexs = ""
					this.loadmore()
					return
				}
				this.sctabsIndexs = items.name
				if (has > -1) {
					const item = {
						...this.dataList[has],
					}
					this.scresData = item.quotation
				}
			},
			searchs() {
				uni.navigateTo({
					url: "/pages/search/search"
				})
			},
			sectionChange(index) {
				this.currentNav = index;
			},
			scrolltolower() {
				this.loadmore()
			},
			myLists() {
				const _this = this
				const restwo = this.$H.get('/api/v1/optional/list', false).then(restwo => {
					this.mLists = restwo.data;

					for (var i = 0; i < this.mLists.length; i++) {
						const has = _this.resData.findIndex(item => item.currency_id == this.mLists[i].currency_id)

						if (has > -1) {
							var iscollect = {
								"isCollect": 1
							};
							const item = {
								..._this.resData[has],
								...iscollect
							}
							_this.resData.splice(has, 1, item)

						} else {
							var iscollect = {
								"isCollect": 0
							};
							const item = {
								..._this.resData[has],
								...iscollect
							}
							_this.resData.splice(has, 1, item)

						}
					}
				})
			},
			clickItems(item) {
				uni.setStorageSync('currency', {
					currency_id: item.currency_id,
					currency_name: item.currency_name,
					legal_name: item.legal_name,
					alias: item.currency.alias
				})
				this.$store.commit('setCurrency', {
					"currency_id": item.currency_id,
					"currency_name": item.currency_name,
					"item.legal_name": item.legal_name,
					"currentIndex": item.currentIndex,
					"opening": item.currency.opening
				})
				// this.$utils.jump('/pages/stock/stock', 'switchTab')
				uni.navigateTo({
					url: '/pages/stock/stock'
				})
			},
			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
			},
			hideModal(e) {
				this.modalName = null
				uni.showTabBar()
			},
			loadmore() {
				this.$H.get('/api/v1/optional/list', false).then(restwo => {
					this.mLists = restwo.data;
					this.resData = [];
					
					this.$H.get('/api/v1/quotation_new', false).then(res => {
						this.dataList = res.data
						for (var i = 0; i < res.data.length; i++) {
							res.data[i].quotation.map(item => {
								// item.precision_length = this.$utils.getPrecisionLength(item.now_price)
								item.precision_length = this.$utils.getPrecisionLength(item.now_price)
								item.currentIndex = i
								const has = this.mLists.findIndex(items => items.currency_id == item.currency_id)
								if (has > -1) {
									item.isCollect = 1
								} else {
									item.isCollect = 0
								}
								this.quotationOriginal.push(item)
								this.resData.push(item)
							})
						}
						this.resData = this.resData.sort(function(a, b) {
							return b.currency.sort - a.currency.sort
						})
						console.log(939,this.resData[2].now_price)
						this.startSocket();
					})
				})

				this.quotation = this.resData
				this.indexList = [];
				this.scindexList = this.indexList;
			},
			// 拿到图形数据
			ongitinitChart() {
				// 假设 this.resData 是一个包含多个元素的数组
				const promises = this.resData.map(element => {
						let froms = Date.parse(new Date()) / 1000 - 15 * 24 * 60 * 60
						let tos = Date.parse(new Date()) / 1000
						const data = {
							symbol: element.currency_name + '/' + element.legal_name,
							period: '1day',
							from: froms,
							to: tos
						}
						return this.$H.post('/api/v1/klineMarketHome', true, data);
					}
				);

				Promise.all(promises)
					.then(results => {
						// results 是一个包含所有请求响应的数组
						results.forEach((response, index) => {
							// 处理每个响应，例如调用 initChart 函数
							const lit = response.data.data.data
							let mean
							const litS = lit.map((element, i) => {
								return element.open
							}, [])
							let Color = 1
							if (this.resData[index] && this.resData[index].change !== undefined) {
								if (this.$utils.getColor(this.resData[index].change) == 12) {
									Color = 2;
								}
							}
							function findMedian(numbers) {
								// 计算总和
								const sum = numbers.reduce((accumulator, currentValue) => accumulator +
									currentValue, 0);
								// 计算平均数
								const average = sum / numbers.length;
								return average;
							}
							const median = findMedian(litS);
							if (this.resData[index] && this.resData[index].show_name !== undefined) {
								this.initChart(this.resData[index].show_name, Color, litS, median);
							}
						});
					})
					.catch(error => {
						console.error('An error occurred:', error);
					});
			},
			initChart(name, Color, chartData, median) {
				this.chart = echarts.init(document.getElementById(name + 'main'));
				let option = {
					xAxis: [{
						type: 'category',
						boundaryGap: false,
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							show: false
						},
						splitLine: {
							show: false
						}
					}],
					yAxis: [{
						type: 'value',
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							show: false
						},
						splitLine: {
							show: false
						},
						// min: 0,
						// max: Math.max(...chartData)*2,
						scale: true, // 设置scale为true，允许y轴根据数据自动缩放
						boundaryGap: ['430%', '530%'], // 设置boundaryGap为百分比，增加y轴两端的空白区域
					}],
					series: [{
						name: 'Line with Shadow',
						type: 'line',
						smooth: false,
						lineStyle: {
							width: 1,
							color: Color === 2 ? '#0166fc' : '#ff0004'
						},
						showSymbol: false,
						areaStyle: {
							// 使用半透明的渐变色来模拟阴影
							opacity: 0.2,
							color: new echarts.graphic.LinearGradient(
								0, 0, 0, 1,
								[{
										offset: 0,
										color: Color === 2 ? '#0166fc' : '#ff0004',
									}, // 阴影顶部颜色
									{
										offset: 1,
										color: 'rgba(255, 255, 255, 0.0)'
									} // 阴影底部逐渐消失
								],
								false
							)
						},
						emphasis: {
							focus: 'series'
						},
						data: chartData
					}]
				};


				this.chart.setOption(option);
			},

		},

	}
</script>

<style>
	/deep/.jypzs .u-cell__title {
		flex: 1;
		line-height: 20px;
	}

	.jypzs {
		color: #fff;
	}

	.sfds {
		color: #fff;
		padding-bottom: 100rpx;
	}

	/deep/.u-line {
		vertical-align: middle;
		border-bottom: 0 !important;
	}

	.dhbts {
		display: inline-block;
		/* background: #fff; */
		margin: 5px;
		width: 70px;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;
		background-color: rgb(243 244 246 / var(--tw-bg-opacity));
		font-weight: 700 !important;
		--tw-text-opacity: 1 !important;
		color: rgb(0 0 0 / var(--tw-text-opacity));
		border-radius: 8vw !important;
		text-align: center;
	}

	.cdhbts {
		display: inline-block;
		background: #0166fc !important;
		margin: 5px;
		width: 70px;
		height: 25px;
		line-height: 25px;
		--tw-bg-opacity: 1 !important;

		font-weight: 700 !important;
		--tw-text-opacity: 1 !important;
		color: #ffffff;
		border-radius: 8vw !important;
		text-align: center;
	}

	/deep/.u-cell__title {
		flex: 1;
		line-height: 58px;
	}

	/deep/.u-subsection--button__bar[data-v-244377f2] {

		background-color: inherit !important;

	}

	.imgClass {
		/* width: 15%; */
	}

	/deep/.u-subsection--button {
		background-color: inherit !important;
		height: 50px;
		margin-left: -5px;

	}

	/deep/.uni-icons {

		width: 5%;
		text-align: right;
		padding-right: 20px;
	}

	/deep/.u-subsection__item__text {
		line-height: 30px !important;
		display: flex;
		flex-direction: row;
		align-items: center;
		transition-property: color;
		transition-duration: 0.3s;
		font-size: 15px !important;
		width: 123px;
		text-align: center;
		justify-content: center;
	}

	/deep/ .u-subsection--button__bar {
		background-color: inherit !important;
		border-bottom: 2px solid #3F47F4;
		/* width: 85px !important; */
		height: 45px !important;
		border-radius: 0px !important;
		color: #fff !important;
	}

	.DrawerPage {
		position: relative;
		width: 100vw;
		height: 100%;
		left: 0vw;
		/* background-color: #F7F8FA; */
		transition: all;
		overflow: hidden;
	}

	.DrawerPage.show {
		transform: scale(0.9, 1);
		left: 66vw;
		box-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);
		/* transform-origin: 0; */
	}

	.DrawerWindow {
		position: absolute;
		width: 62vw;
		height: 100vh;
		left: 0;
		top: 0;
		transform: scale(0.9, 0.9) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.6s;
		padding: 100upx 0;
	}


	/deep/ .u-tabbar__content__item-wrapper {
		height: 153rpx;
		/* 自定义高度 */
	}

	.DrawerWindow.show {
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	/deep/ .u-modal__content__text {

		word-break: break-all;
	}

	.DrawerClose {
		position: absolute;
		width: 40vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30upx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50upx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.6s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 35vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.cuIcon {
		width: 64upx;
		height: 64upx;
		line-height: 64upx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10upx;
		height: 10upx;
		background-color: currentColor;
		position: absolute;
		bottom: 10upx;
		border-radius: 10upx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}

	.DrawerWindow view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	.DrawerClose view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	view,
	image {
		box-sizing: border-box;
	}

	/*#ifdef MP*/
	view,
	image {
		box-sizing: content-box !important;
	}

	/*#endif*/
	.uni-scroll-view-content view,
	image {
		box-sizing: content-box !important;
	}

	.DrawerPage /deep/.uni-scroll-view {
		height: calc(100vh - 160rpx);
	}

	.blacks /deep/.uni-scroll-view {
		background: #1A1C24 !important;
	}

	.blacks .uni-scroll-view-content uni-view {
		background: #22252F !important;
	}

	page {
		overflow: initial;

	}

	.nav {
		background:#ff0000;
		background-size: auto 100%;
		background-position: 606rpx;
	}

	.dealer {
		position: absolute;
		top: 26rpx;
		right: 0;
		color: #FBE6CC;
		border-radius: 31rpx 0 0 31rpx;
		background-image: linear-gradient(to right, #574625, #6B552D);
		padding: 14rpx 44rpx;
		display: flex;
		align-items: center;
	}

	.deposit {
		color: #333333;
		padding: 0 14px;
		padding-top: 19px;
		position: relative;
		border-radius: 10px !important;
		margin: 15px !important;
		color: #fff;
	}

	.uni-page-body {
		color: #303133;
	}

	.deposit {
		background: linear-gradient(1deg, #1a3771 0%, #156290 100%);
	}

	.deposit .earnings-wrap {
		margin: 0 -14px;
		margin-top: 15px;

	}

	.mt-30 {
		margin-top: 15px !important;
	}

	.opacity-50 {
		opacity: 0.50;
	}


	.d-block {
		display: block;
	}

	.d-grid-columns-3 {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
	}

	.deposit .earnings-wrap .earnings {
		padding: 15px 14px 14px 14px;

	}
</style>
