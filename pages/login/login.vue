<template>
	<view>
		<scroll-view scroll-y class="DrawerPage" :class="modalName=='viewModal'?'show':''">
			<view>
				<view
					style="width:93%;display: flex; padding-top: 10px; padding-left: 10px; justify-content: center;align-items: center;">
					<view style="flex: 1;" @click="backs()">
						<image src="../../static/<EMAIL>" style="width: 50rpx; height: 50rpx;"></image>
					</view>
					<view style="width: 124px;
						height: 35px;
						border-radius: 388px 388px 388px 388px;
						opacity: 1; width: 40px; padding: 2px 0px; ; display: flex; justify-content: center; align-items: center;"
						@click="showModal" data-target="viewModal">
						<view>
							<image :src="clangs.img" style="width:25px; height: 20px;"></image>
						</view>
						<!-- <view style="height: 20px; margin: 0 5px; line-height: 20px; font-size: 14px;">
							<text>{{clangs.name}}</text>
						</view> -->
						<!-- <view>
							<image src="../../static/<EMAIL>" style="width:15px; height: 13px;"></image>
						</view> -->
					</view>
				</view>
			</view>
			<view style="display: flex;align-items: center;justify-content: center;">
				<image src="../../static/logo.png" style="width: 300px; height: 80px;"></image>
			</view>
			<uni-forms :modelValue="formData">
				<view style="height: 430px;
		
		opacity: 1; width: 90%; margin: 0 auto; ">
					<view class="wrap">
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 10px;">

							<view style="width: 100%; margin-top: 15px;">
								<text class="wrap_text" style="color: #0166fc;">{{i18n.welcome_sign_in}}</text>
								<u--input :placeholder="i18n.enter_email" v-model="usestring"
									style="border-radius: 55px; padding-left: 15px;  height: 32px; background-color: #f8f8f8; border:none ;"></u--input>
							</view>

						</view>
						<view
							style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-top: 5px;">

							<view style="width: 100%; margin-top: 15px;">
								<text class="wrap_text">{{i18n.pwd}}</text>

								<uni-easyinput class="uni-mt-5" :type="types" :passwordIcon="false"
									:suffixIcon="suffixIcon" v-model="passwords" :placeholder="i18n.enter_password"
									@iconClick="suffix"></uni-easyinput>

								<!-- <u-input class="inps"  :type="types"
			                            placeholder="请再次输入密码"
										@iconClick="suffix()"
			                            v-model="formData.password"
			                            :suffixIcon="suffixIcon"
			                            :password='passwordShow'
										
			                        ></u-input> -->
							</view>

						</view>
					</view>
					<view class="wrap">
						<!-- <view -->
						<!-- style="display: flex; flex-direction: column; align-items: center; width: 100%; margin-bottom: 15px; margin-top: 5px;"> -->




						<!-- </view> -->
						<!-- #ifndef APP-NVUE -->
						<!-- <text>验证码</text> -->
						<!-- <u-input v-model="values" class="inps" style=" height: 20px;" :placeholder="i18n.enter_vercode"> -->
						<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						<!-- <u--input v-model="values" class="inps" style="" :placeholder="i18n.enter_vercode"> -->
						<!-- #endif -->
						<!-- <template slot="suffix"> -->

						<!-- <img v-if="imgcode!==''" @click="changeCodeImg" :src="imgcode" alt=""> -->

						<!-- </template> -->
						<!-- #ifndef APP-NVUE -->
						<!-- </u-input> -->
						<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						</u--input>
						<!-- #endif -->



						<view
							style="display: flex; align-items: center; justify-content: space-between;margin-top: 15px; padding: 0 3px;">
							<view style="color: #9F9F9F; font-size: 12px;" @click="goregister()">{{i18n.have_account}}
								<text
									style="color: #537efd; font-weight: bold; font-size: 15px; padding-left: 10px;">{{i18n.register}}</text>
							</view>
							<view @click="goresetPass()"><text
									style="font-size: 15px;color: #9F9F9F;">{{i18n.forgot_password}}</text></view>
						</view>
						<view style="width:100%;margin-top: 30px;">
							<u-button :loading="loading" loadingText="" @click="obutton()" loadingSize="30" style="border-radius: 55px;  background: #0166fc; margin-top: 40px;
																    border: 0;   font-size: 14px;  height: 45px; color: #FFFFFF;
						line-height: 55px !important;">{{i18n.sign_in}}</u-button>
							<u-button :loading="tyloading" loadingText="" @click="obuttons()" loadingSize="30" style="border-radius: 55px;  background: #0166fc; border:1px solid #0166fc; margin-top: 10px;
																       font-size: 14px;  height: 45px; color: #FFFFFF;
						line-height: 15px !important;">{{i18n.creat_account}}</u-button>

						</view>

					</view>
				</view>


			</uni-forms>
		</scroll-view>
		<view class="DrawerClose" :class="modalName=='viewModal'?'show':''" @tap="hideModal">
			<text class="cuIcon-pullright"></text>
		</view>

	</view>
</template>

<script>
	import {
		langs
	} from "@/common/language.js"
	export default {
		data() {
			return {
				formData: {
					"password": ""
				},
				types: "password",
				loading: false,
				tyloading: false,
				modalName: null,
				values: null,
				value: null,
				passwordShow: true,
				usestring: "",
				passwords: "",
				imgcode: "",
				imgkey: "",
				clangs: {},
				language: [],
				mnUser: {}
			}
		},

		onLoad() {
			const _this = this
			_this.changeCodeImg();
			this.language = langs
			this.setLoads()
			this.setLgs()
			uni.setNavigationBarTitle({
				title: 'priectw'
			})
		},
		onShow() {
			// window.addEventListener("popstate",this.setHistory,false)
			this.setLgs()
		},
		methods: {
			setHistory() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			setLoads() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					el.selected = false
					if (el.value == lgs) el.selected = true
					return el
				})
			},
			setLgs() {
				let lgs = uni.getStorageSync('lang') || 'ara'
				this.language.map(el => {
					if (el.value == lgs) {
						this.clangs = el
					}
					return el
				})
			},
			setLang(item) {
				let langs = this.language.map(el => {
					el.selected = false
					if (el.value == item.value) el.selected = true
					return el
				})
				this.language = langs
				this._i18n.locale = item.value
				console.log(item.value)
				uni.setStorageSync('lang', item.value)
				this.$utils.setTabbar(this)
				this.clangs = item
				this.hideModal()
				// this.$store.commit('setLang', item.value)

				// setTimeout(() => {
				// 	this.showLanguage = false
				// }, 200)
			},

			showModal(e) {
				uni.navigateTo({
					url: '/pages/language/language'
				})
				// console.log('fff')
				// this.modalName = e.currentTarget.dataset.target
			},
			hideModal(e) {
				this.modalName = null

			},
			changeCodeImg() {

				this.$H.get('/api/v1/verification?' + Math.random(), false).then(result => {

					// if(result.data.code == 200){

					this.imgcode = result.img
					this.imgkey = result.key
					// }
				})
			},
			obutton() {
				localStorage.removeItem('VirtualAccount');
				let {
					usestring,
					passwords
				} = this
				// if(!usestring){
				// 	uni.$u.toast('用户名不能为空');
				// 	return false
				// }
				// if(!passwords){
				// 	uni.$u.toast('密码不能为空');
				// 	return false
				// }
				// if(this.$utils.charTest(usestring) || this.$utils.charTest(passwords) ){
				// 	uni.$u.toast('用户名或密码含特殊字符，请重新输入');
				// 	return false
				// }
				this.loading = true

				const res = this.$H.post('/api/v1/user/login', false, {
					user_string: usestring,
					password: passwords,
					captcha: this.values,
					key: this.imgkey
				}).then(res => {

					if (res.type == "ok") {

						console.log(res.data)
						this.$store.commit('login', res.data)
						getApp().setUser()
						setTimeout(() => {
							this.loading = false
							uni.reLaunch({
								url: "/pages/index/index"
							})
						}, 200)
					} else {
						this.loading = false
						this.changeCodeImg();
					}
				})



			},
			obuttons() {
				let {
					usestring,
					passwords
				} = this

				this.tyloading = true

				let resData = this.$H.get('/api/v1/generateAccount', false).then(reso => {
					this.mnUser = reso.data
					const ress = this.$H.post('/api/v1/user/login', false, {
						user_string: this.mnUser.username,
						password: this.mnUser.password
					}).then(res => {
						this.tyloading = false
						if (res.type == "ok") {
							console.log(res.data)
							this.$store.commit('login', res.data)
							getApp().setUser()
							localStorage.setItem('VirtualAccount', '虚拟账号');
							setTimeout(() => {
								this.loading = false
								uni.reLaunch({
									url: "/pages/index/index"
								})
							}, 200)
						} else {
							this.loading = false
							this.changeCodeImg();
						}

					})
				})












			},
			isShow() {
				this.types = this.types === "password" ? "text" : "password"
			},
			suffix() {
				this.types = this.types === "password" ? "text" : "password"
				this.passwordShow = !this.passwordShow
				if (this.passwordShow) {
					this.types = "password"
				}
			},
			goregister() {
				uni.navigateTo({
					url: "/pages/register/register"
				})
			},
			goresetPass() {
				uni.navigateTo({
					url: "/pages/resetPass/resetPass"
				})
			},
			backs() {
				uni.navigateTo({
					url: '/pages/index/index'
				})
			}

		},
		computed: {
			i18n() {
				return this.$t("login")
			},
			suffixIcon() {
				uni.setNavigationBarTitle({
					title: 'priectw'
				})
				if (this.passwordShow) {
					return 'iconfont icon-biyan';
				}
				return 'eye-filled';
			}
		}
	}
</script>

<style>
	page {
		/* background-color: #FFFFFF; */
	}

	.inps {
		border-radius: 55px;
		padding: 10px !important;
		background-color: #f8f8f8;
	}

	/deep/.u-input {
		background: #fff;
	}

	/deep/.u-icon__img {
		width: 20px !important;
		height: 20px !important;
	}

	/deep/.u-icon__icon[data-v-172979f2] {

		font-size: 22px !important;
	}

	/deep/.content-clear-icon {
		color: initial !important;
	}

	/deep/.uni-easyinput__content {
		border-radius: 55px;
		padding: 0px;
		padding-left: 28rpx;
		height: 45px;
	}

	/deep/.uni-easyinput__placeholder-class {

		font-size: 15px;
		color: rgb(192, 196, 204);
	}

	.DrawerPage {
		position: relative;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		background-color: #ffffff;
		transition: all 0.4s;

	}

	/deep/.uni-easyinput__content[data-v-abe12412] {
		background-color: #f8f8f8 !important;
		border-radius: 6vh;
		border: none;
		padding: 4px;
	}

	.wrap_text {
		display: block;
		margin-bottom: 2vh;
		font-size: 25rpx;

	}

	.DrawerPage.show {
		transform: scale(0.9, 0.9);
		left: 65vw;
		box-shadow: 0 0 60upx rgba(0, 0, 0, 0.2);
		transform-origin: 0;
	}

	.DrawerWindow {
		position: absolute;
		width: 65vw;
		height: 100vh;
		left: 0;
		top: 0;
		transform: scale(0.9, 0.9) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
		padding: 100upx 0;
	}

	.DrawerWindow.show {
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	.DrawerClose {
		position: absolute;
		width: 40vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30upx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50upx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 35vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.cuIcon {
		width: 64upx;
		height: 64upx;
		line-height: 64upx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10upx;
		height: 10upx;
		background-color: currentColor;
		position: absolute;
		bottom: 10upx;
		border-radius: 10upx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}

	.DrawerWindow view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	.DrawerClose view,
	scroll-view,
	swiper,
	button,
	input,
	textarea,
	label,
	navigator,
	image {
		box-sizing: border-box;
	}

	view,
	image {
		box-sizing: border-box;
	}

	/*#ifdef MP*/
	view,
	image {
		box-sizing: content-box !important;
	}

	/*#endif*/
	.uni-scroll-view-content view,
	image {
		box-sizing: content-box !important;
	}
</style>