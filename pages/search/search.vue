<template>
	<view>
		<view style="width: 98%;">
			<u-search :showAction="false" :clearabled="false" margin="25rpx" v-model="keyword" :searchIconSize="45"
				inputAlign="left" @search="searchs" :placeholder="i18n.enter_product_code"  height="70"></u-search>
		</view>
		<view class="u-page" style="">
			<view style="width: 98%; padding-left: 20px; color: #80838A;" v-if="$store.state.bgColor=='black'" >{{i18n.search_result}}</view>
			<view style="width: 98%; padding-left: 20px; " v-if="$store.state.bgColor=='while'" >{{i18n.search_result}}</view>

			<u-list v-if="searchList.length>0" class="sfds" style="  width: 100%;overflow: hidden; height: 100% ">
				<u-list-item v-for="(item, index) in searchList" :key="index">
					<!-- <u-cell :title="`列表长度-${index + 1}`" class="u-cell__title-texts" >
						<u-avatar slot="icon" shape="square" size="35"  :src="item.url"
							customStyle="margin: -3px 5px -3px 0"></u-avatar>

					</u-cell> -->
					<u-cell @tap="clickItems(item)">
					<!-- <u-avatar v-if="item.isCollect==1" @click="collects(item)" slot="icon" shape="square" size="55"
						src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>
					<u-avatar v-if="item.isCollect==0" @click="collects(item)" slot="icon" shape="square" size="55"
						src="../../static/<EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar> -->
						<u-avatar  @click.stop="collects(item)" slot="icon" shape="square" size="55"
							:src="'https://admin.evercrestcapitalls.com/storage/'+item.logo" customStyle="margin: -3px 1px -3px 0"></u-avatar>


					<view slot="title" style="display: flex;   color: #fff;"v-if="$store.state.bgColor=='black'">


						<text class="u-cell-text">{{item.currency_name}}</text>
						<text class="u-cell-text"
							style="flex: 1; justify-content: center; text-align: center;">{{Number(item.now_price)  | setPrecision(item.precision_length)}}</text>
						<text  class="u-cell-text" v-if="$utils.getColor(item.change)==12"
							style="color: #19AF6B;">{{item.change}}%</text>
						<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
							style="color: #EF263D;">{{item.change}}%</text>
							<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
								style="color: #EF263D;">{{item.change}}%</text>

					</view>
					<view slot="title" style="display: flex;  "v-if="$store.state.bgColor=='while'">


						<text class="u-cell-text">{{item.currency_name}}</text>
						<text class="u-cell-text"
							style="flex: 1; justify-content: center; text-align: center;">{{Number(item.now_price)  | setPrecision(item.precision_length)}}</text>
						<text  class="u-cell-text" v-if="$utils.getColor(item.change)==12"
							style="color: #19AF6B;">{{item.change}}%</text>
						<text v-if="$utils.getColor(item.change)==13" class="u-cell-text"
							style="color: #EF263D;">{{item.change}}%</text>
							<text v-if="$utils.getColor(item.change)==14" class="u-cell-text"
								style="color: #EF263D;">{{item.change}}%</text>

					</view>
<!--					<u-avatar v-if="$utils.getColor(item.change)==12" slot="right-icon" shape="square" size="35"-->
<!--						src="../../static/Up_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>-->
<!--					<u-avatar v-if="$utils.getColor(item.change)==13" slot="right-icon" shape="square" size="35"-->
<!--						src="../../static/Down_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>-->
<!--						<u-avatar v-if="$utils.getColor(item.change)==14" slot="right-icon" shape="square" size="35"-->
<!--							src="../../static/Down_S <EMAIL>" customStyle="margin: -3px 1px -3px 0"></u-avatar>-->
					</u-cell>
				</u-list-item>
			</u-list>

			<u-empty v-if="searchList.length===0"
			        mode="search"
			        icon="file-text"
					iconSize="207"
					textSize="40"
					marginTop="120"
					:text="i18n.search_result"
			>
			</u-empty>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default {
		data() {
			return {
				indexList: [],
				searchList: [],
				keyword: "",
				quotationOriginal: [],
				quotation:null,
				  resData:null,
				   mLists:null
			}
		},
		computed: {
			i18n() {
				return this.$t("search")
			},
			...mapState({
				socket:state=>state.socket,
				token:state=>state.token
			})
		},
		onLoad() {
console.log('****************')
			this.loadmore();
			const _this = this
			uni.setNavigationBarTitle({
				title:_this.$t("search.search")

			})

			if(this.$store.state.bgColor=='black')
			{
				uni.setNavigationBarColor({
				  frontColor: '#ffffff', // 导航栏标题颜色，只能是'black'或'white'
				  backgroundColor: '#222222' // 导航栏背景颜色
				});
			}



		},
		watch: {

			keyword: function(val) {
				console.log('搜索关键词变化:', val);
				console.log('quotationOriginal 数据:', this.quotationOriginal.length);
				if(val==='')return
				this.searchList=this.quotationOriginal.filter((value) => { //过滤数组元素
					return value.currency_name.toLowerCase().includes(val.toLowerCase()); //如果包含字符返回true
				});
				console.log('搜索结果:', this.searchList.length);
			}
		},
		methods: {
			testClick() {
				console.log('=== 测试按钮点击成功 ===');
				uni.showToast({ title: '按钮触发', icon: 'none' });
				uni.navigateTo({
					url: '/pages/stock/stock',
					success() { console.log('navigateTo 成功'); },
					fail(err) { console.log('navigateTo 失败', err); uni.showToast({ title: '跳转失败', icon: 'none' }); }
				});
			},
			clickItems(item){
				this.$store.commit('setCurrency',{
					"currency_id":item.currency_id,
					"currency_name":item.currency_name,
					"legal_name":item.legal_name,
					"currentIndex":item.currentIndex
				})
				// 同步一份到本地缓存，stock 页会优先读取这里的别名等信息
				try{
					const alias = (item.currency && item.currency.alias) || item.alias || item.currency_name
					uni.setStorageSync('currency', {
						currency_id: item.currency_id,
						currency_name: item.currency_name,
						legal_name: item.legal_name,
						alias
					})
				}catch(e){}
				this.$utils.jump('/pages/stock/stock','navigateTo')
			},
			searchs() {
				// 搜索方法 - 当用户点击搜索或输入时触发
				console.log('搜索关键词:', this.keyword);
				// 搜索逻辑已经在 computed 的 searchList 中处理
			},
			collects(item){
			  // let iscoll=item.isCollect===1? 0 :1;
			  this.auths(()=>{
				  	  if(item.isCollect){
						   item.isCollect=0
						  const res =  this.$H.post('/api/v1/optional/del',false,{currency_id:item.currency_id}).then(res => {

						  	  if(res.type=="ok")
						  	  {
						  		  item.isCollect=0
						  	  }

						  	})
					  }else{
						  item.isCollect=1
						  const res =  this.$H.post('/api/v1/optional/add',false,{currency_id:item.currency_id}).then(res => {

						  	  if(res.type=="ok")
						  	  {
						  		  item.isCollect=1
						  	  }

						  	})
					  }


			  })
			},
			loadmore() {
				// for (let i = 0; i < 60; i++) {
				// 	this.indexList.push({
				// 		url: this.urls[uni.$u.random(0, this.urls.length - 1)]
				// 	})
				// }
 this.resData=[]
				const res =  this.$H.get('/api/v1/quotation_new',false).then(res => {

					const restwo =  this.$H.get('/api/v1/optional/list',false).then(restwo => {
						   this.mLists=restwo.data;
							for (var i = 0; i < res.data.length; i++) {
								console.log('nnnnnnnnnn')
								  res.data[i].quotation.map(item=>{
									item.precision_length = this.$utils.getPrecisionLength(item.now_price)
									item.currentIndex = i
									const has=this.mLists.findIndex(items => items.currency_id == item.currency_id)
									if(has > -1){
										item.isCollect = 1
									}else{
										item.isCollect = 0
									}

									 this.quotationOriginal.push(item)
									  this.resData.push(res.data[i].quotation)
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^')
									  console.log(this.resData)
									  console.log(this.quotationOriginal)
									  this.startSocket();
								 })
							}
						})


					// console.log('uuu')
					// 	console.log(res.data[0].quotation[0].id)






					 // this.myLists()

					})
				console.log(this.resData)
				this.quotation =this.resData
				this.indexList = [{
						date: 'SH600051',
						gprice: '1.778501',
						dprice: '+0.25',
						isBol: 1,
						isCollect: 0
					},
					{
						date: 'SH600051',
						gprice: '1.778502',
						dprice: '+0.15',
						isBol: 1,
						isCollect: 1
					}, {
						date: 'SH600053',
						gprice: '1.778503',
						dprice: '+0.35',
						isBol: 0,
						isCollect: 1
					},
					{
						date: 'SH600054',
						gprice: '1.778504',
						dprice: '+0.45',
						isBol: 1,
						isCollect: 0
					},
					{
						date: 'SH600055',
						gprice: '1.778502',
						dprice: '+0.55',
						isBol: 0,
						isCollect: 0
					}, {
						date: 'SH600056',
						gprice: '1.778503',
						dprice: '+0.65',
						isBol: 0,
						isCollect: 0
					},
					{
						date: 'SH600057',
						gprice: '1.778504',
						dprice: '+0.75',
						isBol: 0,
						isCollect: 1
					},
					{
						date: 'SH600055',
						gprice: '1.778502',
						dprice: '+0.55',
						isBol: 0,
						isCollect: 0
					}, {
						date: 'SH600056',
						gprice: '1.778503',
						dprice: '+0.65',
						isBol: 0,
						isCollect: 1
					},
					{
						date: 'SH600057',
						gprice: '1.778504',
						dprice: '+0.75',
						isBol: 1,
						isCollect: 1
					}
				];

			},
			startSocket(){
								const _this = this
								// console.log(this.token)
								this.socket.on('daymarket', res => {
								     console.log('ooooooooooooooooooooooooooooooooooooooooo')

									 if(this.searchList.length>0){
										 const has = _this.searchList.findIndex(item => item.currency_id == res.currency_id)
										   console.log(res)
										 if(has > -1){
										 	 console.log(res)
										 	const item = {
										 		..._this.searchList[has],
										 		...res
										 	}
										 	_this.searchList.splice(has, 1, item)
										 }
										 return
									 }
									const has = _this.quotationOriginal.findIndex(item => item.currency_id == res.currency_id)
									  console.log(res)
									if(has > -1){
										 console.log(res)
										const item = {
											..._this.quotationOriginal[has],
											...res
										}
										_this.quotationOriginal.splice(has, 1, item)
									}

									// const has2 = _this.optionalList.findIndex(item => item.currency_id == res.currency_id)
									// if(has2 > -1){
									// 	const item2 = {
									// 		..._this.optionalList[has2],
									// 		...res
									// 	}
									// 	_this.optionalList.splice(has2, 1, item2)
									// }

								})
							}
		}
	}
</script>

<style>
	/deep/.u-cell__title {
		flex: 1;
		line-height: 58px;
	}
</style>
